<template>
  <div class="computation-history">
    <div class="history-header">
      <h3 class="history-title">计算历史</h3>
      <el-button :icon="Refresh" circle @click="fetchModelItems" :loading="loading" />
    </div>
    <el-scrollbar class="history-scrollbar">
      <div v-for="item in historyItems" :key="item.id" class="history-item">
        <div class="item-content-wrapper">
          <div class="item-top-row">
            <FileIcon class="file-icon" />
            <span class="file-name">{{ getDisplayName(item) }}</span>
          </div>
          <div class="item-bottom-row">
            <el-tag 
              size="small" 
              :type="getDisplayTagType(item)" 
              class="model-tag"
            >
              {{ getDisplayTagText(item) }}
            </el-tag>
            <span class="timestamp">{{ formatDate(item.createdAt) }}</span>
          </div>
        </div>
        <el-button 
          class="detail-button" 
          @click="viewDetails(item)" 
          :loading="viewLoadingUids.includes(item.uid)"
          :disabled="item.status !== 'completed'"
        >
          查看详情
        </el-button>
      </div>
    </el-scrollbar>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import FileIcon from "@/assets/svg/file.svg?component";
import { getModelList, getModelInfo } from "@/api/models/model";
import { ElMessage } from "element-plus";
import { useRouter } from "vue-router";
import { Refresh } from "@element-plus/icons-vue";
import nameMap from "@/config/nameMap.json";
import { useWorkspaceStoreHook } from "@/store/modules/workspace";

interface HistoryItem {
  id: number;
  uid: string;
  name: string;
  category: string;
  originParams: {
    dataset?: {
      target: string;
      [key: string]: any;
    };
    model?: {
      algorithm?: {
        name: string;
        [key: string]: any;
      };
      [key: string]: any;
    };
    [key: string]: any;
  } | null;
  createdAt: string;
  updatedAt: string;
  modelType: "reg" | "cls";
  status: "completed" | "running" | "pending" | "failed" | "upload";
  targetName: string[];
  targetValues: any[][];
  source: string;
  progress: string;
  asynchronous: boolean;
  isRabbitmqReady: boolean;
  error: any;
  featureRanges: Record<string, { min: number; max: number }>;
}

defineOptions({
  name: "ComputationHistory"
});

const router = useRouter();

const historyItems = ref<HistoryItem[]>([]);
const loading = ref(false);
const viewLoadingUids = ref<string[]>([]);

const workspaceStore = useWorkspaceStoreHook();

const fetchModelItems = async () => {
  loading.value = true;
  const workspacePath = workspaceStore.getCurrentWorkspacePath || "";
  const workspaceName = workspacePath
    ? workspacePath
        .replace(/[\\/]+$/, "")
        .split(/[\\/]/)
        .pop() || ""
    : "";
  try {
    const params = { path: workspaceName };
    const res = await getModelList(params);
    if (res.code === 200) {
      // 过滤掉无效数据
      historyItems.value = (res.data || []).filter(item => item && item.uid);
    } else {
      ElMessage.error(res.msg || "获取计算历史失败");
    }
  } catch (error) {
    console.error("获取计算历史失败:", error);
    ElMessage.error("获取计算历史失败");
  } finally {
    loading.value = false;
  }
};

// 获取显示名称
const getDisplayName = (item: HistoryItem): string => {
  // 如果有 originParams 且有模型算法名称，显示模型名称
  if (item.originParams?.model?.algorithm?.name) {
    return item.name || '未命名模型';
  }
  
  // 如果没有 originParams 且名称为 upload，显示"本地上传"
  if (!item.originParams && item.name === 'upload') {
    return '本地上传';
  }
  
  // 其他情况显示原始名称
  return item.name || '未命名模型';
};

// 获取显示标签文本
const getDisplayTagText = (item: HistoryItem): string => {
  // 如果有 originParams 且有模型算法名称，显示算法名称
  if (item.originParams?.model?.algorithm?.name) {
    const algorithmName = item.originParams.model.algorithm.name;
    return nameMap[algorithmName] || algorithmName;
  }
  
  // 如果没有 originParams 且名称为 upload，显示"本地上传"
  if (!item.originParams && item.name === 'upload') {
    return '本地上传';
  }
  
  // 其他情况根据模型类型显示
  return getModelTypeDisplayName(item.modelType);
};

// 获取显示标签类型
const getDisplayTagType = (item: HistoryItem): string => {
  // 如果有 originParams 且有模型算法名称，根据算法类型返回标签类型
  if (item.originParams?.model?.algorithm?.name) {
    return getCategoryTagType(item.category);
  }
  
  // 如果没有 originParams 且名称为 upload，使用特殊颜色
  if (!item.originParams && item.name === 'upload') {
    return 'warning';
  }
  
  // 其他情况根据模型类型返回标签类型
  return getModelTypeTagType(item.modelType);
};

// 获取模型类型的显示名称
const getModelTypeDisplayName = (modelType: string) => {
  switch (modelType) {
    case "reg":
      return "回归模型";
    case "cls":
      return "分类模型";
    default:
      return modelType || "未知类型";
  }
};

// 获取模型类型标签类型
const getModelTypeTagType = (modelType: string) => {
  switch (modelType) {
    case "reg":
      return "primary";
    case "cls":
      return "success";
    default:
      return "info";
  }
};

// 获取模型类别标签类型（用于算法）
const getCategoryTagType = (category: string) => {
  switch (category) {
    case "linear":
      return "primary";
    case "tree":
      return "success";
    case "other":
      return "info";
    default:
      return "info";
  }
};

const viewDetails = async (item: HistoryItem) => {
  if (!item || !item.uid) {
    ElMessage.warning("无效的模型数据");
    return;
  }

  if (item.status !== "completed") {
    ElMessage.warning("模型尚未完成，无法查看结果");
    return;
  }

  viewLoadingUids.value.push(item.uid);
  try {
    if (window.ipcRenderer) {
      const res = await getModelInfo(item.uid);
      if (res.code === 200) {
        const fullModelInfo = res.data;
        let correctModelType = fullModelInfo.alg || item.modelType;
        
        // 如果是本地上传的模型，将 modelType 设置为 Local
        if (!item.originParams && item.name === 'upload') {
          correctModelType = 'local';
        }

        await window.ipcRenderer.send("cache_model_result", {
          uid: item.uid,
          result: fullModelInfo,
        });

        // 根据是否有 originParams 决定路由
        let routeName = "MLModelResult"; // 默认使用机器学习模型结果页面
        
        // 如果有 originParams 且是线性模型，使用线性模型结果页面
        if (item.originParams && item.category === "linear") {
          routeName = "LMModelResult";
        }

        // 获取正确的显示名称
        const displayName = getDisplayName(item);
        
        const { href } = router.resolve({
          name: routeName,
          query: {
            uid: item.uid,
            modelType: correctModelType,
            modelName: displayName, // 这里会传递 "本地上传"
            targetName: Array.isArray(item.targetName) ? item.targetName.join(',') : (item.targetName || "未知目标"),
          },
        });
        window.open(href);
      } else {
        ElMessage.error(res.msg || "获取模型详情失败");
      }
    } else {
      ElMessage.error("非Electron环境，无法打开独立窗口");
    }
  } catch (error) {
    console.error("获取模型详情失败:", error);
    ElMessage.error("获取模型详情失败");
  } finally {
    viewLoadingUids.value = viewLoadingUids.value.filter(uid => uid !== item.uid);
  }
};

// 格式化日期
const formatDate = (dateString: string) => {
  if (!dateString) return "-";
  try {
    const date = new Date(dateString);
    return date.toLocaleString("zh-CN", {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
    });
  } catch {
    return dateString;
  }
};

onMounted(() => {
  fetchModelItems();
});
</script>

<style scoped>
.computation-history {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

.history-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  margin-bottom: 0;
}

html.dark .history-title {
  color: #eee;
}

.history-scrollbar {
  flex: 1;
  overflow-y: auto;
}

.history-item {
  background: rgba(255, 255, 255, 0.85);
  border: 1px solid var(--el-border-color-light);
  border-radius: 2px;
  box-shadow: 0px 2px 8px 0px rgb(0 0 0 / 8%);
  padding: 10px 15px;
  margin: 10px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 70px;
}

html.dark .history-item {
  background: var(--el-bg-color-overlay);
}

.item-content-wrapper {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  margin-right: 10px;
  min-width: 0;
}

.item-top-row {
  display: flex;
  align-items: center;
  margin-bottom: 5px;
}

.file-icon {
  width: 18px;
  height: 18px;
  color: var(--el-color-primary);
  margin-right: 8px;
  flex-shrink: 0;
}

.file-name {
  font-size: 15px;
  font-weight: 500;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
}

html.dark .file-name {
  color: #fff;
}

.item-bottom-row {
  display: flex;
  align-items: center;
  font-size: 12px;
  color: #999;
  gap: 8px;
}

.model-tag {
  color: #fff;
  flex-shrink: 0;
}

/* 标签样式 */
.el-tag--primary {
  background-color: #005DFF !important;
  border-color: #005DFF !important;
}

.el-tag--success {
  background-color: #9774FE !important;
  border-color: #9774FE !important;
}

.el-tag--info {
  background-color: #5492FF !important;
  border-color: #5492FF !important;
}

.el-tag--warning {
  background-color: #E6A23C !important;
  border-color: #E6A23C !important;
}

.timestamp {
  font-size: 12px;
  color: #999;
  flex-shrink: 0;
  margin-left: auto;
}

.detail-button {
  flex-shrink: 0;
  padding: 8px 12px;
  border: 1px solid var(--el-color-primary);
  color: var(--el-color-primary);
  background-color: transparent;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.detail-button:hover:not(:disabled) {
  background-color: var(--el-color-primary-light-9);
}

.detail-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

html.dark .detail-button {
  border-color: var(--el-color-primary-light-3);
  color: var(--el-color-primary-light-3);
}

html.dark .detail-button:hover:not(:disabled) {
  background-color: rgba(var(--el-color-primary-rgb), 0.1);
}
</style>