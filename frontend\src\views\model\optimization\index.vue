<template>
  <div class="main">
    <div class="card-header">
      <span>材料配方优化</span>
    </div>

    <el-scrollbar class="main-scrollbar">
      <!-- 主Tab切换 -->
      <div class="main-tab-controls">
        <button type="button" :class="['main-tab-button', { 'is-active': activeMainTab === 'single' }]"
          @click="handleTabChange('single')" :disabled="isOptimizationRunning">
          <img :src="singleTargetIcon" alt="单目标优化" class="tab-icon" />
          单目标优化
        </button>
        <button type="button" :class="['main-tab-button', { 'is-active': activeMainTab === 'multi' }]"
          @click="handleTabChange('multi')" :disabled="isOptimizationRunning">
          <img :src="multiTargetIcon" alt="多目标优化" class="tab-icon" />
          多目标优化
        </button>
      </div>

      <!-- 单目标优化 -->
      <div v-if="activeMainTab === 'single'">
        <SingleTargetOptimization 
          ref="singleTargetRef" 
          :model-list="modelOptions"
          :loading="loading"
          @refresh-models="fetchModelList"
          @update:optimization-status="handleOptimizationStatus"
        />
      </div>

      <!-- 多目标优化 -->
      <div v-if="activeMainTab === 'multi'">
        <MultiTargetOptimization 
          ref="multiTargetRef" 
          :model-list="modelOptions"
          :loading="loading"
          @refresh-models="fetchModelList"
          @update:optimization-status="handleOptimizationStatus"
        />
      </div>
    </el-scrollbar>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import { ElMessage } from "element-plus";
import { getModelList } from "@/api/models/model";
import SingleTargetOptimization from "./SingleTargetOptimization.vue";
import MultiTargetOptimization from "./MultiTargetOptimization.vue";
import { useWorkspaceStoreHook } from "@/store/modules/workspace";

defineOptions({
  name: "componentOptimization"
});

const activeMainTab = ref<'single' | 'multi'>('single');

const singleTargetRef = ref<InstanceType<typeof SingleTargetOptimization> | null>(null);
const multiTargetRef = ref<InstanceType<typeof MultiTargetOptimization> | null>(null);

// 共享的模型列表状态
const modelOptions = ref<any[]>([]);
const loading = ref(false);
const isOptimizationRunning = ref(false);

const workspaceStore = useWorkspaceStoreHook();

// 计算图标路径
const singleTargetIcon = computed(() =>
  activeMainTab.value === 'single' 
    ? '/src/assets/svg/single_target_selected.svg' 
    : '/src/assets/svg/single_target.svg'
);

const multiTargetIcon = computed(() => 
  activeMainTab.value === 'multi' 
    ? '/src/assets/svg/multi_target_selected.svg' 
    : '/src/assets/svg/multi_target.svg'
);

const handleOptimizationStatus = (isRunning: boolean) => {
  isOptimizationRunning.value = isRunning;
};

// 获取模型列表的函数
const fetchModelList = async () => {
  loading.value = true;
  try {
    const workspacePath = workspaceStore.getCurrentWorkspacePath || "";
    const workspaceName = workspacePath
      ? workspacePath
          .replace(/[\\/]+$/, "")
          .split(/[\\/]/)
          .pop() || ""
      : "";
    const params = { path: workspaceName };
    const response = await getModelList(params);
    if (response.code === 200 && Array.isArray(response.data)) {
      const completedModels = response.data.filter(model => model.status === "completed");
      modelOptions.value = completedModels.map(model => {
        const createdDate = new Date(model.createdAt);
        const formattedDate = createdDate.toLocaleString('zh-CN', {
          year: 'numeric', month: '2-digit', day: '2-digit',
          hour: '2-digit', minute: '2-digit'
        });
        
        let displayTargetName = model.targetName;
        if (Array.isArray(model.targetName)) {
          displayTargetName = model.targetName.join(', ');
        } else if (typeof model.targetName === 'string') {
          try {
            const parsed = JSON.parse(model.targetName);
            if (Array.isArray(parsed)) {
              displayTargetName = parsed.join(', ');
            }
          } catch {
            // 保持原样
          }
        }
        
        return {
          label: `${model.name} [${displayTargetName}] (${formattedDate})`,
          value: model.uid,
          targetName: model.targetName,
          id: model.id,
          targetValues: model.targetValues,
          createdAt: formattedDate,
          featureRanges: model.featureRanges || {}
        };
      });
    } else {
      console.warn("Invalid response format or no models found");
      ElMessage.warning("未找到可用模型或响应格式错误");
      modelOptions.value = [];
    }
  } catch (error: any) {
    if (error.name === 'AbortError' || error.code === 'ERR_CANCELED') {
      console.log('Model list request was cancelled');
      return;
    }
    console.error("Error fetching model list:", error);
    ElMessage.error("获取模型列表失败");
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  fetchModelList();
});

const handleTabChange = (tab: 'single' | 'multi') => {
  if (activeMainTab.value === tab) return;
  activeMainTab.value = tab;
};
</script>

<style scoped>
/* 样式保持不变 */
.main {
  margin: 20px;
  height: calc(100% - 40px);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background-color: transparent;
}

.card-header {
  padding-bottom: 20px;
  font-size: 18px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.main-scrollbar {
  flex: 1;
  height: 100%;
  overflow: hidden;
  width: 100%;
}

.main-tab-controls {
  display: flex;
  gap: 12px;
  margin-bottom: 24px;
}

.tab-icon {
  width: 16px;
  height: 16px;
  margin-right: 8px;
  transition: all 0.3s ease;
}

.main-tab-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 184px;
  height: 36px;
  border-radius: 24px;
  border: none;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
  background: rgba(206, 206, 206, 0.1);
  color: #666;
}

.main-tab-button.is-active {
  background: rgba(0, 93, 255, 0.1);
  border: 1px solid #005DFF;
  color: #005DFF;
  font-weight: 500;
}

.main-tab-button:disabled {
  cursor: not-allowed;
  opacity: 0.5;
  background: rgba(206, 206, 206, 0.1);
  color: #999;
}

.main-tab-button:disabled:hover {
  background: rgba(206, 206, 206, 0.1);
  color: #999;
}
</style>