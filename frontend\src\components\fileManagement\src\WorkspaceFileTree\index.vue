<template>
  <div class="workspace-file-tree">
    <!-- Workspace header with title only -->
    <div class="workspace-header">
      <div class="workspace-title">
        工作区
        <el-tag v-if="workspaceName">{{ workspaceName }}</el-tag>
      </div>
    </div>

    <!-- File tree -->
    <div class="file-tree-container">
      <div v-if="!currentWorkspacePath && workspaceStore.getOpenedFiles.length === 0" class="empty-workspace">
        <p>未选择工作区</p>
        <el-button type="primary" size="small" @click="selectWorkspaceDirectory">
          选择目录
        </el-button>
      </div>

      <el-tree
        v-else-if="treeData.length > 0"
        ref="treeRef"
        :data="treeData"
        :props="defaultProps"
        :load="loadNode"
        lazy
        highlight-current
        node-key="path"
        :expand-on-click-node="false"
        :current-node-key="currentSelectedFile"
        :default-expanded-keys="expandedKeys"
        @node-click="handleNodeClick"
        @node-expand="handleNodeExpand"
        @node-collapse="handleNodeCollapse"
        class="workspace-tree"
      >
        <template #default="{ node, data }">
          <!-- 文件节点 -->
          <div v-if="!data.isDirectory" class="tree-node-file-wrapper">
            <div
              class="tree-node"
              @contextmenu.prevent="onNodeContextMenu($event, data)"
            >
              <el-icon :size="16" class="node-icon">
                <component :is="isNodeSelected(data.path) ? FileSelectedIcon : FileIcon" />
              </el-icon>
              <!-- 标记圆点 - 移到图标后面，文字前面 -->
              <div v-if="getNodeTags(data.path).length > 0" class="tags-container">
                <el-tooltip
                  v-for="tag in getNodeTags(data.path)"
                  :key="tag.id"
                  :content="`标记: ${tag.name}`"
                  placement="top"
                  effect="dark"
                  :offset="8"
                >
                  <div
                    class="tag-dot"
                    :style="{ backgroundColor: tag.color }"
                  ></div>
                </el-tooltip>
              </div>
              <el-tooltip
                :content="node.label"
                placement="right"
                :show-after="300"
                :hide-after="50"
                effect="dark"
                :offset="8"
              >
                <span class="node-label file-label-ellipsis">{{ node.label }}</span>
              </el-tooltip>
            </div>
          </div>
          <!-- 文件夹节点 -->
          <div
            v-else
            class="tree-node"
            @contextmenu.prevent="onNodeContextMenu($event, data)"
          >
            <el-icon :size="16" class="node-icon">
              <component :is="isDirSelected(data.path) ? FolderSelectedIcon : FolderIcon" />
            </el-icon>
            <div v-if="getNodeTags(data.path).length > 0" class="tags-container">
              <el-tooltip
                v-for="tag in getNodeTags(data.path)"
                :key="tag.id"
                :content="`标记: ${tag.name}`"
                placement="top"
                effect="dark"
                :offset="8"
              >
                <div
                  class="tag-dot"
                  :style="{ backgroundColor: tag.color }"
                ></div>
              </el-tooltip>
            </div>
            <el-tooltip
              :content="node.label"
              placement="right"
              :show-after="300"
              :hide-after="50"
              effect="dark"
              :offset="8"
            >
              <span class="node-label file-label-ellipsis">{{ node.label }}</span>
            </el-tooltip>
          </div>
        </template>
      </el-tree>

      <div v-else-if="loading" class="loading-container">
        <el-icon class="is-loading" :size="20">
          <Loading />
        </el-icon>
        <span>加载中...</span>
      </div>

      <div v-else class="empty-directory">
        <p>目录为空</p>
      </div>

      <!-- 遮罩层 -->
      <div v-if="contextMenu.visible" class="context-menu-overlay" @click="closeContextMenu" @contextmenu.prevent="closeContextMenu"></div>

      <!-- 右键菜单 -->
      <div
        v-if="contextMenu.visible"
        :style="{ left: contextMenu.x + 'px', top: contextMenu.y + 'px' }"
        class="custom-context-menu"
        @click.stop
      >
        <div class="menu-item" @click="handleCreateFile">新建文件</div>
        <div class="menu-item" @click="handleCreateFolder" v-if="contextMenu.target?.isDirectory">新建文件夹</div>
        <div class="menu-item" @click="handleRename">重命名</div>
        <!-- 标记相关菜单 -->
        <div class="menu-separator"></div>
        <div class="menu-item" @click="handleManageTags">管理标记</div>
        <div class="menu-item" @click="handleRemoveAllTags" v-if="getNodeTags(contextMenu.target?.path || '').length > 0">清除所有标记</div>
        <!-- 已有标记的快捷操作 -->
        <template v-if="getNodeTags(contextMenu.target?.path || '').length > 0">
          <div class="menu-separator"></div>
          <div 
            v-for="tag in getNodeTags(contextMenu.target?.path || '')" 
            :key="tag.id"
            class="menu-item tag-menu-item"
            @click="removeTag(contextMenu.target?.path || '', tag.id)"
          >
            <span class="tag-dot-small" :style="{ backgroundColor: tag.color }"></span>
            移除标记: {{ tag.name }}
          </div>
        </template>
        <div class="menu-separator"></div>
        <div class="menu-item delete" @click="handleDelete">删除</div>
      </div>

      <!-- 新建文件/文件夹对话框 -->
      <el-dialog v-model="dialog.visible" :title="dialog.type === 'file' ? '新建文件' : '新建文件夹'" width="400px" @close="dialog.input = ''">
        <el-input v-model="dialog.input" :placeholder="dialog.type === 'file' ? '请输入文件名' : '请输入文件夹名'" @keyup.enter="handleDialogConfirm" />
        <template #footer>
          <el-button @click="dialog.visible = false">取消</el-button>
          <el-button type="primary" @click="handleDialogConfirm">确定</el-button>
        </template>
      </el-dialog>

      <!-- 重命名对话框 -->
      <el-dialog v-model="renameDialog.visible" title="重命名" width="400px" @close="renameDialog.input = ''">
        <el-input v-model="renameDialog.input" :placeholder="`请输入新的${renameDialog.target?.isDirectory ? '文件夹' : '文件'}名`" @keyup.enter="handleRenameConfirm" />
        <template #footer>
          <el-button @click="renameDialog.visible = false">取消</el-button>
          <el-button type="primary" @click="handleRenameConfirm">确定</el-button>
        </template>
      </el-dialog>

      <!-- 标记管理对话框 -->
      <el-dialog v-model="tagDialog.visible" title="标记管理" width="700px" @close="closeTagDialog">
        <div class="tag-management-container">
          <!-- 现有标记列表 -->
          <div class="section-header">
            <h4>现有标记</h4>
            <span class="section-desc">点击选择要应用的标记</span>
          </div>
          <div class="tags-grid">
            <div 
              v-for="tag in allTags" 
              :key="tag.id"
              class="tag-item"
              :class="{ 'tag-selected': isTagSelected(tag.id) }"
              @click="toggleTagSelection(tag.id)"
            >
              <span class="tag-dot-medium" :style="{ backgroundColor: tag.color }"></span>
              <span class="tag-name">{{ tag.name }}</span>
              <span class="tag-count">{{ getTagUsageCount(tag.id) }}</span>
              <el-button 
                type="danger" 
                size="small" 
                @click.stop="deleteTag(tag.id)"
                :disabled="getTagUsageCount(tag.id) > 0"
                class="delete-btn"
              >
                删除
              </el-button>
            </div>
          </div>

          <!-- 新建标记 -->
          <div class="section-header">
            <h4>新建标记</h4>
            <span class="section-desc">创建新的标记</span>
          </div>
          <div class="new-tag-form">
            <el-input 
              v-model="tagDialog.newTagName" 
              placeholder="标记名称" 
              class="tag-name-input"
            />
            <el-color-picker 
              v-model="tagDialog.newTagColor" 
              :predefine="predefinedColors"
              class="color-picker"
            />
            <el-button 
              type="primary" 
              @click="createNewTag"
              :disabled="!tagDialog.newTagName.trim()"
              class="create-btn"
            >
              创建标记
            </el-button>
          </div>

          <!-- 应用到当前节点 -->
          <div class="section-header">
            <h4>应用到当前节点</h4>
            <span class="section-desc">已选择的标记将应用到当前文件/文件夹</span>
          </div>
          <div class="selected-tags">
            <el-tag
              v-for="tagId in tagDialog.selectedTags"
              :key="tagId"
              closable
              @close="removeTagFromSelection(tagId)"
              class="selected-tag"
              :style="{ 
                backgroundColor: getTagById(tagId)?.color + '20',
                borderColor: getTagById(tagId)?.color,
                color: getTagById(tagId)?.color
              }"
            >
              <span class="tag-dot-small" :style="{ backgroundColor: getTagById(tagId)?.color }"></span>
              {{ getTagById(tagId)?.name }}
            </el-tag>
            <div v-if="tagDialog.selectedTags.length === 0" class="no-tags">
              未选择任何标记
            </div>
          </div>
          <div class="apply-actions">
            <el-button @click="closeTagDialog">取消</el-button>
            <el-button type="primary" @click="applyTagsToNode">应用标记</el-button>
          </div>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, computed, watch, nextTick, reactive } from "vue";
import { ElMessage, ElMessageBox, ElLoading } from "element-plus";
import {
  Loading,
} from "@element-plus/icons-vue";
import type Node from "element-plus/es/components/tree/src/model/node";
import { useWorkspaceStoreHook } from "@/store/modules/workspace";
import { useRouter } from "vue-router";
// 导入文件图标
import FileIcon from "@/assets/svg/file.svg?component";
import FileSelectedIcon from "@/assets/svg/file_selected.svg?component";
import FolderIcon from "@/assets/svg/folder.svg?component";
import FolderSelectedIcon from "@/assets/svg/folder_selected.svg?component";
import { debounce } from "@pureadmin/utils"; // 添加debounce导入

// 标记相关接口定义
interface Tag {
  id: string;
  name: string;
  color: string;
  createdAt: number;
}

interface NodeTag {
  nodePath: string;
  tagId: string;
}

// 标记相关状态
const allTags = ref<Tag[]>([]);
const nodeTags = ref<NodeTag[]>([]);
const tagDialog = ref({
  visible: false,
  newTagName: '',
  newTagColor: '#409EFF',
  selectedTags: [] as string[],
  targetPath: '' // 新增：保存目标节点路径
});

// 预定义颜色
const predefinedColors = [
  '#409EFF', '#67C23A', '#E6A23C', '#F56C6C', '#909399',
  '#9C27B0', '#FF9800', '#795548', '#607D8B', '#FF5722'
];

// 初始化标记数据
const initializeTags = () => {
  // 从本地存储加载标记数据
  const savedTags = localStorage.getItem('fileTree_tags');
  const savedNodeTags = localStorage.getItem('fileTree_nodeTags');
  
  if (savedTags) {
    allTags.value = JSON.parse(savedTags);
  }
  
  if (savedNodeTags) {
    nodeTags.value = JSON.parse(savedNodeTags);
  }
};

// 保存标记数据到本地存储
const saveTagsData = () => {
  localStorage.setItem('fileTree_tags', JSON.stringify(allTags.value));
  localStorage.setItem('fileTree_nodeTags', JSON.stringify(nodeTags.value));
};

// 获取节点的标记
const getNodeTags = (nodePath: string): Tag[] => {
  if (!nodePath) return [];
  const nodeTagIds = nodeTags.value
    .filter(nt => nt.nodePath === nodePath)
    .map(nt => nt.tagId);
  
  return allTags.value.filter(tag => nodeTagIds.includes(tag.id));
};

// 获取标记使用次数
const getTagUsageCount = (tagId: string): number => {
  return nodeTags.value.filter(nt => nt.tagId === tagId).length;
};

// 根据ID获取标记
const getTagById = (tagId: string): Tag | undefined => {
  return allTags.value.find(tag => tag.id === tagId);
};

// 检查标记是否被选中
const isTagSelected = (tagId: string): boolean => {
  return tagDialog.value.selectedTags.includes(tagId);
};

// 切换标记选择状态
const toggleTagSelection = (tagId: string) => {
  const index = tagDialog.value.selectedTags.indexOf(tagId);
  if (index > -1) {
    tagDialog.value.selectedTags.splice(index, 1);
  } else {
    tagDialog.value.selectedTags.push(tagId);
  }
};

// 创建新标记
const createNewTag = () => {
  const name = tagDialog.value.newTagName.trim();
  if (!name) return;
  
  // 检查名称是否已存在
  if (allTags.value.some(tag => tag.name === name)) {
    ElMessage.warning('标记名称已存在');
    return;
  }
  
  const newTag: Tag = {
    id: Date.now().toString(),
    name,
    color: tagDialog.value.newTagColor,
    createdAt: Date.now()
  };
  
  allTags.value.push(newTag);
  saveTagsData();
  
  // 清空输入
  tagDialog.value.newTagName = '';
  tagDialog.value.newTagColor = '#409EFF';
  
  ElMessage.success('标记创建成功');
};

// 删除标记
const deleteTag = (tagId: string) => {
  const usageCount = getTagUsageCount(tagId);
  if (usageCount > 0) {
    ElMessage.warning('该标记正在使用中，无法删除');
    return;
  }
  
  const index = allTags.value.findIndex(tag => tag.id === tagId);
  if (index > -1) {
    allTags.value.splice(index, 1);
    saveTagsData();
    ElMessage.success('标记删除成功');
  }
};

// 从选择中移除标记
const removeTagFromSelection = (tagId: string) => {
  const index = tagDialog.value.selectedTags.indexOf(tagId);
  if (index > -1) {
    tagDialog.value.selectedTags.splice(index, 1);
  }
};

// 应用标记到节点
const applyTagsToNode = () => {
  const targetPath = tagDialog.value.targetPath;
  console.log('应用标记到节点:', targetPath); // 调试信息
  
  if (!targetPath) {
    ElMessage.warning('目标路径无效');
    return;
  }
  
  console.log('当前选择的标记:', tagDialog.value.selectedTags); // 调试信息
  
  // 移除该节点的所有现有标记
  nodeTags.value = nodeTags.value.filter(nt => nt.nodePath !== targetPath);
  
  // 添加新选择的标记
  tagDialog.value.selectedTags.forEach(tagId => {
    nodeTags.value.push({
      nodePath: targetPath,
      tagId
    });
  });
  
  console.log('更新后的节点标记:', nodeTags.value); // 调试信息
  
  saveTagsData();
  closeTagDialog();
  ElMessage.success('标记应用成功');
};

// 移除节点的特定标记
const removeTag = (nodePath: string, tagId: string) => {
  const index = nodeTags.value.findIndex(nt => nt.nodePath === nodePath && nt.tagId === tagId);
  if (index > -1) {
    nodeTags.value.splice(index, 1);
    saveTagsData();
    ElMessage.success('标记移除成功');
  }
  closeContextMenu();
};

// 清除节点的所有标记
const handleRemoveAllTags = () => {
  const targetPath = contextMenu.value.target?.path;
  if (!targetPath) return;
  
  nodeTags.value = nodeTags.value.filter(nt => nt.nodePath !== targetPath);
  saveTagsData();
  closeContextMenu();
  ElMessage.success('所有标记已清除');
};

// 管理标记
const handleManageTags = () => {
  const targetPath = contextMenu.value.target?.path;
  if (!targetPath) return;
  
  // 初始化对话框状态
  tagDialog.value.targetPath = targetPath; // 保存目标路径
  tagDialog.value.selectedTags = getNodeTags(targetPath).map(tag => tag.id);
  tagDialog.value.visible = true;
  closeContextMenu();
};

// 关闭标记对话框
const closeTagDialog = () => {
  tagDialog.value.visible = false;
  tagDialog.value.selectedTags = [];
  tagDialog.value.newTagName = '';
  tagDialog.value.newTagColor = '#409EFF';
  tagDialog.value.targetPath = ''; // 清空目标路径
};

interface TreeNode {
  label: string;
  path: string;
  isDirectory: boolean;
  isLeaf?: boolean;
  children?: TreeNode[];
}

const emit = defineEmits<{
  fileSelected: [filePath: string];
  directorySelected: [dirPath: string];
  workspaceChanged: [workspacePath: string | null];
}>();

const workspaceStore = useWorkspaceStoreHook();
const router = useRouter();

const treeRef = ref();
const loading = ref(false);
const treeData = ref<TreeNode[]>([]);

// Use computed properties from store
const currentWorkspacePath = computed(
  () => workspaceStore.getCurrentWorkspacePath,
);
const workspaceName = computed(
  () =>
    workspaceStore.getCurrentWorkspacePath
      .replace(/[\\/]+$/, "")
      .split(/[\\/]/)
      .pop() || "",
);
const currentSelectedFile = computed(() => workspaceStore.getCurrentFilePath);
const openedFiles = computed(() => workspaceStore.getOpenedFiles);

// Expanded keys for tree state management
const expandedKeys = computed(() => workspaceStore.getExpandedNodes);

const defaultProps = {
  children: "children",
  label: "label",
  isLeaf: "isLeaf"
};

// Select workspace directory
const selectWorkspaceDirectory = async () => {
  try {
    const path = await window.ipcRenderer.invoke("dialog:openDirectory");
    if (path) {
      // Clear all opened files and tabs when switching workspace
      console.log('Switching to new workspace, clearing all files and tabs');
      workspaceStore.clearAllFiles();

      // Clear all tabs by dispatching a custom event
      window.dispatchEvent(new CustomEvent('clear-all-tabs'));

      // Set new workspace path
      workspaceStore.setWorkspacePath(path);

      // Don't call loadWorkspace here as the watcher will handle it
      emit('workspaceChanged', path);
    }
  } catch (error) {
    console.error("Error selecting workspace directory:", error);
    ElMessage.error("选择目录失败");
  }
};

// Load workspace files
const loadWorkspace = async () => {
  loading.value = true;
  try {
    if (currentWorkspacePath.value) {
      // 项目模式：有工作区路径，显示项目目录中的所有文件
      console.log('Loading project mode for:', currentWorkspacePath.value);
      const fileList = await window.ipcRenderer.invoke(
        "fs:readDirectory",
        currentWorkspacePath.value
      );

      treeData.value = fileList.map((file: any) => ({
        label: file.name,
        path: file.path,
        isDirectory: file.isDirectory,
        isLeaf: !file.isDirectory
      }));
      console.log('Project mode tree data set:', treeData.value);
    } else {
      // 没有工作区路径，只显示打开的文件
      const files = workspaceStore.getOpenedFiles;
      if (files.length > 0) {
        console.log('Loading opened files:', files);
        treeData.value = files.map((filePath: string) => {
          const fileName = filePath.split(/[/\\]/).pop() || filePath;
          return {
            label: fileName,
            path: filePath,
            isDirectory: false,
            isLeaf: true
          };
        });
        console.log('Opened files tree data set:', treeData.value);
      } else {
        // 没有工作区路径也没有打开的文件
        treeData.value = [];
        console.log('No files to display');
      }
    }
  } catch (error) {
    console.error("Error loading workspace:", error);
    ElMessage.error("加载工作区失败");
    treeData.value = [];
  } finally {
    loading.value = false;
  }
};

// Refresh workspace
const refreshWorkspace = async () => {
  await loadWorkspace();
};

// Lazy load child nodes
const loadNode = async (node: Node, resolve: (data: TreeNode[]) => void) => {
  if (node.level === 0) {
    return resolve(treeData.value);
  }

  const parent = node.data as TreeNode;
  if (!parent.isDirectory) return resolve([]);

  try {
    const fileList = await window.ipcRenderer.invoke(
      "fs:readDirectory",
      parent.path
    );
    const children: TreeNode[] = fileList.map((file: any) => ({
      label: file.name,
      path: file.path,
      isDirectory: file.isDirectory,
      isLeaf: !file.isDirectory
    }));
    resolve(children);
  } catch (err) {
    console.error("Error loading child directory:", err);
    resolve([]);
  }
};

// Handle node expand
const handleNodeExpand = (data: TreeNode) => {
  if (data.isDirectory) {
    workspaceStore.addExpandedNode(data.path);
  }
};

// Handle node collapse
const handleNodeCollapse = (data: TreeNode) => {
  if (data.isDirectory) {
    workspaceStore.removeExpandedNode(data.path);
  }
};

// 防止循环触发的状态锁
const syncingFromTabs = ref(false);

// 防抖过的标签页同步函数
const debouncedSyncTabs = debounce((filePath: string) => {
  if (!filePath || syncingFromTabs.value) return;
  
  // 触发标签页选中事件
  window.dispatchEvent(new CustomEvent('filetree-select-tab', {
    detail: { filePath, source: 'filetree' }
  }));
}, 15); // 减少为15ms以提高响应速度

// 处理标签页选择文件事件的函数
const handleTabSelectFile = (event: CustomEvent) => {
  const { filePath, source } = event.detail;
  
  // 如果事件来源是文件树自己，则忽略
  if (source === 'filetree') return;
  
  // 如果没有文件路径或者与当前选中文件相同，则忽略
  if (!filePath || filePath === currentSelectedFile.value) return;
  
  // 设置锁以防止循环触发
  syncingFromTabs.value = true;
  
  try {
    // 选中文件节点
    selectFileInTree(filePath);
  } finally {
    // 操作完成后立即重置锁
    syncingFromTabs.value = false;
  }
};

// 修改handleNodeClick函数
const handleNodeClick = async (data: TreeNode, node: any, component: any) => {
  if (data.isDirectory) {
    // Toggle expand/collapse on single click for directories
    if (node.expanded) {
      node.collapse();
    } else {
      node.expand();
    }
    emit('directorySelected', data.path);
  } else {
    console.log('File clicked in workspace:', data.path);

    // 如果正在从标签页同步过来，不要再同步回去
    if (syncingFromTabs.value) {
      workspaceStore.setCurrentFile(data.path);
      return;
    }

    const currentFilePath = workspaceStore.getCurrentFilePath;

    // 如果点击的是当前文件，不需要切换
    if (currentFilePath === data.path) {
      return;
    }

    // 只有在已经有当前文件且该文件有修改时才提示保存
    // 如果没有当前文件（比如从首页打开），直接切换
    if (currentFilePath &&
        workspaceStore.isDataModified(currentFilePath) &&
        workspaceStore.isExcelFile(currentFilePath)) {
      try {
        await ElMessageBox.confirm(
          `文件 "${currentFilePath.split(/[/\\]/).pop()}" 已被修改，是否要保存更改？`,
          '确认切换文件',
          {
            confirmButtonText: '保存并切换',
            cancelButtonText: '不保存',
            distinguishCancelAndClose: true,
            type: 'warning'
          }
        );

        // 用户选择保存，触发导出操作
        window.dispatchEvent(new CustomEvent('export-current-file'));
        // 等待导出完成后再切换
        setTimeout(() => {
          switchToFile(data.path);
        }, 500);
      } catch (action) {
        if (action === 'cancel') {
          // 用户选择不保存，直接切换
          switchToFile(data.path);
        } else {
          // 用户点击了X或按了ESC，取消切换操作
          return;
        }
      }
    } else {
      // 没有修改或没有当前文件，直接切换
      switchToFile(data.path);
    }
  }
};

// 切换到指定文件
const switchToFile = (filePath: string) => {
  // 立即更新存储状态和UI
  workspaceStore.setCurrentFile(filePath);
  
  // 立即通知标签页（使用防抖函数，但降低延迟）
  debouncedSyncTabs(filePath);
  
  // 路由切换处理
  if (workspaceStore.isExcelFile(filePath)) {
    // 多 tab：每个 Excel 文件一个路由
    
    // 创建延迟过渡效果，让UI响应更快
    const currentRoute = router.currentRoute.value;
    const targetPath = `/dataManagement/imandex/${encodeURIComponent(filePath)}`;
    
    // 如果已经在目标路径上，不需要重新导航
    if (currentRoute.path === targetPath) {
      console.log('Already on target route, sending UI update signal');
      // 触发一个自定义事件，通知表格组件刷新
      window.dispatchEvent(new CustomEvent('start-loading-excel', { detail: filePath }));
      return;
    }
    
    // 创建一个加载指示器，让用户知道正在切换
    const loadingInstance = ElLoading.service({
      lock: true,
      text: '正在切换...',
      background: 'rgba(0, 0, 0, 0.3)',
      customClass: 'quick-transition-loader'
    });
    
    // 立即隐藏加载指示器，给用户快速切换的感觉
    setTimeout(() => {
      loadingInstance.close();
    }, 100);
    
    // 执行路由切换
    router.push(targetPath);
  } else {
    // 非Excel文件也使用单独的路由以支持独立标签页
    const targetPath = `/workspace/file/${encodeURIComponent(filePath)}`;
    const currentRoute = router.currentRoute.value;
    
    // 如果已经在目标路径上，不需要重新导航
    if (currentRoute.path === targetPath) {
      console.log('Already on non-Excel file route, sending update signal');
      window.dispatchEvent(new CustomEvent('workspace-open-file', { detail: { filePath } }));
      return;
    }
    
    // 执行路由切换
    router.push(targetPath);
    
    // 同时触发fileSelected事件保持兼容
    emit('fileSelected', filePath);
  }
};

// Watch for workspace path changes and auto-load
watch(currentWorkspacePath, async (newPath, oldPath) => {
  if (newPath !== oldPath) {
    await loadWorkspace();
  }
}, { immediate: true });

// Watch for opened files changes when no workspace is active
watch(openedFiles, (newFiles, oldFiles) => {
  if (!currentWorkspacePath.value) {
    console.log('Opened files changed, reloading file tree.');
    loadWorkspace();
  }
}, { deep: true });

// Watch for changes in the currently selected file to sync the tree's highlight
watch(currentSelectedFile, (newPath) => {
  if (treeRef.value) {
    if (!newPath) {
      // No file is selected (e.g., last tab was closed), so clear the highlight.
      treeRef.value.setCurrentKey(null);
    } else if (currentWorkspacePath.value && !newPath.startsWith(currentWorkspacePath.value)) {
      // A file outside the current workspace is selected. Clear the highlight in this tree.
      treeRef.value.setCurrentKey(null);
    } else {
      // A file that should be visible in the tree is selected. Set the highlight.
      treeRef.value.setCurrentKey(newPath);
    }
  }
});

// Handle workspace file creation events
const handleFileCreated = async (event: CustomEvent) => {
  console.log('File created in workspace:', event.detail);
  await refreshWorkspace();
};

onMounted(() => {
  console.log("WorkspaceFileTree mounted");
  console.log("Current workspace path:", currentWorkspacePath.value);
  console.log("Current file path:", currentSelectedFile.value);

  // 初始化标记系统
  initializeTags();

  // Listen for menu-triggered import project
  window.ipcRenderer.on("menu-triggered-import-project", async () => {
    await selectWorkspaceDirectory();
  });

  // Listen for tab selection events to sync with file tree
  window.addEventListener('tab-select-file', handleTabSelectFile as EventListener);
  
  // Listen for workspace file creation events
  window.addEventListener('workspace-file-created', handleFileCreated);

  // Load workspace if path is already set or if there are opened files
  if (currentWorkspacePath.value || workspaceStore.getOpenedFiles.length > 0) {
    console.log("Loading workspace on mount");
    loadWorkspace();
  }
});

onBeforeUnmount(() => {
  window.ipcRenderer.removeAllListeners("menu-triggered-import-project");
  // Remove tab selection event listener
  window.removeEventListener('tab-select-file', handleTabSelectFile as EventListener);
  // Remove workspace file creation event listener
  window.removeEventListener('workspace-file-created', handleFileCreated);
});

// 在文件树中选中指定文件
const selectFileInTree = async (filePath: string) => {
  if (!treeRef.value || !filePath) return;
  
  // 确保文件树已加载
  await nextTick();
  
  try {
    // 设置store中的当前文件，这将触发响应式更新
    workspaceStore.setCurrentFile(filePath);
    
    // 尝试直接设置当前节点
    treeRef.value.setCurrentKey(filePath);
    
    // 如果文件不在可见区域，需要先展开父目录
    // 获取文件的父目录路径
    const pathSeparator = filePath.includes('/') ? '/' : '\\';
    const parentPath = filePath.substring(0, filePath.lastIndexOf(pathSeparator));
    
    if (parentPath && !workspaceStore.isNodeExpanded(parentPath)) {
      // 确保父目录已展开
      workspaceStore.addExpandedNode(parentPath);
      
      // 尝试展开父目录节点
      const parentNode = treeRef.value.getNode(parentPath);
      if (parentNode) {
        await parentNode.expand();
        // 再次尝试设置当前节点
        await nextTick();
        treeRef.value.setCurrentKey(filePath);
      }
    }
  } catch (err) {
    console.error("Error selecting file in tree:", err);
  }
};

// 新增：清除文件树选中状态的方法
const clearSelectedFile = () => {
  workspaceStore.setCurrentFile("");
  if (treeRef.value) {
    // 先设为null再设为''，确保el-tree内部状态被重置
    treeRef.value.setCurrentKey(null);
    treeRef.value.setCurrentKey("");
  }
};

// Expose methods for parent component
defineExpose({
  selectWorkspaceDirectory,
  refreshWorkspace,
  currentWorkspacePath,
  selectFileInTree,
  clearSelectedFile // 新增暴露
});

const contextMenu = ref({ visible: false, x: 0, y: 0, target: null as null | TreeNode });
const dialog = ref({ visible: false, type: 'file' as 'file' | 'folder', input: '', parentPath: '' });
const renameDialog = ref({ visible: false, input: '', target: null as null | TreeNode });

// 右键菜单事件
function onNodeContextMenu(e: MouseEvent, data: TreeNode) {
  e.preventDefault();
  e.stopPropagation();
  
  // 无论菜单是否显示，都更新位置和目标节点
  contextMenu.value.x = e.clientX;
  contextMenu.value.y = e.clientY;
  contextMenu.value.target = data;
  contextMenu.value.visible = true;
}

function closeContextMenu() {
  contextMenu.value.visible = false;
  contextMenu.value.target = null;
}

// 新建文件/文件夹
function handleCreateFile() {
  dialog.value.visible = true;
  dialog.value.type = 'file';
  dialog.value.input = '';
  dialog.value.parentPath = contextMenu.value.target?.isDirectory ? contextMenu.value.target.path : getParentPath(contextMenu.value.target?.path || '');
  closeContextMenu();
}
function handleCreateFolder() {
  dialog.value.visible = true;
  dialog.value.type = 'folder';
  dialog.value.input = '';
  dialog.value.parentPath = contextMenu.value.target?.path || '';
  closeContextMenu();
}

// 重命名处理
function handleRename() {
  const target = contextMenu.value.target;
  if (!target) return;
  
  renameDialog.value.visible = true;
  renameDialog.value.input = target.label;
  renameDialog.value.target = target;
  closeContextMenu();
}

// 重命名确认
async function handleRenameConfirm() {
  const target = renameDialog.value.target;
  const newName = renameDialog.value.input.trim();
  
  if (!target || !newName) {
    ElMessage.warning('名称不能为空');
    return;
  }
  
  if (newName === target.label) {
    ElMessage.warning('新名称与原名称相同');
    return;
  }
  
  // 保存当前展开状态和父目录路径
  const previouslyExpandedKeys = [...workspaceStore.getExpandedNodes];
  const parentPath = getParentPath(target.path);
  
  try {
    // 构建新路径
    const pathSeparator = target.path.includes('/') ? '/' : '\\';
    const newPath = parentPath + pathSeparator + newName;
    
    // 检查新路径是否已存在
    try {
      const accessResult = await window.ipcRenderer.invoke('fs:access', newPath);
      if (accessResult && accessResult.exists) {
        ElMessage.error('该名称已存在，请使用其他名称');
        return;
      }
    } catch {
      // 路径不存在，可以继续重命名
    }
    
    // 执行重命名
    await window.ipcRenderer.invoke('fs:rename', target.path, newPath);
    ElMessage.success('重命名成功');
    
    // 关闭对话框
    renameDialog.value.visible = false;
    renameDialog.value.input = '';
    renameDialog.value.target = null;
    
    // 如果重命名的是当前选中的文件，需要更新store中的路径
    if (currentSelectedFile.value === target.path) {
      workspaceStore.setCurrentFile(newPath);
    }
    
    // 确保父目录路径在展开列表中
    if (parentPath && !previouslyExpandedKeys.includes(parentPath)) {
      previouslyExpandedKeys.push(parentPath);
    }
    
    // 刷新工作区
    await refreshWorkspace();
    
    // 在下一个 tick 恢复展开状态
    await nextTick(async () => {
      if (treeRef.value) {
        // 按照路径层级顺序展开节点
        const sortedPaths = previouslyExpandedKeys.sort((a, b) => 
          (a.match(/[\\/]/g) || []).length - (b.match(/[\\/]/g) || []).length
        );
        
        for (const nodePath of sortedPaths) {
          const node = treeRef.value.getNode(nodePath);
          if (node?.data.isDirectory) {
            await node.expand();
          }
        }
      }
    });
  } catch (err) {
    ElMessage.error('重命名失败: ' + ((err && err.message) || err || '未知错误'));
  }
}

// 获取父目录路径
function getParentPath(path: string) {
  if (!path) return '';
  return path.replace(/[\\/][^\\/]+$/, '');
}

// 新建文件/文件夹确认
async function handleDialogConfirm() {
  const name = dialog.value.input.trim();
  if (!name) {
    ElMessage.warning('名称不能为空');
    return;
  }
  const fullPath = dialog.value.parentPath ? `${dialog.value.parentPath}${dialog.value.parentPath.endsWith('/') || dialog.value.parentPath.endsWith('\\') ? '' : '/'}${name}` : name;

  // 保存当前展开状态和父目录路径
  const previouslyExpandedKeys = [...workspaceStore.getExpandedNodes];
  const parentPath = dialog.value.parentPath;

  try {
    if (dialog.value.type === 'file') {
      await window.ipcRenderer.invoke('fs:createFile', fullPath);
      ElMessage.success('文件创建成功');
    } else {
      await window.ipcRenderer.invoke('fs:createDirectory', fullPath);
      ElMessage.success('文件夹创建成功');
    }
    dialog.value.visible = false;
    dialog.value.input = '';
    
    // 确保父目录路径也在展开列表中
    if (parentPath && !previouslyExpandedKeys.includes(parentPath)) {
      previouslyExpandedKeys.push(parentPath);
    }

    await refreshWorkspace();

    // 在下一个 tick 恢复展开状态
    await nextTick(async () => {
      if (treeRef.value) {
        // 按照路径层级顺序展开节点
        const sortedPaths = previouslyExpandedKeys.sort((a, b) => 
          (a.match(/[\\/]/g) || []).length - (b.match(/[\\/]/g) || []).length
        );
        
        for (const nodePath of sortedPaths) {
          const node = treeRef.value.getNode(nodePath);
          if (node?.data.isDirectory) {
            await node.expand();
          }
        }
      }
    });
  } catch (err) {
    ElMessage.error('创建失败: ' + (err as any)?.message);
  }
}

// 删除
async function handleDelete() {
  const target = contextMenu.value.target;
  closeContextMenu();
  if (!target) return;

  // 保存当前展开状态和父目录路径
  const previouslyExpandedKeys = [...workspaceStore.getExpandedNodes];
  const parentPath = getParentPath(target.path);

  try {
    await ElMessageBox.confirm(`确定要删除"${target.label}"吗？`, '删除确认', { type: 'warning' });
    await window.ipcRenderer.invoke('fs:deletePath', target.path);
    ElMessage.success('删除成功');

    // 从展开列表中移除被删除的目录
    const indexToRemove = previouslyExpandedKeys.indexOf(target.path);
    if (indexToRemove > -1) {
      previouslyExpandedKeys.splice(indexToRemove, 1);
    }

    // 确保父目录路径在展开列表中
    if (parentPath && !previouslyExpandedKeys.includes(parentPath)) {
      previouslyExpandedKeys.push(parentPath);
    }

    await refreshWorkspace();

    // 在下一个 tick 恢复展开状态
    await nextTick(async () => {
      if (treeRef.value) {
        // 按照路径层级顺序展开节点
        const sortedPaths = previouslyExpandedKeys.sort((a, b) => 
          (a.match(/[\\/]/g) || []).length - (b.match(/[\\/]/g) || []).length
        );
        
        for (const nodePath of sortedPaths) {
          const node = treeRef.value.getNode(nodePath);
          if (node?.data.isDirectory) {
            await node.expand();
          }
        }
      }
    });
  } catch (err) {
    // 只在用户不是点击取消时才报错
    if (err === 'cancel' || err === 'close' || (err && err.message === 'cancel')) {
      // 用户主动取消，不提示
      return;
    }
    ElMessage.error('删除失败: ' + ((err && err.message) || err || '未知错误'));
  }
}

// 判断节点是否被选中
const isNodeSelected = (filePath: string) => {
  // 严格比较当前文件路径
  return currentSelectedFile.value === filePath;
};

// 判断目录是否被选中（有文件被选中且属于该目录）
const isDirSelected = (dirPath: string) => {
  const file = currentSelectedFile.value;
  if (!file) return false;
  // 兼容正反斜杠
  const normalized = (p: string) => p.replace(/\\/g, '/');
  return normalized(file).startsWith(normalized(dirPath) + '/');
};
</script>

<style scoped>
.workspace-file-tree {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: transparent; /* 背景透明 */
  color: #303133;
  overflow: hidden;
}

/* 深色模式适配 */
html.dark .workspace-file-tree {
  background: transparent; /* 背景透明 */
  color: var(--el-text-color-primary);
}

.workspace-header {
  display: flex;
  justify-content: flex-start; /* 左对齐标题 */
  align-items: center;
  padding: 12px;
  margin-bottom: 8px;
  background: transparent; /* 移除背景 */
}

/* 深色模式适配 */
html.dark .workspace-header {
  background: transparent;
}

.workspace-title {
  display: flex;
  align-items: center;
  justify-content: flex-start; /* 左对齐 */
  gap: 10px;
  font-size: 16px; /* 修改为16px */
}

/* 深色模式适配 */
html.dark .workspace-title {
  color: #FFFFFF; /* 深色模式下为白色 */
}

.file-tree-container {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 0;
  min-height: 0; /* 确保flex子元素可以正确收缩 */
  background: transparent; /* 背景透明 */
}

/* 文件树滚动条样式 */
.file-tree-container::-webkit-scrollbar {
  width: 6px;
}

.file-tree-container::-webkit-scrollbar-track {
  background: transparent;
}

.file-tree-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.file-tree-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 树组件本身的样式调整 */
:deep(.el-tree) {
  background: transparent !important;
}

/* 文件树节点样式 */
:deep(.el-tree-node__content) {
  position: relative; /* 为伪元素定位 */
  background: transparent;
  color: #303133;
  height: auto; /* 改为auto以适应内容 */
  padding: 0; /* 移除内边距 */
  margin: 0; /* 移除外边距 */
  display: flex;
  align-items: center;
}

/* 文件节点的容器需要撑满 */
:deep(.tree-node-file-wrapper) {
  width: 100%;
  height: 36px; /* 32px + 4px间距 */
  display: flex;
  align-items: center;
}
:deep(.tree-node-file-wrapper)::before {
  content: '';
  position: absolute;
  top: 2px;
  bottom: 2px;
  left: 8px; /* 卡片左侧边距 */
  right: 8px; /* 卡片右侧边距 */
  background-color: white;
  border-radius: 4px;
  z-index: -1;
  box-shadow: 0 1px 2px rgba(0,0,0,0.05);
}

/* 文件夹节点样式保持基本一致 */
:deep(.el-tree-node__content > .tree-node:not(.tree-node-file-wrapper)) {
  height: 28px;
  margin: 2px 0;
  border-radius: 4px;
    width: 100%;
}

/* 悬停效果 */
:deep(.el-tree-node__content:hover) {
  background-color: transparent;
}
:deep(.tree-node-file-wrapper:hover)::before {
  background-color: #f5f7fa;
}
:deep(.el-tree-node__content:hover > .tree-node:not(.tree-node-file-wrapper)) {
  background-color: rgba(64, 158, 255, 0.1);
}

/* 选中效果 */
:deep(.el-tree-node.is-current > .el-tree-node__content) {
  background-color: transparent;
  color: inherit;
}
:deep(.el-tree-node.is-current > .el-tree-node__content .tree-node-file-wrapper)::before {
  background-color: rgba(0, 93, 255, 0.1);
}
:deep(.el-tree-node.is-current > .el-tree-node__content > .tree-node:not(.tree-node-file-wrapper)) {
  background-color: rgba(0, 93, 255, 0.1);
}
:deep(.el-tree-node.is-current > .el-tree-node__content .node-label) {
  color: #005dff;
  font-weight: bold;
}

:deep(.el-overlay-dialog) {
  text-align: center;
  white-space: nowrap;
  overflow: auto;
  &:after {
    content: "";
    display: inline-block;
    vertical-align: middle;
    height: 100%;
  }
  .el-dialog {
    margin: 30px auto !important;
    display: inline-block;
    vertical-align: middle;
    text-align: center;
    white-space: normal;
  }
}


/* 深色模式适配 */
html.dark :deep(.el-tree-node__content) {
  color: var(--el-text-color-primary);
}
html.dark :deep(.tree-node-file-wrapper)::before {
  background-color: var(--el-bg-color-overlay);
}
html.dark :deep(.tree-node-file-wrapper:hover)::before {
  background-color: rgba(255, 255, 255, 0.1);
}
html.dark :deep(.el-tree-node__content:hover > .tree-node:not(.tree-node-file-wrapper)) {
  background-color: rgba(64, 158, 255, 0.1);
}
html.dark :deep(.el-tree-node.is-current > .el-tree-node__content .tree-node-file-wrapper)::before {
  background-color: rgba(0, 93, 255, 0.15);
}
html.dark :deep(.el-tree-node.is-current > .el-tree-node__content > .tree-node:not(.tree-node-file-wrapper)) {
  background-color: rgba(0, 93, 255, 0.1);
}
html.dark :deep(.el-tree-node.is-current > .el-tree-node__content .node-label) {
  color: var(--el-color-primary);
}


/* 确保节点内容布局正确 */
.tree-node {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  width: 100%;
  height: 100%;
  padding: 0 2px;
  cursor: pointer;
}

/* 文件节点的容器需要撑满 */
:deep(.tree-node-file-wrapper) {
  width: 100%;
  height: 36px; /* 32px + 4px间距 */
  display: flex;
  align-items: center;
}

/* 确保图标、标记、文字的顺序和间距 */
.node-icon {
  color: #909399;
  flex-shrink: 0; /* 防止图标被压缩 */
}

.node-label {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: 14px;
  color: #000000;
  min-width: 0; /* 允许文字容器收缩 */
}

/* 展开图标样式修改 */
:deep(.el-tree-node__expand-icon) {
  color: #000000; /* 改为黑色 */
  font-size: 12px; /* 缩小图标 */
  margin-right: 2px;
  transform: scale(0.8);
}

/* 深色模式适配 */
html.dark :deep(.el-tree-node__expand-icon) {
  color: #FFFFFF; /* 深色模式下为白色 */
}

:deep(.el-tree-node__expand-icon.expanded) {
  transform: rotate(90deg) scale(0.8); /* 保持缩小比例 */
}

/* 增加缩进区分层级 */
:deep(.el-tree-node__children) {
  padding-left: 16px; /* 增加子节点缩进 */
}

/* 确保根节点没有左边距 */
.workspace-tree > :deep(.el-tree-node) > :deep(.el-tree-node__content) {
  padding-left: 0 !important;
}

.file-label-ellipsis {
  max-width: 180px;
  display: inline-block;
  vertical-align: middle;
}

/* 空状态样式调整 */
.empty-workspace,
.empty-directory,
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  color: #909399;
  font-size: 12px;
  height: 100%;
}

/* 深色模式适配 */
html.dark .empty-workspace,
html.dark .empty-directory,
html.dark .loading-container {
  color: var(--el-text-color-primary);
}

.loading-container {
  gap: 8px;
}

.workspace-tree {
  background: transparent;
  color: #303133;
}

/* 深色模式适配 */
html.dark .workspace-tree {
  color: var(--el-text-color-primary);
}

/* Ensure tree node content uses flex layout for proper alignment */
:deep(.el-tree-node__content) {
  display: flex !important;
  align-items: center !important;
  height: auto !important;
  padding: 0 !important;
  margin: 0 !important;
}

/* Ensure selected tree node has same height */
:deep(.el-tree-node.is-current > .el-tree-node__content) {
  height: auto !important;
  margin: 0 !important;
}

.context-menu-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9998;
  background: transparent;
}

.custom-context-menu {
  position: fixed;
  z-index: 9999;
  background: #fff;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1);
  min-width: 120px;
  padding: 4px 0;
  user-select: none;
}

.custom-context-menu .menu-item {
  padding: 8px 20px;
  font-size: 14px;
  color: #333;
  cursor: pointer;
  transition: background 0.2s;
}

.custom-context-menu .menu-item:hover {
  background: #f5f7fa;
}

.custom-context-menu .menu-item.delete {
  color: #f56c6c;
}

/* 深色模式适配 */
html.dark .custom-context-menu {
  background: var(--el-bg-color-overlay);
  border-color: var(--el-border-color);
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.3);
}

html.dark .custom-context-menu .menu-item {
  color: var(--el-text-color-primary);
}

html.dark .custom-context-menu .menu-item:hover {
  background: var(--el-fill-color-light);
}

html.dark .custom-context-menu .menu-item.delete {
  color: var(--el-color-danger);
}

/* 标记相关样式 */
.tags-container {
  display: flex;
  gap: 4px;
  align-items: center;
  flex-shrink: 0; /* 防止标记容器被压缩 */
}

.tag-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  transition: transform 0.2s;
  border: 1px solid rgba(255, 255, 255, 0.8);
  cursor: default; /* 移除点击光标，因为现在只是显示用 */
}

.tag-dot:hover {
  transform: scale(1.2);
}

.tag-dot-small {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  display: inline-block;
  margin-right: 6px;
}

.tag-dot-medium {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  display: inline-block;
  margin-right: 8px;
}

/* 右键菜单分隔符 */
.menu-separator {
  height: 1px;
  background-color: #dcdfe6;
  margin: 4px 0;
}

/* 标记菜单项 */
.tag-menu-item {
  display: flex;
  align-items: center;
  padding: 6px 20px;
  font-size: 13px;
}

/* 标记管理对话框样式 */
.tag-management-container {
  max-height: 500px;
  overflow-y: auto;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #ebeef5;
}

.section-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.section-desc {
  font-size: 12px;
  color: #909399;
}

.tags-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 10px;
  margin-bottom: 15px;
}

.tag-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
  background-color: #f5f7fa;
}

.tag-item:hover {
  background-color: #ecf5ff;
  border-color: #409eff;
}

.tag-item.tag-selected {
  background-color: #ecf5ff;
  border-color: #409eff;
}

.tag-name {
  flex: 1;
  margin-right: 8px;
  font-size: 13px;
  color: #303133;
}

.tag-count {
  font-size: 11px;
  color: #909399;
  margin-right: 8px;
}

.delete-btn {
  flex-shrink: 0;
}

.new-tag-form {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 15px;
}

.tag-name-input {
  flex: 1;
}

.color-picker {
  flex-shrink: 0;
}

.create-btn {
  flex-shrink: 0;
}

.selected-tags {
  min-height: 32px;
  padding: 8px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background-color: #f5f7fa;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
}

.selected-tag {
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  gap: 4px;
  border-width: 1px;
  border-style: solid;
}

.no-tags {
  font-size: 12px;
  color: #909399;
  padding: 8px;
}

.apply-actions {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

/* 深色模式适配 */
html.dark .section-header {
  border-bottom-color: var(--el-border-color);
}

html.dark .section-header h4 {
  color: var(--el-text-color-primary);
}

html.dark .section-desc {
  color: var(--el-text-color-regular);
}

html.dark .tags-grid {
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
}

html.dark .tag-item {
  border-color: var(--el-border-color);
  background-color: var(--el-fill-color-light);
}

html.dark .tag-item:hover {
  background-color: rgba(64, 158, 255, 0.1);
  border-color: var(--el-color-primary);
}

html.dark .tag-item.tag-selected {
  background-color: rgba(64, 158, 255, 0.1);
  border-color: var(--el-color-primary);
}

html.dark .tag-name {
  color: var(--el-text-color-primary);
}

html.dark .tag-count {
  color: var(--el-text-color-regular);
}

html.dark .new-tag-form {
  border-bottom-color: var(--el-border-color);
}

html.dark .tag-name-input {
  color: var(--el-text-color-primary);
  background-color: var(--el-fill-color-light);
  border-color: var(--el-border-color);
}

html.dark .color-picker .el-color-picker__trigger {
  background-color: var(--el-fill-color-light);
  border-color: var(--el-border-color);
}

html.dark .color-picker .el-color-picker__trigger:hover {
  background-color: var(--el-fill-color-light);
}

html.dark .color-picker .el-color-picker__trigger .el-icon {
  color: var(--el-text-color-regular);
}

html.dark .selected-tags {
  border-color: var(--el-border-color);
  background-color: var(--el-fill-color-light);
}

html.dark .no-tags {
  color: var(--el-text-color-regular);
}

html.dark .menu-separator {
  background-color: var(--el-border-color);
}
</style>