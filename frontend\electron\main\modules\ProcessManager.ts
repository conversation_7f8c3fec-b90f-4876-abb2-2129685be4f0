import { spawn, type ChildProcess, exec } from "node:child_process";
import { existsSync } from "node:fs";
import { join, dirname } from "node:path";
import { app } from "electron";
import {
  checkExistingBackendProcesses,
  checkExistingRabbitMQProcesses,
} from "../utils";

/**
 * 进程管理器 - 负责管理后端服务和RabbitMQ进程
 */
export class ProcessManager {
  private backendProcess: ChildProcess | null = null;
  private backendPID: number | null = null;
  private runningBackendProcesses: number[] = [];
  private rabbitmqProcess: ChildProcess | null = null;
  private isShuttingDown = false;

  /**
   * 终止指定PID的进程
   */
  async killProcess(pid: number): Promise<boolean> {
    return new Promise((resolve) => {
      if (!pid || pid <= 0) {
        resolve(false);
        return;
      }

      console.log(`Attempting to kill process with PID: ${pid}`);

      try {
        if (process.platform === "win32") {
          exec(`taskkill /pid ${pid} /T /F`, (err) => {
            if (err) {
              console.error(`Failed to kill process ${pid}:`, err);
              resolve(false);
            } else {
              console.log(`Successfully terminated process ${pid}`);
              const index = this.runningBackendProcesses.indexOf(pid);
              if (index !== -1) {
                this.runningBackendProcesses.splice(index, 1);
              }
              resolve(true);
            }
          });
        } else {
          try {
            process.kill(pid, "SIGTERM");
            setTimeout(() => {
              try {
                process.kill(pid, 0);
                process.kill(pid, "SIGKILL");
                console.log(`Had to use SIGKILL for ${pid}`);
              } catch {
                // 进程已经终止
              }

              const index = this.runningBackendProcesses.indexOf(pid);
              if (index !== -1) {
                this.runningBackendProcesses.splice(index, 1);
              }

              resolve(true);
            }, 1000);
          } catch (e) {
            console.error(`Failed to kill process ${pid}:`, e);
            resolve(false);
          }
        }
      } catch (error) {
        console.error(`Error in killProcess for PID ${pid}:`, error);
        resolve(false);
      }
    });
  }

  /**
   * 启动后端服务
   */
  async startBackendService(): Promise<boolean> {
    return new Promise(async (resolve) => {
      try {
        // 先检查是否有已运行的后端进程
        const existingPids = await checkExistingBackendProcesses();

        // 终止所有已存在的进程
        for (const pid of existingPids) {
          await this.killProcess(pid);
        }

        // 如果当前已有进程，则不再启动新的
        if (this.backendProcess !== null && this.backendPID !== null) {
          console.log(
            `Backend service already running with PID: ${this.backendPID}`,
          );
          resolve(true);
          return;
        }

        const backendExecutablePath = app.isPackaged
          ? join(process.resourcesPath, "backend", "runserver.exe")
          : join(app.getAppPath(), "../backend/dist/backend", "runserver.exe");

        console.log("Resource path:", process.resourcesPath);
        console.log(`Starting backend service from: ${backendExecutablePath}`);

        if (!existsSync(backendExecutablePath)) {
          console.error(
            `Backend executable not found at: ${backendExecutablePath}`,
          );
          resolve(false);
          return;
        }

        console.log(`Backend executable exists, attempting to spawn process`);

        this.backendProcess = spawn(backendExecutablePath, [], {
          windowsHide: false,
          stdio: "pipe",
          cwd: app.isPackaged
            ? join(process.resourcesPath, "backend")
            : undefined,
          detached: false,
        });

        if (!this.backendProcess || !this.backendProcess.pid) {
          console.error("Failed to start backend service: Invalid process");
          resolve(false);
          return;
        }

        this.backendPID = this.backendProcess.pid;
        this.runningBackendProcesses.push(this.backendPID);

        console.log("Backend service started with PID:", this.backendPID);

        // 监听后端输出，确认启动成功
        let startupTimeout: NodeJS.Timeout;
        let isStarted = false;
        let hasFlaskApp = false;
        let hasRunningOn = false;

        const checkStartup = (data: Buffer) => {
          const output = data.toString();
          console.log("Backend output:", output);

          if (
            output.includes("Serving Flask app") ||
            output.includes("Flask 应")
          ) {
            hasFlaskApp = true;
            console.log("Flask app detected");
          }

          if (
            output.includes("Running on http://") ||
            output.includes("* Running on all addresses") ||
            output.includes("Press CTRL+C to quit")
          ) {
            hasRunningOn = true;
            console.log("Server listening detected");
          }

          if (hasFlaskApp && hasRunningOn && !isStarted) {
            isStarted = true;
            clearTimeout(startupTimeout);
            console.log(
              "Backend service started successfully - Flask app is serving and listening",
            );
            resolve(true);
          }

          if (
            (output.includes("Running on http://") &&
              output.includes("Press CTRL+C to quit")) ||
            output.includes("Server started") ||
            output.includes("Backend ready") ||
            output.includes("服务已启动")
          ) {
            if (!isStarted) {
              isStarted = true;
              clearTimeout(startupTimeout);
              console.log("Backend service started successfully");
              resolve(true);
            }
          }
        };

        const checkStartupError = (data: Buffer) => {
          const output = data.toString();
          console.log("Backend error:", output);

          if (
            output.includes("Serving Flask app") ||
            output.includes("Flask 应")
          ) {
            hasFlaskApp = true;
            console.log("Flask app detected (from stderr)");
          }

          if (
            output.includes("Running on http://") ||
            output.includes("* Running on all addresses") ||
            output.includes("Press CTRL+C to quit")
          ) {
            hasRunningOn = true;
            console.log("Server listening detected (from stderr)");
          }

          if (hasFlaskApp && hasRunningOn && !isStarted) {
            isStarted = true;
            clearTimeout(startupTimeout);
            console.log(
              "Backend service started successfully - Flask app is serving and listening (from stderr)",
            );
            resolve(true);
          }

          if (
            output.includes("Address already in use") ||
            output.includes("Permission denied") ||
            output.includes("Fatal error") ||
            output.includes("Failed to start") ||
            (output.includes("Error:") &&
              !output.includes("INFO") &&
              !output.includes("WARNING"))
          ) {
            if (!isStarted) {
              console.error("Backend startup error detected:", output);
              clearTimeout(startupTimeout);
              resolve(false);
            }
          }
        };

        if (this.backendProcess.stdout) {
          this.backendProcess.stdout.on("data", checkStartup);
        }

        if (this.backendProcess.stderr) {
          this.backendProcess.stderr.on("data", checkStartupError);
        }

        this.backendProcess.on("error", (err) => {
          console.error("Failed to start backend service:", err);

          if (this.backendPID !== null) {
            const index = this.runningBackendProcesses.indexOf(this.backendPID);
            if (index !== -1) {
              this.runningBackendProcesses.splice(index, 1);
            }
          }

          this.backendProcess = null;
          this.backendPID = null;

          if (!isStarted) {
            clearTimeout(startupTimeout);
            resolve(false);
          }
        });

        this.backendProcess.on("close", (code) => {
          console.log(`Backend service exited with code ${code}`);

          if (this.backendPID !== null) {
            const index = this.runningBackendProcesses.indexOf(this.backendPID);
            if (index !== -1) {
              this.runningBackendProcesses.splice(index, 1);
            }
          }

          this.backendProcess = null;
          this.backendPID = null;

          if (!isStarted && !this.isShuttingDown) {
            clearTimeout(startupTimeout);
            resolve(false);
          }
        });

        startupTimeout = setTimeout(() => {
          if (!isStarted) {
            console.error("Backend service startup timeout");
            console.log(
              `Startup status: hasFlaskApp=${hasFlaskApp}, hasRunningOn=${hasRunningOn}`,
            );
            resolve(false);
          }
        }, 45000);
      } catch (error) {
        console.error("Error starting backend service:", error);
        this.backendProcess = null;
        this.backendPID = null;
        resolve(false);
      }
    });
  }

  /**
   * 启动RabbitMQ服务
   */
  async startRabbitMQ(): Promise<boolean> {
    return new Promise(async (resolve) => {
      try {
        const existingPids = await checkExistingRabbitMQProcesses();

        if (existingPids.length > 0) {
          console.log(
            `Terminating ${existingPids.length} existing RabbitMQ processes before starting new one`,
          );
          for (const pid of existingPids) {
            await this.killProcess(pid);
          }
          await new Promise((resolve) => setTimeout(resolve, 2000));
        }

        const rabbitmqScriptPath = app.isPackaged
          ? join(process.resourcesPath, "start_rabbitmq.bat")
          : join(app.getAppPath(), "../start_rabbitmq.bat");

        console.log(`Starting RabbitMQ service from: ${rabbitmqScriptPath}`);

        if (!existsSync(rabbitmqScriptPath)) {
          console.error(`RabbitMQ script not found at: ${rabbitmqScriptPath}`);
          resolve(false);
          return;
        }

        console.log(`RabbitMQ script exists, attempting to start service`);

        const workingDir = app.isPackaged
          ? dirname(rabbitmqScriptPath)
          : join(app.getAppPath(), "..");

        console.log(`RabbitMQ working directory: ${workingDir}`);

        this.rabbitmqProcess = spawn(rabbitmqScriptPath, [], {
          windowsHide: false,
          stdio: "pipe",
          cwd: workingDir,
          shell: true,
        });

        if (!this.rabbitmqProcess || !this.rabbitmqProcess.pid) {
          console.error("Failed to start RabbitMQ: Invalid process");
          resolve(false);
          return;
        }

        console.log(
          "RabbitMQ process started with PID:",
          this.rabbitmqProcess.pid,
        );

        let startupTimeout: NodeJS.Timeout;
        let isStarted = false;

        const checkStartup = (data: Buffer) => {
          const output = data.toString();
          console.log("RabbitMQ output:", output);

          if (
            output.includes("completed with") ||
            output.includes("started") ||
            (output.includes("RabbitMQ") && output.includes("running")) ||
            output.includes("broker running") ||
            output.includes("Starting broker")
          ) {
            isStarted = true;
            clearTimeout(startupTimeout);
            console.log("RabbitMQ service started successfully");
            resolve(true);
          }

          if (
            output.includes("error") ||
            output.includes("failed") ||
            output.includes("could not start")
          ) {
            console.error("RabbitMQ startup error detected:", output);
            clearTimeout(startupTimeout);
            resolve(false);
          }
        };

        if (this.rabbitmqProcess.stdout) {
          this.rabbitmqProcess.stdout.on("data", checkStartup);
        }

        if (this.rabbitmqProcess.stderr) {
          this.rabbitmqProcess.stderr.on("data", (data) => {
            const errorOutput = data.toString();
            console.error("RabbitMQ error:", errorOutput);
            checkStartup(data);
          });
        }

        this.rabbitmqProcess.on("error", (err) => {
          console.error("Failed to start RabbitMQ service:", err);
          this.rabbitmqProcess = null;

          if (!isStarted) {
            clearTimeout(startupTimeout);
            resolve(false);
          }
        });

        this.rabbitmqProcess.on("close", (code) => {
          console.log(`RabbitMQ service exited with code ${code}`);
          this.rabbitmqProcess = null;

          if (!isStarted && !this.isShuttingDown) {
            clearTimeout(startupTimeout);
            resolve(false);
          }
        });

        startupTimeout = setTimeout(() => {
          if (!isStarted) {
            console.error("RabbitMQ service startup timeout");
            resolve(false);
          }
        }, 60000);
      } catch (error) {
        console.error("Error starting RabbitMQ service:", error);
        this.rabbitmqProcess = null;
        resolve(false);
      }
    });
  }

  /**
   * 终止RabbitMQ服务
   */
  async terminateRabbitMQ(): Promise<void> {
    return new Promise((resolve) => {
      if (this.rabbitmqProcess === null) {
        console.log("RabbitMQ process is already null");
        resolve();
        return;
      }

      console.log("Terminating RabbitMQ service...");

      try {
        if (process.platform === "win32" && this.rabbitmqProcess.pid) {
          exec(`taskkill /F /T /PID ${this.rabbitmqProcess.pid}`, (err) => {
            if (err) {
              console.error("Error terminating RabbitMQ process:", err);
            } else {
              console.log("RabbitMQ service terminated successfully.");
            }
            this.rabbitmqProcess = null;
            resolve();
          });
        } else {
          this.rabbitmqProcess.kill();
          console.log("RabbitMQ service terminated successfully.");
          this.rabbitmqProcess = null;
          resolve();
        }
      } catch (error) {
        console.error("Error terminating RabbitMQ service:", error);
        this.rabbitmqProcess = null;
        resolve();
      }
    });
  }

  /**
   * 终止所有后端进程
   */
  async terminateAllBackendProcesses() {
    console.log("Terminating all backend processes...");

    if (this.backendProcess !== null && this.backendPID !== null) {
      try {
        await this.killProcess(this.backendPID);
      } catch (error) {
        console.error(
          `Error terminating current backend process ${this.backendPID}:`,
          error,
        );
      }
      this.backendProcess = null;
      this.backendPID = null;
    }

    const processes = [...this.runningBackendProcesses];
    for (const pid of processes) {
      await this.killProcess(pid);
    }

    const remainingPids = await checkExistingBackendProcesses();
    for (const pid of remainingPids) {
      await this.killProcess(pid);
    }
  }

  /**
   * 终止所有RabbitMQ相关进程
   */
  async terminateAllRabbitMQProcesses() {
    console.log("Terminating all RabbitMQ processes...");

    if (this.rabbitmqProcess !== null && this.rabbitmqProcess.pid) {
      try {
        console.log(
          `Terminating current RabbitMQ process with PID: ${this.rabbitmqProcess.pid}`,
        );
        await this.terminateRabbitMQ();
      } catch (error) {
        console.error(`Error terminating current RabbitMQ process:`, error);
      }
      this.rabbitmqProcess = null;
    }

    try {
      const remainingPids = await checkExistingRabbitMQProcesses();
      console.log(
        `Found ${remainingPids.length} remaining RabbitMQ processes to terminate`,
      );
      for (const pid of remainingPids) {
        await this.killProcess(pid);
      }
    } catch (error) {
      console.error(`Error checking for remaining RabbitMQ processes:`, error);
    }
  }

  /**
   * 设置关闭标志
   */
  setShuttingDown(value: boolean) {
    this.isShuttingDown = value;
  }

  /**
   * 获取后端进程状态
   */
  getBackendStatus() {
    return {
      process: this.backendProcess,
      pid: this.backendPID,
      runningProcesses: [...this.runningBackendProcesses],
    };
  }

  /**
   * 获取RabbitMQ进程状态
   */
  getRabbitMQStatus() {
    return {
      process: this.rabbitmqProcess,
      pid: this.rabbitmqProcess?.pid || null,
    };
  }
}
