"use strict";
var __defProp = Object.defineProperty;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __publicField = (obj, key, value) => __defNormalProp(obj, typeof key !== "symbol" ? key + "" : key, value);
Object.defineProperty(exports, Symbol.toStringTag, { value: "Module" });
const node_os = require("node:os");
const node_url = require("node:url");
const node_path = require("node:path");
const electron = require("electron");
const node_fs = require("node:fs");
const node_child_process = require("node:child_process");
const fs = require("fs");
const path = require("path");
const dragAddon = require("electron-click-drag-plugin");
var _documentCurrentScript = typeof document !== "undefined" ? document.currentScript : null;
async function checkExistingBackendProcesses(processName = "runserver.exe") {
  return new Promise((resolve) => {
    if (process.platform !== "win32") {
      resolve([]);
      return;
    }
    const cmd = `wmic process where "name='${processName}'" get processid`;
    node_child_process.exec(cmd, (error, stdout) => {
      if (error) {
        console.log(
          "No existing backend processes found or error running command"
        );
        resolve([]);
        return;
      }
      const pidPattern = /(\d+)/g;
      const pids = [];
      let match;
      while ((match = pidPattern.exec(stdout)) !== null) {
        const pid = parseInt(match[1], 10);
        if (!isNaN(pid) && pid !== process.pid) {
          pids.push(pid);
        }
      }
      console.log(`Found ${pids.length} existing backend processes:`, pids);
      resolve(pids);
    });
  });
}
async function checkExistingRabbitMQProcesses() {
  return new Promise((resolve) => {
    if (process.platform !== "win32") {
      resolve([]);
      return;
    }
    const cmd = `wmic process where "name='erl.exe' or name='epmd.exe'" get processid,name`;
    node_child_process.exec(cmd, (error, stdout) => {
      if (error) {
        console.log(
          "No existing RabbitMQ processes found or error running command"
        );
        resolve([]);
        return;
      }
      const processLines = stdout.trim().split("\n").slice(1);
      const pids = [];
      for (const line of processLines) {
        const match = line.trim().match(/(\d+)\s+(.+)/);
        if (match) {
          const pid = parseInt(match[1], 10);
          const name = match[2].trim();
          if (!isNaN(pid) && pid !== process.pid) {
            console.log(
              `Found RabbitMQ related process: ${name} (PID: ${pid})`
            );
            pids.push(pid);
          }
        }
      }
      console.log(`Found ${pids.length} existing RabbitMQ processes`);
      resolve(pids);
    });
  });
}
class ProcessManager {
  constructor() {
    __publicField(this, "backendProcess", null);
    __publicField(this, "backendPID", null);
    __publicField(this, "runningBackendProcesses", []);
    __publicField(this, "rabbitmqProcess", null);
    __publicField(this, "isShuttingDown", false);
  }
  /**
   * 终止指定PID的进程
   */
  async killProcess(pid) {
    return new Promise((resolve) => {
      if (!pid || pid <= 0) {
        resolve(false);
        return;
      }
      console.log(`Attempting to kill process with PID: ${pid}`);
      try {
        if (process.platform === "win32") {
          node_child_process.exec(`taskkill /pid ${pid} /T /F`, (err) => {
            if (err) {
              console.error(`Failed to kill process ${pid}:`, err);
              resolve(false);
            } else {
              console.log(`Successfully terminated process ${pid}`);
              const index = this.runningBackendProcesses.indexOf(pid);
              if (index !== -1) {
                this.runningBackendProcesses.splice(index, 1);
              }
              resolve(true);
            }
          });
        } else {
          try {
            process.kill(pid, "SIGTERM");
            setTimeout(() => {
              try {
                process.kill(pid, 0);
                process.kill(pid, "SIGKILL");
                console.log(`Had to use SIGKILL for ${pid}`);
              } catch {
              }
              const index = this.runningBackendProcesses.indexOf(pid);
              if (index !== -1) {
                this.runningBackendProcesses.splice(index, 1);
              }
              resolve(true);
            }, 1e3);
          } catch (e) {
            console.error(`Failed to kill process ${pid}:`, e);
            resolve(false);
          }
        }
      } catch (error) {
        console.error(`Error in killProcess for PID ${pid}:`, error);
        resolve(false);
      }
    });
  }
  /**
   * 启动后端服务
   */
  async startBackendService() {
    return new Promise(async (resolve) => {
      try {
        const existingPids = await checkExistingBackendProcesses();
        for (const pid of existingPids) {
          await this.killProcess(pid);
        }
        if (this.backendProcess !== null && this.backendPID !== null) {
          console.log(
            `Backend service already running with PID: ${this.backendPID}`
          );
          resolve(true);
          return;
        }
        const backendExecutablePath = electron.app.isPackaged ? node_path.join(process.resourcesPath, "backend", "runserver.exe") : node_path.join(electron.app.getAppPath(), "../backend/dist/backend", "runserver.exe");
        console.log("Resource path:", process.resourcesPath);
        console.log(`Starting backend service from: ${backendExecutablePath}`);
        if (!node_fs.existsSync(backendExecutablePath)) {
          console.error(
            `Backend executable not found at: ${backendExecutablePath}`
          );
          resolve(false);
          return;
        }
        console.log(`Backend executable exists, attempting to spawn process`);
        this.backendProcess = node_child_process.spawn(backendExecutablePath, [], {
          windowsHide: false,
          stdio: "pipe",
          cwd: electron.app.isPackaged ? node_path.join(process.resourcesPath, "backend") : void 0,
          detached: false
        });
        if (!this.backendProcess || !this.backendProcess.pid) {
          console.error("Failed to start backend service: Invalid process");
          resolve(false);
          return;
        }
        this.backendPID = this.backendProcess.pid;
        this.runningBackendProcesses.push(this.backendPID);
        console.log("Backend service started with PID:", this.backendPID);
        let startupTimeout;
        let isStarted = false;
        let hasFlaskApp = false;
        let hasRunningOn = false;
        const checkStartup = (data) => {
          const output = data.toString();
          console.log("Backend output:", output);
          if (output.includes("Serving Flask app") || output.includes("Flask 应")) {
            hasFlaskApp = true;
            console.log("Flask app detected");
          }
          if (output.includes("Running on http://") || output.includes("* Running on all addresses") || output.includes("Press CTRL+C to quit")) {
            hasRunningOn = true;
            console.log("Server listening detected");
          }
          if (hasFlaskApp && hasRunningOn && !isStarted) {
            isStarted = true;
            clearTimeout(startupTimeout);
            console.log(
              "Backend service started successfully - Flask app is serving and listening"
            );
            resolve(true);
          }
          if (output.includes("Running on http://") && output.includes("Press CTRL+C to quit") || output.includes("Server started") || output.includes("Backend ready") || output.includes("服务已启动")) {
            if (!isStarted) {
              isStarted = true;
              clearTimeout(startupTimeout);
              console.log("Backend service started successfully");
              resolve(true);
            }
          }
        };
        const checkStartupError = (data) => {
          const output = data.toString();
          console.log("Backend error:", output);
          if (output.includes("Serving Flask app") || output.includes("Flask 应")) {
            hasFlaskApp = true;
            console.log("Flask app detected (from stderr)");
          }
          if (output.includes("Running on http://") || output.includes("* Running on all addresses") || output.includes("Press CTRL+C to quit")) {
            hasRunningOn = true;
            console.log("Server listening detected (from stderr)");
          }
          if (hasFlaskApp && hasRunningOn && !isStarted) {
            isStarted = true;
            clearTimeout(startupTimeout);
            console.log(
              "Backend service started successfully - Flask app is serving and listening (from stderr)"
            );
            resolve(true);
          }
          if (output.includes("Address already in use") || output.includes("Permission denied") || output.includes("Fatal error") || output.includes("Failed to start") || output.includes("Error:") && !output.includes("INFO") && !output.includes("WARNING")) {
            if (!isStarted) {
              console.error("Backend startup error detected:", output);
              clearTimeout(startupTimeout);
              resolve(false);
            }
          }
        };
        if (this.backendProcess.stdout) {
          this.backendProcess.stdout.on("data", checkStartup);
        }
        if (this.backendProcess.stderr) {
          this.backendProcess.stderr.on("data", checkStartupError);
        }
        this.backendProcess.on("error", (err) => {
          console.error("Failed to start backend service:", err);
          if (this.backendPID !== null) {
            const index = this.runningBackendProcesses.indexOf(this.backendPID);
            if (index !== -1) {
              this.runningBackendProcesses.splice(index, 1);
            }
          }
          this.backendProcess = null;
          this.backendPID = null;
          if (!isStarted) {
            clearTimeout(startupTimeout);
            resolve(false);
          }
        });
        this.backendProcess.on("close", (code) => {
          console.log(`Backend service exited with code ${code}`);
          if (this.backendPID !== null) {
            const index = this.runningBackendProcesses.indexOf(this.backendPID);
            if (index !== -1) {
              this.runningBackendProcesses.splice(index, 1);
            }
          }
          this.backendProcess = null;
          this.backendPID = null;
          if (!isStarted && !this.isShuttingDown) {
            clearTimeout(startupTimeout);
            resolve(false);
          }
        });
        startupTimeout = setTimeout(() => {
          if (!isStarted) {
            console.error("Backend service startup timeout");
            console.log(
              `Startup status: hasFlaskApp=${hasFlaskApp}, hasRunningOn=${hasRunningOn}`
            );
            resolve(false);
          }
        }, 45e3);
      } catch (error) {
        console.error("Error starting backend service:", error);
        this.backendProcess = null;
        this.backendPID = null;
        resolve(false);
      }
    });
  }
  /**
   * 启动RabbitMQ服务
   */
  async startRabbitMQ() {
    return new Promise(async (resolve) => {
      try {
        const existingPids = await checkExistingRabbitMQProcesses();
        if (existingPids.length > 0) {
          console.log(
            `Terminating ${existingPids.length} existing RabbitMQ processes before starting new one`
          );
          for (const pid of existingPids) {
            await this.killProcess(pid);
          }
          await new Promise((resolve2) => setTimeout(resolve2, 2e3));
        }
        const rabbitmqScriptPath = electron.app.isPackaged ? node_path.join(process.resourcesPath, "start_rabbitmq.bat") : node_path.join(electron.app.getAppPath(), "../start_rabbitmq.bat");
        console.log(`Starting RabbitMQ service from: ${rabbitmqScriptPath}`);
        if (!node_fs.existsSync(rabbitmqScriptPath)) {
          console.error(`RabbitMQ script not found at: ${rabbitmqScriptPath}`);
          resolve(false);
          return;
        }
        console.log(`RabbitMQ script exists, attempting to start service`);
        const workingDir = electron.app.isPackaged ? node_path.dirname(rabbitmqScriptPath) : node_path.join(electron.app.getAppPath(), "..");
        console.log(`RabbitMQ working directory: ${workingDir}`);
        this.rabbitmqProcess = node_child_process.spawn(rabbitmqScriptPath, [], {
          windowsHide: false,
          stdio: "pipe",
          cwd: workingDir,
          shell: true
        });
        if (!this.rabbitmqProcess || !this.rabbitmqProcess.pid) {
          console.error("Failed to start RabbitMQ: Invalid process");
          resolve(false);
          return;
        }
        console.log(
          "RabbitMQ process started with PID:",
          this.rabbitmqProcess.pid
        );
        let startupTimeout;
        let isStarted = false;
        const checkStartup = (data) => {
          const output = data.toString();
          console.log("RabbitMQ output:", output);
          if (output.includes("completed with") || output.includes("started") || output.includes("RabbitMQ") && output.includes("running") || output.includes("broker running") || output.includes("Starting broker")) {
            isStarted = true;
            clearTimeout(startupTimeout);
            console.log("RabbitMQ service started successfully");
            resolve(true);
          }
          if (output.includes("error") || output.includes("failed") || output.includes("could not start")) {
            console.error("RabbitMQ startup error detected:", output);
            clearTimeout(startupTimeout);
            resolve(false);
          }
        };
        if (this.rabbitmqProcess.stdout) {
          this.rabbitmqProcess.stdout.on("data", checkStartup);
        }
        if (this.rabbitmqProcess.stderr) {
          this.rabbitmqProcess.stderr.on("data", (data) => {
            const errorOutput = data.toString();
            console.error("RabbitMQ error:", errorOutput);
            checkStartup(data);
          });
        }
        this.rabbitmqProcess.on("error", (err) => {
          console.error("Failed to start RabbitMQ service:", err);
          this.rabbitmqProcess = null;
          if (!isStarted) {
            clearTimeout(startupTimeout);
            resolve(false);
          }
        });
        this.rabbitmqProcess.on("close", (code) => {
          console.log(`RabbitMQ service exited with code ${code}`);
          this.rabbitmqProcess = null;
          if (!isStarted && !this.isShuttingDown) {
            clearTimeout(startupTimeout);
            resolve(false);
          }
        });
        startupTimeout = setTimeout(() => {
          if (!isStarted) {
            console.error("RabbitMQ service startup timeout");
            resolve(false);
          }
        }, 6e4);
      } catch (error) {
        console.error("Error starting RabbitMQ service:", error);
        this.rabbitmqProcess = null;
        resolve(false);
      }
    });
  }
  /**
   * 终止RabbitMQ服务
   */
  async terminateRabbitMQ() {
    return new Promise((resolve) => {
      if (this.rabbitmqProcess === null) {
        console.log("RabbitMQ process is already null");
        resolve();
        return;
      }
      console.log("Terminating RabbitMQ service...");
      try {
        if (process.platform === "win32" && this.rabbitmqProcess.pid) {
          node_child_process.exec(`taskkill /F /T /PID ${this.rabbitmqProcess.pid}`, (err) => {
            if (err) {
              console.error("Error terminating RabbitMQ process:", err);
            } else {
              console.log("RabbitMQ service terminated successfully.");
            }
            this.rabbitmqProcess = null;
            resolve();
          });
        } else {
          this.rabbitmqProcess.kill();
          console.log("RabbitMQ service terminated successfully.");
          this.rabbitmqProcess = null;
          resolve();
        }
      } catch (error) {
        console.error("Error terminating RabbitMQ service:", error);
        this.rabbitmqProcess = null;
        resolve();
      }
    });
  }
  /**
   * 终止所有后端进程
   */
  async terminateAllBackendProcesses() {
    console.log("Terminating all backend processes...");
    if (this.backendProcess !== null && this.backendPID !== null) {
      try {
        await this.killProcess(this.backendPID);
      } catch (error) {
        console.error(
          `Error terminating current backend process ${this.backendPID}:`,
          error
        );
      }
      this.backendProcess = null;
      this.backendPID = null;
    }
    const processes = [...this.runningBackendProcesses];
    for (const pid of processes) {
      await this.killProcess(pid);
    }
    const remainingPids = await checkExistingBackendProcesses();
    for (const pid of remainingPids) {
      await this.killProcess(pid);
    }
  }
  /**
   * 终止所有RabbitMQ相关进程
   */
  async terminateAllRabbitMQProcesses() {
    console.log("Terminating all RabbitMQ processes...");
    if (this.rabbitmqProcess !== null && this.rabbitmqProcess.pid) {
      try {
        console.log(
          `Terminating current RabbitMQ process with PID: ${this.rabbitmqProcess.pid}`
        );
        await this.terminateRabbitMQ();
      } catch (error) {
        console.error(`Error terminating current RabbitMQ process:`, error);
      }
      this.rabbitmqProcess = null;
    }
    try {
      const remainingPids = await checkExistingRabbitMQProcesses();
      console.log(
        `Found ${remainingPids.length} remaining RabbitMQ processes to terminate`
      );
      for (const pid of remainingPids) {
        await this.killProcess(pid);
      }
    } catch (error) {
      console.error(`Error checking for remaining RabbitMQ processes:`, error);
    }
  }
  /**
   * 设置关闭标志
   */
  setShuttingDown(value) {
    this.isShuttingDown = value;
  }
  /**
   * 获取后端进程状态
   */
  getBackendStatus() {
    return {
      process: this.backendProcess,
      pid: this.backendPID,
      runningProcesses: [...this.runningBackendProcesses]
    };
  }
  /**
   * 获取RabbitMQ进程状态
   */
  getRabbitMQStatus() {
    var _a;
    return {
      process: this.rabbitmqProcess,
      pid: ((_a = this.rabbitmqProcess) == null ? void 0 : _a.pid) || null
    };
  }
}
class WindowManager {
  constructor() {
    __publicField(this, "mainWindow", null);
    __publicField(this, "loadingWindow", null);
    __publicField(this, "allWindows", /* @__PURE__ */ new Map());
    __publicField(this, "taskWindowMap", /* @__PURE__ */ new Map());
    __publicField(this, "preFetchedResults", /* @__PURE__ */ new Map());
    __publicField(this, "pendingModelCompleteEvents", /* @__PURE__ */ new Map());
    __publicField(this, "preload", node_path.join(__dirname, "../preload/index.js"));
    __publicField(this, "url", process.env.VITE_DEV_SERVER_URL);
    __publicField(this, "indexHtml", node_path.join(process.env.DIST, "index.html"));
  }
  /**
   * 创建闪屏窗口
   */
  createLoadingWindow() {
    const loadingPreload = node_path.join(__dirname, "../preload/loading.js");
    this.loadingWindow = new electron.BrowserWindow({
      width: 450,
      height: 300,
      icon: node_path.join(process.env.PUBLIC, "favicon.ico"),
      frame: false,
      transparent: true,
      resizable: false,
      webPreferences: {
        preload: loadingPreload,
        contextIsolation: true,
        nodeIntegration: false
      }
    });
    const loadingHtmlPath = node_path.join(process.env.PUBLIC, "loading.html");
    this.loadingWindow.loadFile(loadingHtmlPath);
    this.loadingWindow.on("closed", () => {
      this.loadingWindow = null;
    });
    return this.loadingWindow;
  }
  /**
   * 创建主窗口
   */
  async createMainWindow(initialRoute) {
    this.mainWindow = new electron.BrowserWindow({
      show: false,
      width: 1024,
      height: 768,
      minWidth: 1024,
      minHeight: 768,
      title: "ML Desktop",
      icon: node_path.join(process.env.PUBLIC, "favicon.ico"),
      frame: false,
      transparent: true,
      resizable: true,
      webPreferences: {
        preload: this.preload,
        nodeIntegration: false,
        contextIsolation: true
      }
    });
    const targetUrl = initialRoute ? `${this.url}#${initialRoute}` : this.url;
    const targetIndexHtml = initialRoute ? { pathname: this.indexHtml, hash: initialRoute } : this.indexHtml;
    if (process.env.VITE_DEV_SERVER_URL) {
      this.mainWindow.loadURL(targetUrl);
      this.mainWindow.webContents.openDevTools({ mode: "bottom" });
    } else {
      this.mainWindow.loadFile(
        typeof targetIndexHtml === "string" ? targetIndexHtml : targetIndexHtml.pathname,
        typeof targetIndexHtml === "string" ? {} : { hash: targetIndexHtml.hash }
      );
    }
    this.mainWindow.webContents.setWindowOpenHandler(({ url }) => {
      const isModelResultWindow = url.includes("/modelManagement/");
      const childWindow = new electron.BrowserWindow({
        width: 1024,
        height: 768,
        minWidth: 1024,
        minHeight: 768,
        autoHideMenuBar: true,
        frame: !isModelResultWindow,
        transparent: isModelResultWindow,
        backgroundColor: isModelResultWindow ? "#00000000" : "#fff",
        icon: node_path.join(process.env.PUBLIC, "favicon.ico"),
        webPreferences: {
          preload: this.preload,
          nodeIntegration: false,
          contextIsolation: true
        },
        ...isModelResultWindow ? {
          show: false
        } : {}
      });
      childWindow.loadURL(url);
      this.setupDragOptimization(childWindow);
      if (isModelResultWindow) {
        childWindow.once("ready-to-show", () => {
          childWindow.show();
        });
      }
      return { action: "deny" };
    });
    this.setupDragOptimization(this.mainWindow);
    this.setupWindowStateEvents(this.mainWindow);
    return this.mainWindow;
  }
  /**
   * 创建子窗口
   */
  createChildWindow(url) {
    const childWindow = new electron.BrowserWindow({
      icon: node_path.join(process.env.PUBLIC, "favicon.ico"),
      webPreferences: {
        preload: this.preload,
        nodeIntegration: false,
        contextIsolation: true
      }
    });
    if (process.env.VITE_DEV_SERVER_URL) {
      childWindow.loadURL(`${this.url}#${url}`);
    } else {
      childWindow.loadFile(this.indexHtml, { hash: url });
    }
    this.setupDragOptimization(childWindow);
    return childWindow;
  }
  /**
   * 获取主窗口
   */
  getMainWindow() {
    return this.mainWindow;
  }
  /**
   * 获取加载窗口
   */
  getLoadingWindow() {
    return this.loadingWindow;
  }
  /**
   * 关闭加载窗口
   */
  closeLoadingWindow() {
    if (this.loadingWindow && !this.loadingWindow.isDestroyed()) {
      this.loadingWindow.close();
      this.loadingWindow = null;
    }
  }
  /**
   * 显示主窗口
   */
  showMainWindow() {
    if (this.mainWindow && !this.mainWindow.isDestroyed()) {
      this.mainWindow.show();
      this.mainWindow.maximize();
    }
  }
  /**
   * 注册任务窗口映射
   */
  registerTaskWindow(uid, window) {
    this.taskWindowMap.set(uid, window);
  }
  /**
   * 获取任务窗口
   */
  getTaskWindow(uid) {
    return this.taskWindowMap.get(uid);
  }
  /**
   * 缓存预取结果
   */
  cachePreFetchedResult(uid, result) {
    this.preFetchedResults.set(uid, result);
  }
  /**
   * 获取预取结果
   */
  getPreFetchedResult(uid) {
    return this.preFetchedResults.get(uid);
  }
  /**
   * 删除预取结果
   */
  deletePreFetchedResult(uid) {
    this.preFetchedResults.delete(uid);
  }
  /**
   * 缓存待处理的模型完成事件
   */
  cachePendingModelCompleteEvent(uid, data) {
    this.pendingModelCompleteEvents.set(uid, data);
  }
  /**
   * 获取待处理的模型完成事件
   */
  getPendingModelCompleteEvent(uid) {
    return this.pendingModelCompleteEvents.get(uid);
  }
  /**
   * 删除待处理的模型完成事件
   */
  deletePendingModelCompleteEvent(uid) {
    this.pendingModelCompleteEvents.delete(uid);
  }
  /**
   * 窗口控制 - 最小化
   */
  minimizeWindow(window) {
    const targetWindow = window || this.mainWindow;
    if (targetWindow && !targetWindow.isDestroyed()) {
      targetWindow.minimize();
    }
  }
  /**
   * 窗口控制 - 最大化/还原
   */
  toggleMaximizeWindow(window) {
    const targetWindow = window || this.mainWindow;
    if (targetWindow && !targetWindow.isDestroyed()) {
      if (targetWindow.isMaximized()) {
        targetWindow.unmaximize();
      } else {
        targetWindow.maximize();
      }
    }
  }
  /**
   * 窗口控制 - 关闭
   */
  closeWindow(window) {
    const targetWindow = window || this.mainWindow;
    if (targetWindow && !targetWindow.isDestroyed()) {
      targetWindow.close();
    }
  }
  /**
   * 设置窗口拖拽优化逻辑
   * 当最大化窗口被拖拽时，先还原窗口大小再移动
   */
  setupDragOptimization(window) {
    if (!window || window.isDestroyed()) return;
    let isProcessingMove = false;
    window.on("will-move", (event) => {
      if (window.isMaximized() && !isProcessingMove) {
        isProcessingMove = true;
        event.preventDefault();
        window.unmaximize();
        process.nextTick(() => {
          isProcessingMove = false;
        });
      }
    });
    window.on("unmaximize", () => {
      isProcessingMove = false;
    });
  }
  /**
   * 设置窗口状态事件监听
   * 向渲染进程发送窗口状态变化事件
   */
  setupWindowStateEvents(window) {
    if (!window || window.isDestroyed()) return;
    window.on("maximize", () => {
      window.webContents.send("window-maximized");
    });
    window.on("unmaximize", () => {
      window.webContents.send("window-unmaximized");
    });
    window.on("restore", () => {
      window.webContents.send("window-restored");
    });
  }
  /**
   * 清理资源
   */
  cleanup() {
    this.allWindows.clear();
    this.taskWindowMap.clear();
    this.preFetchedResults.clear();
    this.pendingModelCompleteEvents.clear();
  }
}
const mammoth = require("mammoth");
const _FileSystemHandler = class _FileSystemHandler {
  constructor() {
    // 文件类型定义
    __publicField(this, "fileTypes", {
      excel: [".xlsx", ".xls", ".csv", ".xlsm", ".xlsb"],
      markdown: [".md", ".markdown"],
      text: [".txt", ".log"],
      code: [".ts", ".js", ".py", ".json", ".html", ".css"]
    });
    // 文件过滤器
    __publicField(this, "filters", {
      excel: [
        {
          name: "Excel文件",
          extensions: ["xlsx", "xls", "csv", "xlsm", "xlsb"]
        }
      ],
      markdown: [
        {
          name: "Markdown文件",
          extensions: ["md", "markdown"]
        }
      ],
      text: [
        {
          name: "文本文件",
          extensions: ["txt", "log"]
        }
      ],
      code: [
        {
          name: "代码文件",
          extensions: ["ts", "js", "py", "json", "html", "css"]
        }
      ],
      all: [
        {
          name: "所有文件",
          extensions: ["*"]
        }
      ]
    });
  }
  static getInstance() {
    if (!_FileSystemHandler.instance) {
      _FileSystemHandler.instance = new _FileSystemHandler();
    }
    return _FileSystemHandler.instance;
  }
  /**
   * 检测文件类型
   */
  getFileType(filePath) {
    const ext = path.extname(filePath).toLowerCase();
    const textExtensions = [
      ".txt",
      ".md",
      ".json",
      ".xml",
      ".html",
      ".css",
      ".js",
      ".ts",
      ".vue",
      ".py",
      ".java",
      ".cpp",
      ".c",
      ".h",
      ".sql",
      ".log",
      ".ini",
      ".cfg",
      ".conf",
      ".yaml",
      ".yml"
    ];
    const officeExtensions = [
      ".doc",
      ".docx",
      ".xls",
      ".xlsx",
      ".ppt",
      ".pptx"
    ];
    const pdfExtensions = [".pdf"];
    const imageExtensions = [
      ".jpg",
      ".jpeg",
      ".png",
      ".gif",
      ".bmp",
      ".svg",
      ".ico"
    ];
    const archiveExtensions = [".zip", ".rar", ".7z", ".tar", ".gz"];
    const executableExtensions = [
      ".exe",
      ".msi",
      ".dmg",
      ".app",
      ".deb",
      ".rpm"
    ];
    const mediaExtensions = [".mp3", ".mp4", ".avi", ".mov", ".wav", ".flac"];
    if (textExtensions.includes(ext)) {
      return { type: ext, category: "text", supported: true };
    } else if (officeExtensions.includes(ext)) {
      return { type: ext, category: "office", supported: true };
    } else if (pdfExtensions.includes(ext)) {
      return { type: ext, category: "pdf", supported: true };
    } else if (imageExtensions.includes(ext)) {
      return { type: ext, category: "image", supported: true };
    } else if (archiveExtensions.includes(ext)) {
      return { type: ext, category: "archive", supported: false };
    } else if (executableExtensions.includes(ext)) {
      return { type: ext, category: "executable", supported: false };
    } else if (mediaExtensions.includes(ext)) {
      return { type: ext, category: "media", supported: false };
    } else {
      return { type: ext, category: "unknown", supported: false };
    }
  }
  /**
   * 获取图像文件的 MIME 类型
   */
  getMimeType(extension) {
    const mimeTypes = {
      ".jpg": "image/jpeg",
      ".jpeg": "image/jpeg",
      ".png": "image/png",
      ".gif": "image/gif",
      ".bmp": "image/bmp",
      ".svg": "image/svg+xml",
      ".ico": "image/x-icon",
      ".webp": "image/webp"
    };
    return mimeTypes[extension.toLowerCase()] || "image/jpeg";
  }
  /**
   * 获取不支持文件类型的提示信息
   */
  getUnsupportedMessage(fileInfo) {
    switch (fileInfo.category) {
      case "image":
        return "图片文件不支持文本编辑，请使用图片查看器打开";
      case "archive":
        return "压缩文件不支持直接编辑，请先解压缩";
      case "executable":
        return "可执行文件不支持编辑";
      case "media":
        return "音视频文件不支持文本编辑，请使用媒体播放器打开";
      default:
        return "不支持的文件类型，无法在文本编辑器中打开";
    }
  }
  /**
   * 提取 DOCX 文档的文本内容
   */
  async extractDocxText(filePath) {
    try {
      try {
        const result = await mammoth.extractRawText({ path: filePath });
        return result.value || "无法提取文档内容";
      } catch {
        console.log("Mammoth not available");
        return `Word 文档预览

文件路径: ${filePath}

注意：无法提取文档内容，请使用 Microsoft Word 或其他兼容软件打开此文件。

要完整支持 Word 文档，请安装 mammoth 库：npm install mammoth`;
      }
    } catch (error) {
      console.error("Error extracting DOCX text:", error);
      return `Word 文档预览

文件路径: ${filePath}

错误：无法读取文档内容 - ${(error == null ? void 0 : error.message) || "Unknown error"}`;
    }
  }
  /**
   * 读取目录内容
   */
  async readDirectory(dirPath) {
    const files = await fs.promises.readdir(dirPath, { withFileTypes: true });
    return files.map((dirent) => ({
      name: dirent.name,
      isDirectory: dirent.isDirectory(),
      path: path.join(dirPath, dirent.name)
    }));
  }
  /**
   * 创建新目录
   */
  async createDirectory(targetPath) {
    await fs.promises.mkdir(targetPath, { recursive: true });
    return { success: true };
  }
  /**
   * 创建新文件
   */
  async createFile(filePath) {
    await fs.promises.writeFile(filePath, "");
    return { success: true };
  }
  /**
   * 删除文件/目录
   */
  async deletePath(targetPath) {
    const stats = await fs.promises.stat(targetPath);
    if (stats.isDirectory()) {
      await fs.promises.rmdir(targetPath, { recursive: true });
    } else {
      await fs.promises.unlink(targetPath);
    }
    return { success: true };
  }
  /**
   * 重命名文件/目录
   */
  async rename(oldPath, newPath) {
    try {
      await fs.promises.rename(oldPath, newPath);
      return { success: true };
    } catch (error) {
      console.error("Error renaming file/directory:", error);
      throw error;
    }
  }
  /**
   * 检查文件/目录是否存在
   */
  async access(filePath) {
    try {
      await fs.promises.access(filePath);
      return { exists: true };
    } catch {
      return { exists: false };
    }
  }
  /**
   * 读取文件内容
   */
  async readFile(filePath) {
    try {
      const content = await fs.promises.readFile(filePath, "utf-8");
      return content;
    } catch (error) {
      console.error("Error reading file:", error);
      throw error;
    }
  }
  /**
   * 检测文件类型和读取内容
   */
  async readFileWithType(filePath) {
    try {
      const fileInfo = this.getFileType(filePath);
      if (!fileInfo.supported) {
        return {
          success: false,
          fileInfo,
          error: `不支持的文件类型: ${fileInfo.type}`,
          message: this.getUnsupportedMessage(fileInfo)
        };
      }
      let content = "";
      let imageData = null;
      let rawData = null;
      if (fileInfo.category === "text") {
        content = await fs.promises.readFile(filePath, "utf-8");
      } else if (fileInfo.category === "office") {
        if (fileInfo.type === ".docx") {
          content = await this.extractDocxText(filePath);
        } else if (fileInfo.type === ".doc") {
          content = "暂不支持 .doc 格式，请转换为 .docx 格式";
        } else {
          content = `不支持的Office 文档 (${fileInfo.type})，请使用Office工具进行编辑`;
        }
      } else if (fileInfo.category === "pdf") {
        rawData = await fs.promises.readFile(filePath);
        content = "";
      } else if (fileInfo.category === "image") {
        const imageBuffer = await fs.promises.readFile(filePath);
        const base64Data = imageBuffer.toString("base64");
        const mimeType = this.getMimeType(fileInfo.type);
        imageData = `data:${mimeType};base64,${base64Data}`;
        content = "";
      }
      return {
        success: true,
        fileInfo,
        content,
        imageData,
        rawData
      };
    } catch (error) {
      console.error("Error reading file with type:", error);
      return {
        success: false,
        fileInfo: this.getFileType(filePath),
        error: (error == null ? void 0 : error.message) || "Unknown error",
        message: "读取文件时发生错误"
      };
    }
  }
  /**
   * 读取文件内容为Buffer
   */
  async readFileBuffer(filePath) {
    try {
      const buffer = await fs.promises.readFile(filePath);
      return buffer;
    } catch (error) {
      console.error("Error reading file buffer:", error);
      throw error;
    }
  }
  /**
   * 写入文件内容
   */
  async writeFile(filePath, content) {
    try {
      await fs.promises.writeFile(filePath, content, "utf-8");
      return { success: true };
    } catch (error) {
      console.error("Error writing file:", error);
      throw error;
    }
  }
  /**
   * 写入文件内容 (Buffer)
   */
  async writeFileBuffer(filePath, arrayBuffer) {
    try {
      const buffer = Buffer.from(arrayBuffer);
      await fs.promises.writeFile(filePath, buffer);
      return { success: true };
    } catch (error) {
      console.error("Error writing file buffer:", error);
      throw error;
    }
  }
  /**
   * 写入文件内容 (Base64Buffer)
   */
  async writeFileBase64Buffer(filePath, arrayBuffer) {
    try {
      const buffer = Buffer.from(arrayBuffer, "base64");
      await fs.promises.writeFile(filePath, buffer);
      return { success: true };
    } catch (error) {
      console.error("Error writing file buffer:", error);
      throw error;
    }
  }
  /**
   * 验证文件路径是否存在
   */
  async validatePath(filePath) {
    try {
      const exists = await fs.promises.access(filePath).then(() => true).catch(() => false);
      return { exists, path: filePath };
    } catch (error) {
      return { exists: false, path: filePath, error };
    }
  }
  /**
   * 获取最近文件路径
   */
  getRecentFilesPath() {
    return path.join(electron.app.getPath("userData"), "recent-files.json");
  }
  /**
   * 获取最近文件
   */
  async getRecentFiles() {
    try {
      const recentFilesPath = this.getRecentFilesPath();
      const exists = await fs.promises.access(recentFilesPath).then(() => true).catch(() => false);
      if (!exists) {
        return { success: true, files: [] };
      }
      const fileContent = await fs.promises.readFile(recentFilesPath, "utf-8");
      const files = JSON.parse(fileContent);
      return { success: true, files: Array.isArray(files) ? files : [] };
    } catch (error) {
      console.error("获取最近文件失败:", error);
      return { success: false, files: [] };
    }
  }
  /**
   * 保存最近文件
   */
  async saveRecentFiles(files) {
    try {
      const recentFilesPath = this.getRecentFilesPath();
      await fs.promises.writeFile(
        recentFilesPath,
        JSON.stringify(files, null, 2),
        "utf-8"
      );
      return { success: true };
    } catch (error) {
      console.error("保存最近文件失败:", error);
      return { success: false, message: "保存失败" };
    }
  }
  /**
   * 创建文件信息对象
   */
  createFileInfo(filePath) {
    return {
      path: filePath,
      name: path.basename(filePath),
      extension: path.extname(filePath).toLowerCase(),
      type: this.getFileTypeCategory(filePath),
      exists: fs.existsSync(filePath)
    };
  }
  /**
   * 获取文件类型分类
   */
  getFileTypeCategory(filePath) {
    const ext = path.extname(filePath).toLowerCase();
    for (const [type, extensions] of Object.entries(this.fileTypes)) {
      if (extensions.includes(ext)) {
        return type;
      }
    }
    return "other";
  }
  /**
   * 打开文件对话框
   */
  async openFile(window, options = {}) {
    try {
      const { canceled, filePaths } = await electron.dialog.showOpenDialog(window, {
        properties: ["openFile"],
        filters: options.filters || this.filters.all
      });
      if (!canceled && filePaths.length > 0) {
        const fileInfo = this.createFileInfo(filePaths[0]);
        if (options.isDataImport && !this.fileTypes.excel.includes(fileInfo.extension)) {
          await electron.dialog.showMessageBox(window, {
            type: "error",
            title: "文件类型错误",
            message: "请选择Excel文件"
          });
          return null;
        }
        window.webContents.send("file-selected", fileInfo);
        return fileInfo;
      }
    } catch (error) {
      console.error("Error opening file:", error);
      electron.dialog.showErrorBox("错误", "打开文件时发生错误");
    }
    return null;
  }
  /**
   * 导入项目
   */
  async importProject(window) {
    try {
      const { canceled, filePaths } = await electron.dialog.showOpenDialog(window, {
        properties: ["openDirectory"],
        title: "选择项目文件夹"
      });
      if (!canceled && filePaths.length > 0) {
        const projectPath = filePaths[0];
        const projectInfo = {
          path: projectPath,
          name: path.basename(projectPath)
        };
        window.webContents.send("project-imported", projectInfo);
        return projectInfo;
      }
    } catch (error) {
      console.error("Error importing project:", error);
      electron.dialog.showErrorBox("错误", "导入项目时发生错误");
    }
    return null;
  }
  /**
   * 处理文件菜单的打开文件操作
   */
  async handleOpenFile(win) {
    if (!win || win.isDestroyed()) return;
    const currentURL = win.webContents.getURL();
    const isDataImportPage = currentURL.includes("/dataManagement/imandex");
    await this.openFile(win, {
      filters: isDataImportPage ? this.filters.excel : this.filters.all,
      isDataImport: isDataImportPage
    });
  }
  /**
   * 处理导入项目操作
   */
  async handleImportProject(win) {
    if (!win || win.isDestroyed()) return;
    await this.importProject(win);
  }
  /**
   * 获取目录大小信息
   */
  async getDirectorySize(dirPath) {
    try {
      let totalSize = 0;
      let fileCount = 0;
      const calculateSize = async (currentPath) => {
        const stats = await fs.promises.stat(currentPath);
        if (stats.isFile()) {
          totalSize += stats.size;
          fileCount++;
        } else if (stats.isDirectory()) {
          const entries = await fs.promises.readdir(currentPath);
          for (const entry of entries) {
            const entryPath = path.join(currentPath, entry);
            await calculateSize(entryPath);
          }
        }
      };
      await calculateSize(dirPath);
      return { size: totalSize, fileCount };
    } catch (error) {
      console.error("Error calculating directory size:", error);
      return { size: 0, fileCount: 0 };
    }
  }
  /**
   * 递归读取目录结构
   */
  async readDirectoryRecursive(dirPath) {
    try {
      const files = [];
      const readRecursive = async (currentPath, relativePath = "") => {
        const entries = await fs.promises.readdir(currentPath, {
          withFileTypes: true
        });
        for (const entry of entries) {
          const fullPath = path.join(currentPath, entry.name);
          const relPath = relativePath ? path.join(relativePath, entry.name) : entry.name;
          if (entry.isFile()) {
            files.push({
              relativePath: relPath,
              fullPath,
              isFile: true
            });
          } else if (entry.isDirectory()) {
            files.push({
              relativePath: relPath,
              fullPath,
              isFile: false
            });
            await readRecursive(fullPath, relPath);
          }
        }
      };
      await readRecursive(dirPath);
      return { files };
    } catch (error) {
      console.error("Error reading directory recursively:", error);
      return { files: [] };
    }
  }
  /**
   * 检查路径是否存在
   */
  async pathExists(filePath) {
    try {
      await fs.promises.access(filePath);
      return true;
    } catch {
      return false;
    }
  }
};
__publicField(_FileSystemHandler, "instance");
let FileSystemHandler = _FileSystemHandler;
class DialogHandler {
  /**
   * 选择目录对话框
   */
  async openDirectory() {
    const result = await electron.dialog.showOpenDialog({
      properties: ["openDirectory"]
    });
    return result.filePaths[0];
  }
  /**
   * 选择文件对话框
   */
  async openFile() {
    const result = await electron.dialog.showOpenDialog({
      properties: ["openFile"]
    });
    if (result.canceled || result.filePaths.length === 0) {
      return null;
    }
    return result.filePaths[0];
  }
  /**
   * 选择保存文件对话框
   */
  async saveFile(options) {
    const result = await electron.dialog.showSaveDialog({
      ...options,
      filters: [
        { name: "JSON Files", extensions: ["json"] },
        { name: "All Files", extensions: ["*"] }
      ]
    });
    return result;
  }
  /**
   * 项目导出对话框
   */
  async exportProject(projectData) {
    try {
      const result = await electron.dialog.showSaveDialog({
        title: "导出项目",
        defaultPath: `project_${(/* @__PURE__ */ new Date()).toISOString().slice(0, 19).replace(/:/g, "-")}.json`,
        filters: [
          { name: "JSON Files", extensions: ["json"] },
          { name: "All Files", extensions: ["*"] }
        ]
      });
      if (result.canceled || !result.filePath) {
        return { success: false, message: "用户取消导出" };
      }
      await fs.promises.writeFile(
        result.filePath,
        JSON.stringify(projectData, null, 2),
        "utf-8"
      );
      return {
        success: true,
        filePath: result.filePath,
        message: "项目导出成功"
      };
    } catch (error) {
      console.error("导出项目失败:", error);
      return { success: false, message: `导出失败` };
    }
  }
  /**
   * 项目导入对话框
   */
  async importProject() {
    try {
      const result = await electron.dialog.showOpenDialog({
        title: "导入项目",
        properties: ["openFile"],
        filters: [
          { name: "项目文件", extensions: ["zip"] },
          { name: "All Files", extensions: ["*"] }
        ]
      });
      if (result.canceled || result.filePaths.length === 0) {
        return { success: false, message: "用户取消导入" };
      }
      const filePath = result.filePaths[0];
      const fileBuffer = await fs.promises.readFile(filePath);
      return {
        success: true,
        fileBuffer,
        filePath,
        message: "项目文件读取成功"
      };
    } catch (error) {
      console.error("导入项目失败:", error);
      return { success: false, message: `导入失败` };
    }
  }
  /**
   * 选择新的文件路径
   */
  async selectNewPath(options) {
    const result = await electron.dialog.showOpenDialog({
      title: options.title || "选择新路径",
      properties: options.properties || ["openDirectory"],
      defaultPath: options.defaultPath || ""
    });
    if (result.canceled || result.filePaths.length === 0) {
      return null;
    }
    return result.filePaths[0];
  }
  /**
   * 显示打开对话框 (带窗口上下文)
   */
  async showOpenDialog(window, options) {
    if (!window) {
      return { canceled: true, filePaths: [] };
    }
    return await electron.dialog.showOpenDialog(window, options);
  }
  /**
   * 显示保存对话框 (带窗口上下文)
   */
  async showSaveDialog(window, options) {
    if (!window) {
      return { canceled: true, filePath: "" };
    }
    return await electron.dialog.showSaveDialog(window, options);
  }
  /**
   * 显示消息框
   */
  async showMessageBox(window, options) {
    if (!window) {
      return { response: 0 };
    }
    return await electron.dialog.showMessageBox(window, options);
  }
  /**
   * 显示错误对话框
   */
  showErrorBox(title, content) {
    electron.dialog.showErrorBox(title, content);
  }
}
class MenuManager {
  constructor(isDev2 = false) {
    __publicField(this, "isDev");
    this.isDev = isDev2;
  }
  /**
   * 创建应用菜单
   */
  createMenu(label = "进入全屏幕") {
    const menu = electron.Menu.buildFromTemplate(
      this.buildAppMenu(label)
    );
    electron.Menu.setApplicationMenu(menu);
  }
  /**
   * 构建应用菜单模板
   */
  buildAppMenu(fullscreenLabel) {
    const devMenuItems = [];
    if (this.isDev) {
      devMenuItems.push(
        { label: "开发者工具", role: "toggleDevTools" },
        { label: "强制刷新", role: "forceReload" }
      );
    }
    const template = [
      {
        label: "文件",
        submenu: [
          {
            label: "打开文件夹...",
            accelerator: "CmdOrCtrl+Shift+O",
            toolTip: "打开现有项目文件夹",
            click: async () => {
              const win = electron.BrowserWindow.getFocusedWindow();
              if (win && !win.isDestroyed()) {
                win.focus();
                win.webContents.send("menu-triggered-import-project");
              }
            }
          },
          {
            label: "打开文件...",
            accelerator: "CmdOrCtrl+O",
            toolTip: "打开单个文件进行编辑",
            click: async () => {
              const win = electron.BrowserWindow.getFocusedWindow();
              if (win && !win.isDestroyed()) {
                win.focus();
                const currentURL = win.webContents.getURL();
                const isDataImportPage = currentURL.includes(
                  "/dataManagement/imandex"
                );
                if (isDataImportPage) {
                  win.webContents.send("menu-triggered-open-file");
                } else {
                  win.webContents.send("menu-triggered-open-file");
                }
              }
            }
          },
          { type: "separator" },
          {
            label: "项目管理",
            submenu: [
              {
                label: "导出项目",
                accelerator: "CmdOrCtrl+Shift+E",
                toolTip: "导出当前项目状态，包括标签页、工作区等信息",
                click: async () => {
                  const win = electron.BrowserWindow.getFocusedWindow();
                  if (win && !win.isDestroyed()) {
                    win.focus();
                    win.webContents.send("menu-triggered-export-project");
                  }
                }
              },
              {
                label: "导入项目",
                accelerator: "CmdOrCtrl+Shift+I",
                toolTip: "导入项目状态，恢复之前的工作环境",
                click: async () => {
                  const win = electron.BrowserWindow.getFocusedWindow();
                  if (win && !win.isDestroyed()) {
                    win.focus();
                    win.webContents.send("menu-triggered-import-project-state");
                  }
                }
              }
            ]
          },
          { type: "separator" },
          {
            label: "退出",
            role: "quit",
            accelerator: "CmdOrCtrl+Q"
          }
        ]
      },
      {
        label: "编辑",
        submenu: [
          {
            label: "撤销",
            role: "undo",
            accelerator: "CmdOrCtrl+Z"
          },
          {
            label: "重做",
            role: "redo",
            accelerator: "CmdOrCtrl+Shift+Z"
          },
          { type: "separator" },
          {
            label: "剪切",
            role: "cut",
            accelerator: "CmdOrCtrl+X"
          },
          {
            label: "复制",
            role: "copy",
            accelerator: "CmdOrCtrl+C"
          },
          {
            label: "粘贴",
            role: "paste",
            accelerator: "CmdOrCtrl+V"
          },
          {
            label: "删除",
            role: "delete",
            accelerator: "Delete"
          },
          {
            label: "全选",
            role: "selectAll",
            accelerator: "CmdOrCtrl+A"
          }
        ]
      },
      {
        label: "显示",
        submenu: [
          {
            label: "放大",
            role: "zoomIn",
            accelerator: "CmdOrCtrl+Plus"
          },
          {
            label: "默认大小",
            role: "resetZoom",
            accelerator: "CmdOrCtrl+0"
          },
          {
            label: "缩小",
            role: "zoomOut",
            accelerator: "CmdOrCtrl+-"
          },
          { type: "separator" },
          {
            label: fullscreenLabel,
            role: "togglefullscreen",
            accelerator: "F11"
          }
        ]
      },
      ...this.isDev ? [
        {
          label: "开发",
          submenu: devMenuItems
        }
      ] : [],
      {
        label: "关于",
        submenu: [
          {
            label: "关于应用",
            role: "about",
            accelerator: "F1"
          }
        ]
      }
    ];
    return template;
  }
  /**
   * 创建上下文菜单 - 文件菜单
   */
  createFileMenu(win) {
    const fileMenu = electron.Menu.buildFromTemplate([
      {
        label: "打开文件夹...",
        click: () => {
          win == null ? void 0 : win.webContents.send("menu-triggered-import-project");
        }
      },
      {
        label: "打开文件...",
        click: () => {
          win == null ? void 0 : win.webContents.send("menu-triggered-open-file");
        }
      },
      { type: "separator" },
      {
        label: "退出",
        click: () => electron.app.quit()
      }
    ]);
    fileMenu.popup({ window: win });
  }
  /**
   * 创建上下文菜单 - 项目菜单
   */
  createProjectMenu(win) {
    const projectMenu = electron.Menu.buildFromTemplate([
      {
        label: "导入项目",
        click: () => {
          win == null ? void 0 : win.webContents.send("menu-triggered-import-project-state");
        }
      },
      {
        label: "导出项目",
        click: () => {
          win == null ? void 0 : win.webContents.send("menu-triggered-export-project");
        }
      }
    ]);
    projectMenu.popup({ window: win });
  }
  /**
   * 创建上下文菜单 - 编辑菜单
   */
  createEditMenu(win) {
    const editMenu = electron.Menu.buildFromTemplate([
      { label: "撤销", role: "undo" },
      { label: "重做", role: "redo" },
      { type: "separator" },
      { label: "剪切", role: "cut" },
      { label: "复制", role: "copy" },
      { label: "粘贴", role: "paste" },
      { type: "separator" },
      { label: "全选", role: "selectAll" }
    ]);
    editMenu.popup({ window: win });
  }
  /**
   * 创建上下文菜单 - 视图菜单
   */
  createViewMenu(win) {
    const viewMenu = electron.Menu.buildFromTemplate([
      { label: "放大", role: "zoomIn" },
      { label: "默认大小", role: "resetZoom" },
      { label: "缩小", role: "zoomOut" },
      { type: "separator" },
      {
        label: (win == null ? void 0 : win.isFullScreen()) ? "退出全屏" : "进入全屏",
        role: "togglefullscreen"
      }
    ]);
    viewMenu.popup({ window: win });
  }
  /**
   * 创建上下文菜单 - 开发菜单
   */
  createDevMenu(win) {
    if (!this.isDev) return;
    const devMenu = electron.Menu.buildFromTemplate([
      { label: "开发者工具", role: "toggleDevTools" },
      { label: "强制刷新", role: "forceReload" }
    ]);
    devMenu.popup({ window: win });
  }
  /**
   * 创建上下文菜单 - 关于菜单
   */
  createAboutMenu(win) {
    const aboutMenu = electron.Menu.buildFromTemplate([
      {
        label: "关于应用",
        click: () => {
          let version = "未知版本";
          try {
            const packagePath = path.join(electron.app.getAppPath(), "package.json");
            if (node_fs.existsSync(packagePath)) {
              const packageData = JSON.parse(node_fs.readFileSync(packagePath, "utf8"));
              version = packageData.version || "未知版本";
            }
          } catch (error) {
            console.error("读取版本号失败:", error);
          }
          electron.dialog.showMessageBox(win, {
            title: "关于应用",
            message: "ML Desktop",
            detail: `版本 ${version}
一个机器学习数据处理和建模的桌面应用。`,
            buttons: ["确定"],
            type: "info"
          });
        }
      }
    ]);
    aboutMenu.popup({ window: win });
  }
}
class IPCHandler {
  constructor(windowManager2, fileSystemHandler2, dialogHandler2, menuManager2) {
    __publicField(this, "windowManager");
    __publicField(this, "fileSystemHandler");
    __publicField(this, "dialogHandler");
    __publicField(this, "menuManager");
    this.windowManager = windowManager2;
    this.fileSystemHandler = fileSystemHandler2;
    this.dialogHandler = dialogHandler2;
    this.menuManager = menuManager2;
    this.setupIpcHandlers();
  }
  /**
   * 设置所有 IPC 处理器
   */
  setupIpcHandlers() {
    this.setupModelHandlers();
    this.setupWindowHandlers();
    this.setupFileSystemHandlers();
    this.setupDialogHandlers();
    this.setupMenuHandlers();
    this.setupUtilityHandlers();
  }
  /**
   * 设置模型相关的 IPC 处理器
   */
  setupModelHandlers() {
    electron.ipcMain.on("model_progress_update", (event, data) => {
      const uid = data.uid;
      const targetWindow = this.windowManager.getTaskWindow(uid);
      if (targetWindow && !targetWindow.isDestroyed()) {
        targetWindow.webContents.send("model_progress_update", data);
      }
    });
    electron.ipcMain.on("model_complete", (event, data) => {
      const uid = data.uid;
      const targetWindow = this.windowManager.getTaskWindow(uid);
      this.windowManager.cachePendingModelCompleteEvent(uid, data);
      if (targetWindow && !targetWindow.isDestroyed()) {
        targetWindow.webContents.send("model_complete", data);
      }
    });
    electron.ipcMain.on("cache_model_result", (event, { uid, result }) => {
      if (uid && result) {
        this.windowManager.cachePreFetchedResult(uid, result);
      }
    });
    electron.ipcMain.on("model_result_window_ready", (event, data) => {
      const uid = data.uid;
      if (!uid) return;
      const resultWindow = electron.BrowserWindow.fromWebContents(event.sender);
      if (resultWindow) {
        this.windowManager.registerTaskWindow(uid, resultWindow);
        const preFetchedResult = this.windowManager.getPreFetchedResult(uid);
        if (preFetchedResult) {
          resultWindow.webContents.send("model_complete", {
            uid,
            result: preFetchedResult
          });
          this.windowManager.deletePreFetchedResult(uid);
          return;
        }
        const pendingData = this.windowManager.getPendingModelCompleteEvent(uid);
        if (pendingData) {
          resultWindow.webContents.send("model_complete", pendingData);
          this.windowManager.deletePendingModelCompleteEvent(uid);
        }
      }
    });
    electron.ipcMain.on("request_model_info", async (event, data) => {
      const uid = data.uid;
      if (!uid) {
        event.sender.send("model_info_response", {
          code: 400,
          msg: "无效的任务ID"
        });
        return;
      }
      const resultWindow = electron.BrowserWindow.fromWebContents(event.sender);
      if (resultWindow) {
        this.windowManager.registerTaskWindow(uid, resultWindow);
      }
      const mainWindow = this.windowManager.getMainWindow();
      if (!mainWindow || mainWindow.isDestroyed()) {
        event.sender.send("model_info_response", {
          code: 500,
          msg: "主窗口不可用"
        });
        return;
      }
      try {
        const result = await mainWindow.webContents.executeJavaScript(`
          (function() {
            return new Promise((resolve) => {
              const socket = window.socketInstance;
              if (!socket || !socket.connected) {
                resolve({ code: 500, msg: 'WebSocket未连接' });
                return;
              }
              
              socket.emit('get_model_info', { uid: '${uid}' }, (response) => {
                resolve(response || { code: 404, msg: '未收到响应' });
              });
              
              setTimeout(() => {
                resolve({ code: 408, msg: '请求超时' });
              }, 10000);
            });
          })()
        `);
        event.sender.send("model_info_response", result);
      } catch (error) {
        event.sender.send("model_info_response", {
          code: 500,
          msg: "执行请求失败: " + (error.message || "未知错误")
        });
      }
    });
  }
  /**
   * 设置窗口相关的 IPC 处理器
   */
  setupWindowHandlers() {
    electron.ipcMain.handle("open-win", (_, arg) => {
      return this.windowManager.createChildWindow(arg);
    });
    electron.ipcMain.on("APP_READY_TO_SHOW_MAIN_WINDOW", (event, args = {}) => {
      this.windowManager.createMainWindow(args.targetRoute);
      if (args.openedFilePath) {
        const sendFileData = () => {
          var _a;
          const win2 = this.windowManager.getMainWindow();
          if ((_a = args.targetRoute) == null ? void 0 : _a.includes("/dataManagement/imandex")) {
            win2 == null ? void 0 : win2.webContents.send("excel-file-selected", args.openedFilePath);
          } else {
            win2 == null ? void 0 : win2.webContents.send(
              "workspace-file-selected",
              args.openedFilePath
            );
          }
          if (args.singleFileMode) {
            win2 == null ? void 0 : win2.webContents.send("set-single-file-mode", args.openedFilePath);
          }
        };
        const win = this.windowManager.getMainWindow();
        win == null ? void 0 : win.webContents.once("did-finish-load", sendFileData);
        win == null ? void 0 : win.webContents.once("dom-ready", sendFileData);
        setTimeout(sendFileData, 1e3);
      }
    });
    electron.ipcMain.on("minimize-window", () => {
      this.windowManager.minimizeWindow();
    });
    electron.ipcMain.on("maximize-window", () => {
      this.windowManager.toggleMaximizeWindow();
    });
    electron.ipcMain.on("close-window", () => {
      this.windowManager.closeWindow();
    });
    electron.ipcMain.on("minimize-current-window", (event) => {
      const currentWindow = electron.BrowserWindow.fromWebContents(event.sender);
      if (currentWindow) {
        this.windowManager.minimizeWindow(currentWindow);
      }
    });
    electron.ipcMain.on("maximize-current-window", (event) => {
      const currentWindow = electron.BrowserWindow.fromWebContents(event.sender);
      if (currentWindow) {
        this.windowManager.toggleMaximizeWindow(currentWindow);
      }
    });
    electron.ipcMain.on("close-current-window", (event) => {
      const currentWindow = electron.BrowserWindow.fromWebContents(event.sender);
      if (currentWindow) {
        this.windowManager.closeWindow(currentWindow);
      }
    });
    electron.ipcMain.on("workspace-file-selected", (event, filePath) => {
      const win = this.windowManager.getMainWindow();
      if (win && !win.isDestroyed()) {
        win.webContents.send("workspace-file-selected", filePath);
      }
    });
    electron.ipcMain.on("excel-file-selected", (event, filePath) => {
      const win = this.windowManager.getMainWindow();
      if (win && !win.isDestroyed()) {
        win.webContents.send("excel-file-selected", filePath);
      }
    });
    electron.ipcMain.on("unmaximize-window", () => {
      const mainWindow = this.windowManager.getMainWindow();
      if (mainWindow && !mainWindow.isDestroyed() && mainWindow.isMaximized()) {
        mainWindow.unmaximize();
      }
    });
    electron.ipcMain.on(
      "set-window-position",
      (event, position) => {
        const mainWindow = this.windowManager.getMainWindow();
        if (mainWindow && !mainWindow.isDestroyed()) {
          mainWindow.setPosition(position.x, position.y);
        }
      }
    );
    electron.ipcMain.handle("is-window-maximized", () => {
      const mainWindow = this.windowManager.getMainWindow();
      return mainWindow && !mainWindow.isDestroyed() ? mainWindow.isMaximized() : false;
    });
    electron.ipcMain.on("start-drag", (event) => {
      try {
        const window = electron.BrowserWindow.fromWebContents(event.sender);
        if (window && !window.isDestroyed()) {
          const hwndBuffer = window.getNativeWindowHandle();
          const windowId = process.platform === "linux" ? hwndBuffer.readUInt32LE(0) : hwndBuffer;
          dragAddon.startDrag(windowId);
        }
      } catch (error) {
        console.error("Failed to start drag:", error);
      }
    });
    electron.ipcMain.on("app-quit", () => {
      electron.app.quit();
    });
  }
  /**
   * 设置文件系统相关的 IPC 处理器
   */
  setupFileSystemHandlers() {
    electron.ipcMain.handle("fs:readDirectory", async (_, dirPath) => {
      return await this.fileSystemHandler.readDirectory(dirPath);
    });
    electron.ipcMain.handle("fs:createDirectory", async (_, targetPath) => {
      return await this.fileSystemHandler.createDirectory(targetPath);
    });
    electron.ipcMain.handle("fs:createFile", async (_, filePath) => {
      return await this.fileSystemHandler.createFile(filePath);
    });
    electron.ipcMain.handle("fs:deletePath", async (_, targetPath) => {
      return await this.fileSystemHandler.deletePath(targetPath);
    });
    electron.ipcMain.handle("fs:rename", async (_, oldPath, newPath) => {
      return await this.fileSystemHandler.rename(oldPath, newPath);
    });
    electron.ipcMain.handle("fs:access", async (_, filePath) => {
      return await this.fileSystemHandler.access(filePath);
    });
    electron.ipcMain.handle("fs:readFile", async (_, filePath) => {
      return await this.fileSystemHandler.readFile(filePath);
    });
    electron.ipcMain.handle("fs:readFileWithType", async (_, filePath) => {
      return await this.fileSystemHandler.readFileWithType(filePath);
    });
    electron.ipcMain.handle("fs:readFileBuffer", async (_, filePath) => {
      return await this.fileSystemHandler.readFileBuffer(filePath);
    });
    electron.ipcMain.handle(
      "fs:writeFile",
      async (_, filePath, content) => {
        return await this.fileSystemHandler.writeFile(filePath, content);
      }
    );
    electron.ipcMain.handle(
      "fs:writeFileBuffer",
      async (_, filePath, arrayBuffer) => {
        return await this.fileSystemHandler.writeFileBuffer(
          filePath,
          arrayBuffer
        );
      }
    );
    electron.ipcMain.handle(
      "fs:writeFileBase64Buffer",
      async (_, filePath, arrayBuffer) => {
        return await this.fileSystemHandler.writeFileBase64Buffer(
          filePath,
          arrayBuffer
        );
      }
    );
    electron.ipcMain.handle("fs:validatePath", async (_, filePath) => {
      return await this.fileSystemHandler.validatePath(filePath);
    });
    electron.ipcMain.handle("recentFiles:get", async () => {
      return await this.fileSystemHandler.getRecentFiles();
    });
    electron.ipcMain.handle("recentFiles:save", async (_, files) => {
      return await this.fileSystemHandler.saveRecentFiles(files);
    });
    electron.ipcMain.handle("fs:getDirectorySize", async (_, dirPath) => {
      return await this.fileSystemHandler.getDirectorySize(dirPath);
    });
    electron.ipcMain.handle("fs:readDirectoryRecursive", async (_, dirPath) => {
      return await this.fileSystemHandler.readDirectoryRecursive(dirPath);
    });
    electron.ipcMain.handle("fs:pathExists", async (_, filePath) => {
      return await this.fileSystemHandler.pathExists(filePath);
    });
  }
  /**
   * 设置对话框相关的 IPC 处理器
   */
  setupDialogHandlers() {
    electron.ipcMain.handle("dialog:openDirectory", async () => {
      return await this.dialogHandler.openDirectory();
    });
    electron.ipcMain.handle("dialog:openFile", async () => {
      return await this.dialogHandler.openFile();
    });
    electron.ipcMain.handle("dialog:saveFile", async (_, options) => {
      return await this.dialogHandler.saveFile(options);
    });
    electron.ipcMain.handle("project:export", async (_, projectData) => {
      return await this.dialogHandler.exportProject(projectData);
    });
    electron.ipcMain.handle("project:import", async () => {
      return await this.dialogHandler.importProject();
    });
    electron.ipcMain.handle("dialog:selectNewPath", async (_, options) => {
      return await this.dialogHandler.selectNewPath(options);
    });
    electron.ipcMain.handle("dialog:showOpenDialog", async (event, options) => {
      const window = electron.BrowserWindow.fromWebContents(event.sender);
      if (window) {
        return await this.dialogHandler.showOpenDialog(window, options);
      }
      return { canceled: true, filePaths: [] };
    });
    electron.ipcMain.handle("dialog:showSaveDialog", async (event, options) => {
      const window = electron.BrowserWindow.fromWebContents(event.sender);
      if (window) {
        return await this.dialogHandler.showSaveDialog(window, options);
      }
      return { canceled: true, filePath: "" };
    });
  }
  /**
   * 设置菜单相关的 IPC 处理器
   */
  setupMenuHandlers() {
    electron.ipcMain.on("show-file-menu", (event) => {
      const win = electron.BrowserWindow.fromWebContents(event.sender);
      if (win && !win.isDestroyed()) {
        this.menuManager.createFileMenu(win);
      }
    });
    electron.ipcMain.on("show-project-menu", (event) => {
      const win = electron.BrowserWindow.fromWebContents(event.sender);
      if (win && !win.isDestroyed()) {
        this.menuManager.createProjectMenu(win);
      }
    });
    electron.ipcMain.on("show-edit-menu", (event) => {
      const win = electron.BrowserWindow.fromWebContents(event.sender);
      if (win && !win.isDestroyed()) {
        this.menuManager.createEditMenu(win);
      }
    });
    electron.ipcMain.on("show-view-menu", (event) => {
      const win = electron.BrowserWindow.fromWebContents(event.sender);
      if (win && !win.isDestroyed()) {
        this.menuManager.createViewMenu(win);
      }
    });
    electron.ipcMain.on("show-dev-menu", (event) => {
      const win = electron.BrowserWindow.fromWebContents(event.sender);
      if (win && !win.isDestroyed()) {
        this.menuManager.createDevMenu(win);
      }
    });
    electron.ipcMain.on("show-about-menu", (event) => {
      const win = electron.BrowserWindow.fromWebContents(event.sender);
      if (win && !win.isDestroyed()) {
        this.menuManager.createAboutMenu(win);
      }
    });
  }
  /**
   * 设置实用工具相关的 IPC 处理器
   */
  setupUtilityHandlers() {
    electron.ipcMain.handle("os:tmpdir", async () => {
      return node_os.tmpdir();
    });
    electron.ipcMain.handle("path:join", async (_, ...paths) => {
      return node_path.join(...paths);
    });
    electron.ipcMain.handle("path:dirname", async (_, filePath) => {
      return node_path.dirname(filePath);
    });
  }
}
const __filename$1 = node_url.fileURLToPath(typeof document === "undefined" ? require("url").pathToFileURL(__filename).href : _documentCurrentScript && _documentCurrentScript.tagName.toUpperCase() === "SCRIPT" && _documentCurrentScript.src || new URL("index.js", document.baseURI).href);
const __dirname$1 = node_path.dirname(__filename$1);
process.env.DIST_ELECTRON = node_path.join(__dirname$1, "..");
process.env.DIST = node_path.join(process.env.DIST_ELECTRON, "../dist");
process.env.PUBLIC = process.env.VITE_DEV_SERVER_URL ? node_path.join(process.env.DIST_ELECTRON, "../public") : process.env.DIST;
const isDev = process.env["NODE_ENV"] === "development";
if (node_os.release().startsWith("6.1")) electron.app.disableHardwareAcceleration();
if (process.platform === "win32") electron.app.setAppUserModelId(electron.app.getName());
if (!electron.app.requestSingleInstanceLock()) {
  electron.app.quit();
  process.exit(0);
}
const processManager = new ProcessManager();
const windowManager = new WindowManager();
const fileSystemHandler = new FileSystemHandler();
const dialogHandler = new DialogHandler();
const menuManager = new MenuManager(isDev);
const ipcHandler = new IPCHandler(
  windowManager,
  fileSystemHandler,
  dialogHandler,
  menuManager
);
let isShuttingDown = false;
async function startServicesAndMainWindow() {
  console.log("Services starting...");
  console.log("All services started successfully, creating UI...");
  const loadingWindow = windowManager.getLoadingWindow();
  loadingWindow == null ? void 0 : loadingWindow.webContents.send(
    "update-progress",
    100,
    "服务启动完成，请稍后..."
  );
  await windowManager.createMainWindow("/welcome");
  const mainWindow = windowManager.getMainWindow();
  if (mainWindow) {
    mainWindow.once("ready-to-show", () => {
      setTimeout(() => {
        windowManager.closeLoadingWindow();
        windowManager.showMainWindow();
      }, 300);
    });
  }
}
function initializeApp() {
  const loadingWindow = windowManager.createLoadingWindow();
  loadingWindow.webContents.on("did-finish-load", () => {
    try {
      const packageJsonPath = node_path.join(electron.app.getAppPath(), "package.json");
      const pkg = JSON.parse(node_fs.readFileSync(packageJsonPath, "utf-8"));
      loadingWindow == null ? void 0 : loadingWindow.webContents.send("set-version", pkg.version);
    } catch (error) {
      console.error("Failed to read package.json version:", error);
    }
    loadingWindow == null ? void 0 : loadingWindow.show();
    startServicesAndMainWindow();
  });
  menuManager.createMenu();
}
electron.app.whenReady().then(() => {
  initializeApp();
});
electron.app.on("window-all-closed", () => {
  if (process.platform !== "darwin") {
    electron.app.quit();
  }
});
electron.app.on("before-quit", async (event) => {
  const BackendEnabled = "false";
  if (!isShuttingDown && BackendEnabled === "true") {
    event.preventDefault();
    isShuttingDown = true;
    processManager.setShuttingDown(true);
    console.log("Application is quitting, terminating all services...");
    const terminateWithTimeout = async (terminateFunc, name, timeout) => {
      return Promise.race([
        terminateFunc(),
        new Promise((resolve) => {
          setTimeout(() => {
            console.warn(`${name} termination timeout after ${timeout}ms`);
            resolve();
          }, timeout);
        })
      ]);
    };
    try {
      await Promise.all([
        terminateWithTimeout(
          async () => await processManager.terminateAllRabbitMQProcesses(),
          "RabbitMQ",
          5e3
        ),
        terminateWithTimeout(
          async () => await processManager.terminateAllBackendProcesses(),
          "Backend",
          5e3
        )
      ]);
      console.log("All processes terminated successfully");
    } catch (error) {
      console.error("Error during service termination:", error);
    }
    setTimeout(() => {
      console.log("Clean exit completed, quitting application");
      electron.app.exit(0);
    }, 1e3);
  }
});
process.on("exit", () => {
});
process.on("uncaughtException", async (error) => {
  console.error("Uncaught exception:", error);
  isShuttingDown = true;
  processManager.setShuttingDown(true);
  try {
    await Promise.race([
      Promise.all([
        processManager.terminateAllBackendProcesses(),
        processManager.terminateAllRabbitMQProcesses()
      ]),
      new Promise((resolve) => setTimeout(resolve, 3e3))
    ]);
    console.log("Emergency cleanup completed");
  } catch (e) {
    console.error("Emergency cleanup error:", e);
  }
  electron.app.exit(1);
});
process.on("unhandledRejection", (reason) => {
  console.error("Unhandled Promise rejection:", reason);
});
electron.app.on("second-instance", () => {
  const mainWindow = windowManager.getMainWindow();
  if (mainWindow && !mainWindow.isDestroyed()) {
    if (mainWindow.isMinimized()) mainWindow.restore();
    mainWindow.focus();
  }
});
electron.app.on("activate", () => {
  const mainWindow = windowManager.getMainWindow();
  if (!mainWindow || mainWindow.isDestroyed()) {
    windowManager.createMainWindow("/welcome");
  } else {
    if (mainWindow.isMinimized()) mainWindow.restore();
    mainWindow.focus();
  }
});
exports.dialogHandler = dialogHandler;
exports.fileSystemHandler = fileSystemHandler;
exports.ipcHandler = ipcHandler;
exports.menuManager = menuManager;
exports.processManager = processManager;
exports.windowManager = windowManager;
//# sourceMappingURL=index.js.map
