{"name": "MultiObjectiveBayesianOptimization", "displayName": "多目标贝叶斯优化算法", "description": "通过多目标贝叶斯优化算法设计配方", "type": "multi-target-search", "defaultParams": {"nIterations": {"value": 100, "type": "number", "description": "优化迭代次数", "displayName": "优化迭代次数", "min": 20, "max": 500, "step": 10}}, "tips": ["多目标贝叶斯优化算法基于高斯过程回归和采集函数", "MOBO适用于计算成本高的多目标优化问题", "初始采样点数影响算法的探索能力", "采集函数的选择影响算法的性能"], "introduction": {"detailedDescription": "多目标贝叶斯优化算法（MOBO）是一种基于高斯过程回归的优化算法，通过构建目标函数的概率模型来指导搜索。MOBO适用于计算成本高的多目标优化问题，能够有效平衡探索与开发", "usageTips": ["MOBO适用于计算成本高的多目标优化问题"], "scenarios": "适用于计算成本高的多目标优化问题，如配方优化、参数调优、工程设计等", "mainParams": [{"name": "n_iterations", "description": "优化迭代次数"}]}}