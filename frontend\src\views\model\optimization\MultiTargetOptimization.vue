<template>
  <div class="multi-target-optimization">
    <!-- 主配置卡片 -->
    <div class="main-config-card content-bg">
      <!-- Tab controls -->
      <div class="selection-controls">
        <button type="button" :class="['selection-button', { 'is-active': activeSelectionTab === 'settings' }]"
          @click="activeSelectionTab = 'settings'">
          <img :src="settingsIcon" alt="icon" class="btn-icon" />
          优化设置
        </button>
        <button type="button" :class="['selection-button', { 'is-active': activeSelectionTab === 'parameters' }]"
          @click="activeSelectionTab = 'parameters'">
          <img :src="parameterIcon" alt="icon" class="btn-icon" />
          算法参数
        </button>
        <button type="button" 
          :class="['selection-button', { 'is-active': activeSelectionTab === 'featureRanges' }]"
          @click="activeSelectionTab = 'featureRanges';"
          :disabled="selectedModels.length === 0">
          <img :src="featureRangeIcon" alt="icon" class="btn-icon" />
          自变量范围
        </button>
      </div>

      <div class="selection-content">
        <div v-show="activeSelectionTab === 'settings'">
          <div class="setting-section">
            <el-row :gutter="20" align="middle">
              <el-col :xs="24" :sm="12" :lg="6">
                <div class="form-item-label">预测模型</div>
                  <div style="display: flex; align-items: center; width: 100%;">
                   <el-select v-model="selectedModels" multiple placeholder="选择多个预测模型" style="flex-grow: 1;"
                     @change="handleModelsChange" filterable :filter-method="filterModels" :reserve-keyword="false" :disabled="loading">
                     <el-option v-for="item in filteredModelOptions" :key="item.value" :label="item.label" :value="item.value">
                       <el-tooltip :content="`ID: ${item.id} | 创建时间: ${item.createdAt} | 目标: ${item.targetName}`"
                         placement="right" effect="light">
                         <div>{{ item.label }}</div>
                       </el-tooltip>
                     </el-option>
                   </el-select>
                  <el-button circle @click="emit('refresh-models')" :disabled="loading" style="margin-left: 8px;">
                    <el-icon :class="{ 'is-loading': loading }">
                      <Refresh />
                    </el-icon>
                  </el-button>
                </div>
              </el-col>
              <el-col :xs="24" :sm="12" :lg="4">
                <div class="form-item-label">优化算法</div>
                <el-select v-model="selectedOptimizer" placeholder="选择优化算法" style="width: 100%">
                  <el-option v-for="item in optimizerOptions" :key="item.id" :label="item.config.displayName"
                    :value="item.id" />
                </el-select>
              </el-col>
              <el-col :xs="24" :sm="24" :lg="14">
                <div class="target-info">
                  <el-button type="danger" v-if="progress === 100" @click="resetOptimization" style="margin-left: 20px;">
                    重置
                  </el-button>
                </div>
              </el-col>
            </el-row>

            <!-- 多模型配置 -->
            <div v-if="selectedModels.length > 0" class="models-config">
              <div class="config-header">
                <div class="form-item-label">模型配置</div>
                <el-button
                  class="expand-btn"
                  :class="{ 'is-active': isModelConfigVisible }"
                  @click="isModelConfigVisible = !isModelConfigVisible"
                >
                  {{ isModelConfigVisible ? "收起" : "展开" }}
                  <el-icon class="el-icon--right" :class="{ 'is-active': isModelConfigVisible }">
                    <ArrowDown />
                  </el-icon>
                </el-button>
              </div>
              <el-collapse-transition>
                <div v-show="isModelConfigVisible">
                  <div class="models-table">
                    <div class="models-header">
                      <div class="header-cell checkbox-cell">选择</div>
                      <div class="header-cell optimization-target">优化目标</div>
                      <div class="header-cell model-name">预测模型</div>
                      <div class="header-cell target-value">目标值</div>
                      <div class="header-cell optimization-precision">优化精度%</div>
                      <div class="header-cell target-weight">目标权重</div>
                    </div>
                    <!-- 为每个模型的每个优化目标创建单独的行 -->
                    <template v-for="modelUid in selectedModels" :key="modelUid">
                      <div v-for="(targetConfig, targetIndex) in modelConfigs[modelUid]"
                          :key="`${modelUid}-${targetIndex}`"
                          :class="['model-row', { 'disabled-row': !targetConfig.enabled }]">
                        <div class="model-cell checkbox-cell">
                          <el-checkbox
                            v-model="targetConfig.enabled"
                            @change="updateTotalWeight" />
                        </div>
                        <div class="model-cell optimization-target">
                          {{ targetConfig.targetName }}
                        </div>
                        <div class="model-cell model-name">
                          <!-- 只在第一行显示模型名称 -->
                          <span>{{ getModelLabel(modelUid) }}</span>
                        </div>
                        <div class="model-cell target-value">
                          <el-input-number
                            v-model="targetConfig.targetValue"
                            :precision="1"
                            :step="0.1"
                            :min="getModelMinValue(modelUid, targetConfig.targetName)"
                            :max="getModelMaxValue(modelUid, targetConfig.targetName)"
                            placeholder="目标值"
                            :disabled="!targetConfig.enabled"
                            @change="updateTotalWeight" />
                        </div>
                        <div class="model-cell optimization-precision">
                          <el-input-number
                            v-model="targetConfig.criterion"
                            :precision="2"
                            :step="0.01"
                            :min="0.01"
                            :max="1"
                            placeholder="精度%"
                            :disabled="!targetConfig.enabled"
                            @change="updateTotalWeight" />
                        </div>
                        <div class="model-cell target-weight">
                          <el-input-number
                            v-model="targetConfig.weight"
                            :precision="2"
                            :step="0.1"
                            :min="0"
                            :max="10"
                            placeholder="权重"
                            :disabled="!targetConfig.enabled"
                            @change="updateTotalWeight" />
                        </div>
                      </div>
                    </template>
                    <div class="tips">
                      <div class="tips-row">
                        <el-tag v-if="hasDuplicateTargets" type="warning" size="small">
                          存在重复的优化目标，建议优化目标不要重复
                        </el-tag>
                      </div>
                      <div class="tips-row">
                        <div class="total-weight">
                          总权重: {{ totalWeight.toFixed(2) }}
                          <el-tag v-if="totalWeight !== 1" type="warning" size="small">
                            建议总权重为1
                          </el-tag>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </el-collapse-transition>
            </div>

            <el-row class="mt-4" align="middle">
              <el-col :span="24" style="display: flex; align-items: center;">
                <el-button type="primary" @click="startOptimization" :disabled="disableOptimization">
                  开始优化
                </el-button>
                <el-progress :percentage="progress" type="circle" :width="40" style="margin-left: 16px;"
                  v-show="progress > 0" :status="progress === 100 ? 'success' : ''" />
              </el-col>
            </el-row>
          </div>
        </div>
        <div v-show="activeSelectionTab === 'parameters'">
          <div class="params-section">
            <ModelParamsSetting v-if="selectedOptimizer" ref="modelParamsSettingRef" :algorithm-name="selectedOptimizer"
              :is-optimizer="true" />
            <el-empty v-else description="请先选择优化算法" />
          </div>
        </div>
        <div v-show="activeSelectionTab === 'featureRanges'">
          <div class="feature-ranges-section">
            <template v-if="selectedModels.length > 0 && mergedFeatureRanges.length > 0">
              <div class="form-item-label">自变量范围设置（多模型合并）</div>
              
              <!-- 合并后的自变量范围表格 -->
              <div class="feature-ranges-table">
                <div class="models-header">
                  <div class="header-cell feature-name">特征名称</div>
                  <div class="header-cell model-names">模型</div>
                  <div class="header-cell min-value">最小值</div>
                  <div class="header-cell max-value">最大值</div>
                </div>
                <div v-for="(range, index) in mergedFeatureRanges" :key="index" class="model-row">
                  <div class="model-cell feature-name">
                    {{ range.featureName }}
                  </div>
                  <div class="model-cell model-names">
                    <div class="model-names-container" :class="{ 'single-model': range.models.length === 1 }">
                      <template v-for="(model, idx) in range.models.slice(0, 3)" :key="idx">
                        <el-tooltip 
                          :content="model" 
                          placement="top"
                          :disabled="model.length <= 25">
                          <span class="model-name-item">{{ truncateModelName(model, 25) }}</span>
                        </el-tooltip>
                      </template>
                      <el-tooltip 
                        v-if="range.models.length > 3"
                        placement="top">
                        <template #content>
                          <div style="max-width: 300px;">
                            <div><strong>所有模型：</strong></div>
                            <div v-for="(model, idx) in range.models" :key="idx" style="padding: 2px 0;">
                              {{ idx + 1 }}. {{ model }}
                            </div>
                          </div>
                        </template>
                        <span class="model-name-more">
                          +{{ range.models.length - 3 }} 更多
                        </span>
                      </el-tooltip>
                    </div>
                  </div>
                  <div class="model-cell min-value">
                    <el-input-number
                      v-model="range.minValue"
                      :precision="3"
                      :step="0.001"
                      :min="-1000"
                      :max="range.maxValue"
                      size="small"
                      style="width: 100%" />
                  </div>
                  <div class="model-cell max-value">
                    <el-input-number
                      v-model="range.maxValue"
                      :precision="3"
                      :step="0.001"
                      :min="range.minValue"
                      :max="1000"
                      size="small"
                      style="width: 100%" />
                  </div>
                </div>
              </div>
              
              <el-alert
                title="提示"
                description="自变量范围已合并多个模型的范围（取并集），将影响优化算法的搜索空间。"
                type="info"
                :closable="false"
                style="margin-top: 16px;" />
            </template>
            <el-empty v-else description="请先选择预测模型以查看自变量范围" />
          </div>
        </div>
      </div>
    </div>

    <!-- 优化配方结果 -->
    <div class="result-section content-bg">
      <div class="section-header">
        <h3 class="section-title">优化配方结果</h3>
        <el-button :icon="Download" @click="exportResults" :disabled="resultData.length === 0">
          导出数据
        </el-button>
      </div>
      <ReTable :data="resultData">
        <template #default="{ data: paginatedData }">
          <el-table :data="paginatedData" class="result-table" max-height="calc(100vh - 450px)">
            <template #empty>
              <el-empty description="暂无结果" />
            </template>
            <el-table-column type="index" label="序号" width="50" v-if="resultData.length > 0" />
            <el-table-column v-for="col in sortedTableColumns" 
              :key="col.prop" 
              :prop="col.prop" 
              :label="col.label"
              sortable 
              v-if="resultData.length > 0" />
          </el-table>
        </template>
      </ReTable>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, computed, onBeforeUnmount, defineComponent } from "vue";
import { ElMessage, ElLoading } from "element-plus";
import { InfoFilled, Refresh, ArrowDown } from "@element-plus/icons-vue";
import ModelParamsSetting from "@/components/modelManagement/src/ModelParamsSetting/index.vue";
import { SupportedModel } from "@/utils/modelParamsLoader";
import { runOptimizationMultiple } from "@/api/models/optimize";
import { dynamicModelManager, ModelMetadata, getMultiTargetAlgorithms } from "@/utils/dynamicModelLoader";
import { getSocket } from "@/utils/socket";
import { Loading, Check, Download } from "@element-plus/icons-vue";
import ReTable from "@/components/ReTable/index.vue";
import { exportSingleSheet } from "@/utils/exportUtils";

import settingsIconSelected from "@/assets/svg/variable_selected.svg?url";
import settingsIconDefault from "@/assets/svg/variable.svg?url";
import parameterIconSelected from "@/assets/svg/parameter_selected.svg?url";
import parameterIconDefault from "@/assets/svg/parameter.svg?url";
import featureRangeIconSelected from "@/assets/svg/range_selected.svg?url";
import featureRangeIconDefault from "@/assets/svg/range.svg?url";
import { useWorkspaceStoreHook } from "@/store/modules/workspace";

defineOptions({
  name: "MultiTargetOptimization"
});

const props = defineProps<{
  modelList: any[],
  loading: boolean
}>();

const emit = defineEmits(['refresh-models', 'update:optimization-status']);

const isModelConfigVisible = ref(true);

const activeSelectionTab = ref<'settings' | 'parameters' | 'featureRanges'>('settings');

const workspaceStore = useWorkspaceStoreHook();

const settingsIcon = computed(() =>
  activeSelectionTab.value === 'settings'
    ? settingsIconSelected
    : settingsIconDefault
);
const parameterIcon = computed(() =>
  activeSelectionTab.value === 'parameters'
    ? parameterIconSelected
    : parameterIconDefault
);
const featureRangeIcon = computed(() =>
  activeSelectionTab.value === 'featureRanges'
    ? featureRangeIconSelected
    : featureRangeIconDefault
);

const selectedModels = ref<string[]>([]);
const selectedOptimizer = ref<string | null>(null);
// 改为支持每个模型的多个优化目标
const modelConfigs = ref<Record<string, Array<{
  targetName: string;
  targetValue: number;
  criterion: number;
  weight: number;
  enabled: boolean; // 新增：是否启用此变量
}>>>({});
const optimizationInProgress = ref(false);

// 参数设置组件引用
const modelParamsSettingRef = ref<InstanceType<typeof ModelParamsSetting> | null>(null);

// 动态获取的模型选项 (从父组件获取)
const modelOptions = ref<any[]>([]);

// 过滤后的模型选项（用于搜索）
const filteredModelOptions = ref<any[]>([]);

// 优化算法选项 (从searchParams JSON文件获取)
const optimizerOptions = ref<ModelMetadata[]>([]);

// 结果表格列
const tableColumns = ref<Array<{ prop: string; label: string }>>([]);
const resultData = ref<Array<Record<string, any>>>([]);

// 自变量范围数据
const featureRanges = ref<Array<{
  featureName: string;
  minValue: number;
  maxValue: number;
}>>([]);

const mergedFeatureRanges = computed(() => {
  const featureMap = new Map<string, {
    minValue: number;
    maxValue: number;
    models: string[]; // 包含此特征的模型名称列表
  }>();

  // 遍历所有选中的模型
  selectedModels.value.forEach(modelUid => {
    const modelData = modelOptions.value.find(m => m.value === modelUid);
    if (modelData && modelData.featureRanges) {
      // 获取模型名称（截断处理）
      const modelName = modelData.label.split(' [')[0]; // 只取模型名称部分
      
      Object.entries(modelData.featureRanges).forEach(([featureName, range]) => {
        if (featureMap.has(featureName)) {
          // 如果特征已存在，更新范围（取并集）和模型列表
          const existing = featureMap.get(featureName)!;
          existing.minValue = Math.min(existing.minValue, range.min);
          existing.maxValue = Math.max(existing.maxValue, range.max);
          if (!existing.models.includes(modelName)) {
            existing.models.push(modelName);
          }
        } else {
          // 新特征
          featureMap.set(featureName, {
            minValue: range.min,
            maxValue: range.max,
            models: [modelName]
          });
        }
      });
    }
  });

  // 转换为数组格式
  return Array.from(featureMap.entries()).map(([featureName, data]) => ({
    featureName,
    minValue: data.minValue,
    maxValue: data.maxValue,
    models: data.models
  })).sort((a, b) => a.featureName.localeCompare(b.featureName));
});

const truncateModelName = (modelName: string, maxLength: number = 20) => {
  if (modelName.length <= maxLength) {
    return modelName;
  }
  return modelName.substring(0, maxLength - 3) + '...';
};

// 节流
let lastUpdate = 0;
const throttleInterval = 1000; // 1000ms
const throttleData = ref<Array<any>>([]);

// 进度
const progress = ref(0);

// 总权重计算
const totalWeight = computed(() => {
  return Object.values(modelConfigs.value)
    .flat()
    .filter(config => config.enabled) // 只计算启用的变量
    .reduce((sum, config) => sum + config.weight, 0);
});

// 添加计算属性来检测是否有重复的优化目标
const hasDuplicateTargets = computed(() => {
  const enabledTargets: string[] = [];
  
  Object.values(modelConfigs.value).forEach(configs => {
    configs.forEach(config => {
      if (config.enabled) {
        enabledTargets.push(config.targetName);
      }
    });
  });
  
  // 检查是否有重复
  const uniqueTargets = new Set(enabledTargets);
  return uniqueTargets.size < enabledTargets.length;
});

const handleSearchInfo = (data) => {
  console.log(data);
  progress.value = Number(data.data.progress || 0);

  if (data.code === 200) {
    // 检查是否已经完成，避免重复提示
    if ((Number(data.data.progress) >= 100 || (data.data.status && data.data.status === "completed")) && optimizationInProgress.value) {
      optimizationInProgress.value = false; // 重新启用按钮
      emit('update:optimization-status', false);
      ElMessage.success("优化已完成");
      progress.value = 100;
      
      // 移除 socket 监听器，避免继续接收消息
      const socket = getSocket();
      socket.off("search_info");
      return; // 直接返回，不再处理后续数据
    } else if (!optimizationInProgress.value) {
      // 如果优化已经结束，忽略后续消息
      return;
    } else {
      // 节流
      const now = Date.now();
      if (now - lastUpdate < throttleInterval) {
        throttleData.value.push(...data.data.searchInfo);
        return;
      };
      lastUpdate = now;
    }

    let tmpData = [];
    if (throttleData.value.length > 0) {
      tmpData.push(...throttleData.value);
      throttleData.value = [];
    }
    if (data.data.searchInfo && data.data.searchInfo.length > 0) {
      tmpData.push(...data.data.searchInfo);
    }
    resultData.value.push(...tmpData);
    if (tableColumns.value.length === 0 && resultData.value.length > 0) {
      tableColumns.value = Object.keys(resultData.value[0]).map(key => ({
        prop: key,
        label: key
      }));
    }
  } else {
    ElMessage.error(data.msg);
    optimizationInProgress.value = false; // 出错时也要重置状态
    emit('update:optimization-status', false);
  }
};

const resetOptimization = () => {
  // 停止正在进行的优化
  if (optimizationInProgress.value) {
    const socket = getSocket();
    socket.off("search_info");
  }
  
  // 重置所有状态
  resultData.value = [];
  progress.value = 0;
  optimizationInProgress.value = false;
  emit('update:optimization-status', false);
  tableColumns.value = [];
  selectedModels.value = []; // 清空选择的模型
  modelConfigs.value = {}; // 清空模型配置
  featureRanges.value = []; // 清空自变量范围数据
  activeSelectionTab.value = 'settings'; // 切换回设置选项卡
  
  // 或者保持默认的优化算法
  const moboAlgorithm = optimizerOptions.value.find(opt => opt.id === 'MultiObjectiveBayesianOptimization');
  if (moboAlgorithm) {
    selectedOptimizer.value = 'MultiObjectiveBayesianOptimization';
  }
  
  // 重置参数设置组件
  if (modelParamsSettingRef.value?.resetParams) {
    modelParamsSettingRef.value.resetParams();
  }
  
  // 清空节流数据
  throttleData.value = [];
  lastUpdate = 0;
};

// 添加新的计算属性，将带(pred)的列排在前面
const sortedTableColumns = computed(() => {
  if (tableColumns.value.length === 0) {
    return [];
  }
  
  // 分离带(pred)的列和其他列
  const predColumns = tableColumns.value.filter(col => 
    col.prop.includes('(pred)') || col.label.includes('(pred)')
  );
  
  const otherColumns = tableColumns.value.filter(col => 
    !col.prop.includes('(pred)') && !col.label.includes('(pred)')
  );
  
  // 先显示带(pred)的列，再显示其他列
  return [...predColumns, ...otherColumns];
});

watch(() => props.modelList, (newValue) => {
  modelOptions.value = newValue;
  filteredModelOptions.value = newValue;
}, { immediate: true, deep: true });

// 修改导出函数
const exportResults = () => {
  if (resultData.value.length === 0) {
    ElMessage.warning("没有可导出的数据");
    return;
  }

  // 使用排序后的列
  const columnsToExport = sortedTableColumns.value;

  const headers = ["序号", ...columnsToExport.map(c => c.label)];

  const content = resultData.value.map((row, index) => [
    index + 1,
    ...columnsToExport.map(c => row[c.prop])
  ]);

  exportSingleSheet(
    { headers, content },
    { suggestedName: "多目标优化配方结果" }
  );
};

// 从动态加载器获取优化算法
const loadOptimizers = () => {
  try {
    optimizerOptions.value = getMultiTargetAlgorithms();
    if (optimizerOptions.value.length === 0) {
      console.warn("No multi-target optimization algorithms found");
      ElMessage.warning("未找到可用的多目标优化算法");
    } else {
      console.log(`Loaded ${optimizerOptions.value.length} multi-target optimization algorithms:`,
        optimizerOptions.value.map(m => m.id).join(', '));
      
      // 默认选择多目标贝叶斯优化算法
      const moboAlgorithm = optimizerOptions.value.find(opt => opt.id === 'MultiObjectiveBayesianOptimization');
      if (moboAlgorithm) {
        selectedOptimizer.value = 'MultiObjectiveBayesianOptimization';
        console.log('Default selected: MultiObjectiveBayesianOptimization');
      } else {
        // 如果找不到MOBO算法，则选择第一个可用的算法
        selectedOptimizer.value = optimizerOptions.value[0].id;
        console.log('MultiObjectiveBayesianOptimization not found, selected first available:', optimizerOptions.value[0].id);
      }
    }
  } catch (error) {
    console.error("Error loading multi-target optimization algorithms:", error);
    ElMessage.error("加载多目标优化算法失败");
  }
};

const startOptimization = async () => {
  if (disableOptimization.value) {
    ElMessage.warning("请选择预测模型、优化算法，并设置有效的配置");
    return;
  }

  resultData.value = [];
  progress.value = 0;
  optimizationInProgress.value = true;
  emit('update:optimization-status', true);

  let params = {};
  try {
    if (modelParamsSettingRef.value?.getParams) {
      params = modelParamsSettingRef.value.getParams();
    } else {
      console.warn("无法获取优化参数，使用默认参数");
    }
  } catch (error) {
    console.error("Error getting optimization parameters:", error);
    ElMessage.warning("获取优化参数时出错，将使用默认参数");
  }

  const loadingInstance = ElLoading.service({
    lock: true, text: '多目标优化中，请稍候...', background: 'rgba(0, 0, 0, 0.7)',
  });

  try {
    // 构建多目标优化请求
    const targets: any[] = [];
    selectedModels.value.forEach(modelUid => {
      const configs = modelConfigs.value[modelUid];
      if (configs && configs.length > 0) {
        configs.forEach(config => {
          // 只添加启用的变量到优化目标中
          if (config.enabled) {
            targets.push({
              modelUid: modelUid,
              target: {
                name: config.targetName,
                value: config.targetValue
              },
              criterion: config.criterion,
              weight: config.weight
            });
          }
        });
      }
    });

    const featureRanges: Record<string, { min: number; max: number }> = {};
    mergedFeatureRanges.value.forEach(range => {
      featureRanges[range.featureName] = {
        min: range.minValue,
        max: range.maxValue
      };
    });
    const workspacePath = workspaceStore.getCurrentWorkspacePath || '';
    const workspaceName = workspacePath
    ? (workspacePath.replace(/[\\/]+$/, '').split(/[\\/]/).pop() || '')
    : '';

    const response = await runOptimizationMultiple({
      targets: targets,
      search: {
        algorithm: selectedOptimizer.value,
        params: params,
      },
      featureRanges: featureRanges,
      path: workspaceName,
    });

    if (response.code === 200) {
      const taskUid = response.data.uid;
      const socket = getSocket();
      socket.off("search_info");
      socket.emit("get_search_info", { uid: taskUid });
      socket.on("search_info", (data) => {
        if (typeof data === "string") data = JSON.parse(data);
        handleSearchInfo(data);
      });
      progress.value = 1;
    } else {
      ElMessage.error(`优化失败: ${response.msg || '未知错误'}`);
      optimizationInProgress.value = false;
      emit('update:optimization-status', false);
    }
  } catch (error) {
    console.error("Optimization error:", error);
    ElMessage.error('优化请求失败，请重试');
    optimizationInProgress.value = false;
    emit('update:optimization-status', false);
  } finally {
    loadingInstance.close();
  }
};

const handleModelsChange = () => {
  // 重置配置
  modelConfigs.value = {};
  selectedModels.value.forEach(modelUid => {
    const modelData = modelOptions.value.find(m => m.value === modelUid);
    if (modelData) {
      // 处理优化目标：如果是数组则为每个目标创建配置，否则创建单个配置
      const targetName = modelData.targetName;
      let targets: string[] = [];
      
      if (Array.isArray(targetName)) {
        targets = targetName;
      } else if (typeof targetName === 'string') {
        // 尝试解析字符串形式的数组，如 '["Burning rate/(mm/sec)", "NC+NG/%"]'
        try {
          const parsed = JSON.parse(targetName);
          if (Array.isArray(parsed)) {
            targets = parsed;
          } else {
            targets = [targetName];
          }
        } catch {
          targets = [targetName];
        }
      } else {
        targets = ['未知目标'];
      }
      
      // 计算每个目标的平均值
      const targetAverages: Record<string, number> = {};
      
      if (modelData.targetValues && Array.isArray(modelData.targetValues)) {
        // 获取目标索引
        targets.forEach((target, index) => {
          let sum = 0;
          let count = 0;
          
          // 遍历所有数据行，计算对应目标的平均值
          modelData.targetValues.forEach(row => {
            if (Array.isArray(row) && row.length > index) {
              const value = row[index];
              if (typeof value === 'number' && !isNaN(value)) {
                sum += value;
                count++;
              }
            }
          });
          
          // 计算平均值
          targetAverages[target] = count > 0 ? parseFloat((sum / count).toFixed(1)) : 0;
        });
      }
      
      // 为每个优化目标创建配置
      modelConfigs.value[modelUid] = targets.map(target => ({
        targetName: target,
        targetValue: targetAverages[target] || 0, // 使用计算的平均值
        criterion: 0.05,
        weight: 1 / targets.length, // 平均分配权重
        enabled: true // 默认启用所有变量
      }));
    }
  });
  updateTotalWeight();
};

const getModelLabel = (modelUid: string) => {
  const model = modelOptions.value.find(m => m.value === modelUid);
  return model ? model.label : modelUid;
};

const getModelMinValue = (modelUid: string, targetName?: string) => {
  const model = modelOptions.value.find(m => m.value === modelUid);
  if (model && model.targetValues && Array.isArray(model.targetValues)) {
    // 如果指定了目标名称，找到对应的索引
    if (targetName) {
      const targets = Array.isArray(model.targetName)
        ? model.targetName
        : typeof model.targetName === "string"
          ? (() => {
              try {
                return JSON.parse(model.targetName);
              } catch {
                return [model.targetName];
              }
            })()
          : ["未知目标"];
      const targetIndex = targets.indexOf(targetName);

      if (targetIndex >= 0) {
        const values: number[] = [];
        model.targetValues.forEach((row) => {
          if (Array.isArray(row) && row.length > targetIndex) {
            const value = row[targetIndex];
            if (typeof value === "number" && !isNaN(value)) {
              values.push(value);
            }
          }
        });
        return values.length > 0 ? Math.min(...values) : 0;
      }
    }
  }
  return 0;
};

const getModelMaxValue = (modelUid: string, targetName?: string) => {
  const model = modelOptions.value.find(m => m.value === modelUid);
  if (model && model.targetValues && Array.isArray(model.targetValues)) {
    // 如果指定了目标名称，找到对应的索引
    if (targetName) {
      const targets = Array.isArray(model.targetName) ? model.targetName : 
                      (typeof model.targetName === 'string' ? 
                        ((() => { try { return JSON.parse(model.targetName); } catch { return [model.targetName]; } })()) : 
                        ['未知目标']);
      const targetIndex = targets.indexOf(targetName);
      
      if (targetIndex >= 0) {
        const values: number[] = [];
        model.targetValues.forEach(row => {
          if (Array.isArray(row) && row.length > targetIndex) {
            const value = row[targetIndex];
            if (typeof value === 'number' && !isNaN(value)) {
              values.push(value);
            }
          }
        });
        return values.length > 0 ? Math.max(...values) : 100;
      }
    }
  }
  return 100;
};

const updateTotalWeight = () => {
  // 权重变化时的处理逻辑
  console.log('Total weight updated:', totalWeight.value);
};

// 模型搜索过滤函数
const filterModels = (query: string) => {
  if (!query) {
    filteredModelOptions.value = modelOptions.value;
    return;
  }
  
  const lowerQuery = query.toLowerCase();
  filteredModelOptions.value = modelOptions.value.filter(model => {
    // 搜索模型名称
    if (model.label.toLowerCase().includes(lowerQuery)) {
      return true;
    }
    // 搜索优化目标
    if (model.targetName.toLowerCase().includes(lowerQuery)) {
      return true;
    }
    // 搜索模型ID
    if (model.id.toString().includes(lowerQuery)) {
      return true;
    }
    return false;
  });
};

const disableOptimization = computed(() => {
  return selectedModels.value.length === 0 || 
    !selectedOptimizer.value || 
    props.loading || 
    optimizationInProgress.value;
});

onMounted(async () => {
  try {
    await dynamicModelManager.initialize();
    loadOptimizers();
    tableColumns.value = [];
  } catch (error) {
    console.error("Error initializing optimization page:", error);
    ElMessage.error("初始化页面失败");
  }
});

watch(selectedModels, handleModelsChange);

onBeforeUnmount(() => {
  try {
    getSocket().off("search_info");
  } catch (error) {
    console.error("Error cleaning up socket listeners:", error);
  }
});
</script>

<style scoped>
.multi-target-optimization {
  width: 100%;
}

.main-config-card {
  margin: 10px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.section-header .section-title {
  margin-bottom: 0;
}

.content-bg {
  background-color: var(--el-bg-color, #ffffff);
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0px 0px 12px rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 16px;
}

.form-item-label {
  margin-bottom: 8px;
  font-size: 14px;
  color: var(--el-text-color-regular);
}

.config-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.config-header .form-item-label {
  margin-bottom: 0;
}

.expand-btn {
  font-size: 14px;
  background: #f9fbff;
}

.expand-btn .el-icon--right {
  margin-left: 4px;
  transition: transform 0.3s;
}

.expand-btn .el-icon--right.is-active {
  transform: rotate(180deg);
}

.target-info {
  display: flex;
  align-items: center;
  height: 32px;
}

.mt-4 {
  margin-top: 1.5rem;
}

.result-table {
  width: 100%;
  overflow-x: auto;
  flex: 1;
}

.setting-section,
.params-section,
.result-section {
  margin: 20px 10px;
  box-sizing: border-box;
  overflow-x: hidden;
}

.result-section {
  display: flex;
  flex-direction: column;
  flex: 1;
  min-height: 0;
}

.params-section :deep(.params-setting-container) {
  display: flex !important;
  flex-wrap: wrap !important;
  background-color: transparent !important;
}

.params-section :deep(.param-card) {
  flex: 1 1 45% !important;
  margin: 10px !important;
}

.result-table {
  margin-bottom: 20px;
}

:deep(.el-progress) {
  &.el-progress--circle {
    .el-progress__text {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      font-size: 14px !important;
      font-weight: 600;
      color: var(--el-text-color-primary);
      line-height: 1;
      text-align: center;
      width: 100%;
      margin: 0 !important;
    }
  }
}

.selection-controls {
  display: flex;
  gap: 12px;
  margin-bottom: 24px;
}

.selection-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 184px;
  height: 36px;
  border-radius: 24px;
  border: none;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
  background: rgba(206, 206, 206, 0.1);
  color: #666;
}

.selection-button.is-active {
  background: rgba(0, 93, 255, 0.1);
  border: 1px solid #005DFF;
  color: #005DFF;
  font-weight: 500;
}

.btn-icon {
  width: 16px;
  height: 16px;
  margin-right: 8px;
}

.models-config {
  margin-top: 24px;
}

.models-table {
  border: 1px solid var(--el-border-color-light);
  border-radius: 8px;
  overflow: hidden;
}

.models-header {
  display: flex;
  background-color: var(--el-fill-color-light);
  border-bottom: 1px solid var(--el-border-color-light);
}

.header-cell {
  padding: 12px 16px;
  font-weight: 600;
  justify-content: center;
  text-align: center;
  color: var(--el-text-color-primary);
  border-right: 1px solid var(--el-border-color-light);
}

.header-cell:last-child {
  border-right: none;
}

.model-row {
  display: flex;
  border-bottom: 1px solid var(--el-border-color-light);
}

.model-row:last-child {
  border-bottom: none;
}

.model-cell {
  padding: 12px 16px;
  display: flex;
  align-items: center;
  border-right: 1px solid var(--el-border-color-light);
  justify-content: center;
  text-align: center;
}

.model-cell:last-child {
  border-right: none;
}

.model-name {
  flex: 2;
  min-width: 200px;
}

.optimization-target {
  flex: 1;
  min-width: 120px;
}

.target-value {
  flex: 1;
  min-width: 120px;
}

.model-feature-section {
  margin-bottom: 24px;
}

.model-title {
  margin-bottom: 12px;
  
  h4 {
    font-size: 16px;
    font-weight: 600;
    color: var(--el-text-color-primary);
    margin: 0;
  }
}

.selection-button:disabled {
  cursor: not-allowed;
  opacity: 0.5;
  background: rgba(206, 206, 206, 0.1);
  color: #999;
}

.selection-button:disabled:hover {
  background: rgba(206, 206, 206, 0.1);
  color: #999;
}

.optimization-precision {
  flex: 1;
  min-width: 120px;
}

.target-weight {
  flex: 1;
  min-width: 120px;
}

.tips {
  display: flex;
  flex-direction: column;
  padding: 16px;
  background-color: var(--el-fill-color-lighter);
  border-top: 1px solid var(--el-border-color-light);
  gap: 5px;
}

.tips-row {
  display: flex;
  justify-content: flex-end;
}

.tips-text {
  font-size: 14px;
  margin-right: 10px;
}

.total-weight {
  display: flex;
  align-items: center;
  gap: 12px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.checkbox-cell {
  flex: 0;
  min-width: 80px;
  text-align: center;
}

.disabled-row .model-cell {
  color: var(--el-text-color-disabled);
}

.feature-ranges-section {
  margin: 20px 10px;
  box-sizing: border-box;
  overflow-x: hidden;
}

.feature-ranges-table {
  margin-top: 16px;
  border: 1px solid var(--el-border-color-light);
  border-radius: 8px;
  max-height: 400px;
  overflow-y: scroll;
}

/* 自定义滚动条样式 */
.feature-ranges-table::-webkit-scrollbar {
  width: 6px;
}

.feature-ranges-table::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.feature-ranges-table::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.feature-ranges-table::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Add specific styles for feature ranges table to align columns */
.feature-ranges-table .feature-name {
  flex: 2;
  min-width: 200px;
}

.feature-ranges-table .min-value,
.feature-ranges-table .max-value {
  flex: 1;
  min-width: 120px;
}

.model-names {
  flex: 2;
  min-width: 200px;
  max-width: 300px;
}

.model-names-container {
  display: flex;
  flex-direction: column;
  gap: 4px;
  width: 100%;
}

/* 单个模型时居中显示 */
.model-names-container.single-model {
  flex-direction: row;
  justify-content: center;
  align-items: center;
}

.model-name-item {
  font-size: 13px;
  line-height: 1.4;
  color: var(--el-text-color-regular);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.model-name-more {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  font-style: italic;
}

/* 调整行高以适应多行内容 */
.feature-ranges-table .model-row {
  min-height: 60px;
  align-items: stretch;
}

.feature-ranges-table .model-cell {
  padding: 12px 8px;
  display: flex;
  align-items: center;
  border-right: 1px solid var(--el-border-color-light);
  justify-content: center;
  text-align: center;
}

/* 模型名称列的对齐方式 */
.feature-ranges-table .model-cell.model-names {
  align-items: center; /* 改为居中对齐 */
}

/* 调整其他列的宽度 */
.feature-ranges-table .feature-name {
  flex: 1.5;
  min-width: 150px;
}

.feature-ranges-table .min-value,
.feature-ranges-table .max-value {
  flex: 1;
  min-width: 100px;
}
</style>