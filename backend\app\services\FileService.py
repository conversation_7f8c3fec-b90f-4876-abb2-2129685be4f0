from app.configs import BasicConfig
from joblib import dump, load
from typing import List

MODEL_PATH = BasicConfig.MODEL_PATH

class FileService(object):

    @staticmethod
    def save_model(model: object, model_id: str):
        model_path = MODEL_PATH / model_id
        dump(model, model_path)

    @staticmethod
    def load_model(model_id: str):
        model_path = MODEL_PATH / model_id
        return load(model_path)

    @staticmethod
    def delete_model(model_id: str):
        model_path = MODEL_PATH / model_id
        if model_path.exists():
            model_path.unlink()

    @staticmethod
    def get_model_path(model_id: str):
        return MODEL_PATH / model_id

    @staticmethod
    def delete_models(model_uids: List[str]):
        for model_uid in model_uids:
            model_path = MODEL_PATH / model_uid
            if model_path.exists():
                model_path.unlink()
