import numpy as np
import pandas as pd
from pymoo.core.problem import Problem
from pymoo.algorithms.soo.nonconvex.ga import GA
from pymoo.algorithms.soo.nonconvex.pso import PSO
from pymoo.algorithms.soo.nonconvex.de import DE
from pymoo.operators.crossover.sbx import SBX
from pymoo.operators.mutation.pm import PM
from pymoo.operators.sampling.rnd import FloatRandomSampling
from pymoo.operators.sampling.lhs import LHS
from pymoo.optimize import minimize
from pymoo.core.callback import Callback
from typing import Callable, List
from sklearn.base import BaseEstimator
from sklearn.preprocessing import StandardScaler

class CustomCallback(Callback):
    def __init__(self, 
        criterion: float, 
        model: BaseEstimator, 
        scaler: StandardScaler, 
        columns: List[str],
        __callable__: Callable[[float, str, str, dict], None]=None,
    ):
        super().__init__()
        self.codes = []
        self.criterion = criterion
        self.columns = columns
        self.data = pd.DataFrame([])
        self.model = model
        self.scaler = scaler
        self.__callable__ = __callable__

    def notify(self, algorithm):
        current_data = np.concatenate([algorithm.pop.get("F"), algorithm.pop.get("Y").reshape(-1, 1),algorithm.pop.get("X")], axis=1)
        codes = [ "-".join(i.tolist()) for i in current_data.round(2).astype(str) ]
        current_data = pd.DataFrame(current_data, index=codes)
        current_data = current_data[current_data.iloc[:, 0] < self.criterion]
        if not current_data.empty:
            new_codes = list(set(current_data.index).difference(self.codes))
            if len(new_codes) > 0:
                self.codes.extend(new_codes)
                current_data = current_data.iloc[:, 1:]
                current_data.columns = self.columns
                self.data = pd.concat([self.data, current_data], axis=0)
                self.__callable__(
                    body=current_data.round(2).astype(str).to_dict(orient="records"), 
                    result=self.data.round(2).astype(str).to_dict(orient="records"),
                    progress=f"{(algorithm.n_gen/algorithm.termination.n_max_gen)*100:.2f}", 
                    status="running", message="Search task running"
                )

class CustomBoundsProblem(Problem):
    def __init__(
        self, 
        model: BaseEstimator, 
        scaler: StandardScaler,
        xl: np.array, 
        xu: np.array, 
        target_value: float, 
        feature_names: List[str]
    ):
        super().__init__(n_var=xl.shape[0], n_obj=1, n_constr=0, xl=xl, xu=xu)
        self.target_value = target_value
        self.model = model
        self.scaler = scaler
        self.feature_names = feature_names

    def _evaluate(self, x, out, *args, **kwargs):
        x = pd.DataFrame(x, columns=self.feature_names)
        scaler_feature_names = getattr(self.scaler, 'feature_names_in_', [])
        if not (self.feature_names == scaler_feature_names).all(): print("feature_names != scaler.feature_names_in_")
        x[:] = self.scaler.transform(x)
        y = self.model.predict(x)
        out["F"] = np.abs(y - self.target_value) / (self.target_value + 1e-10)
        out["Y"] = y

class GeneticAlgorithm(object):

    def __init__(self, 
        population_size: int, 
        generations: int, 
        crossover: float, 
        mutation: float, 
        max_iter: int, 
        max_time: int,
        criterion: float
    ):
        self.population_size = population_size
        self.generations = generations
        self.crossover = crossover
        self.mutation = mutation
        self.max_iter = max_iter
        self.max_time = max_time
        self.criterion = criterion

        self.ga = GA(
            pop_size=self.population_size,                 # 种群大小
            sampling=FloatRandomSampling(),  # 随机采样初始种群
            crossover=SBX(prob=self.crossover, eta=15), # 模拟二进制交叉
            mutation=PM(eta=self.mutation),          # 多项式变异
            eliminate_duplicates=True     # 避免重复个体
        )
    
    def fit(self, 
        model: BaseEstimator,
        scaler: StandardScaler,
        target_value: float,    
        target_name: str,
        xl: np.array, 
        xu: np.array, 
        feature_names: List[str], 
        __callable__: Callable[[float, str, str, dict], None],
    ) -> None:

        self.problem = CustomBoundsProblem(
            xl=xl, 
            xu=xu, 
            model=model, 
            scaler=scaler,
            target_value=target_value, 
            feature_names=feature_names
        )

        minimize(
            self.problem, 
            self.ga, 
            ('n_gen', self.generations), 
            ('time', self.max_time), 
            ('n_eval', self.max_iter), 
            verbose=True,
            callback=CustomCallback(
                criterion=self.criterion, 
                model=model, 
                scaler=scaler, 
                columns=[f"{target_name}(pred)"]+feature_names.tolist(),
                __callable__=__callable__
            )
        )


class ParticleSwarmOptimization(object):

    def __init__(self, 
        population_size: int, 
        generations: int, 
        c1: float, 
        c2: float, 
        w: float, 
        max_iter: int, 
        max_time: int,
        criterion: float
    ):
        self.population_size = population_size
        self.generations = generations
        self.c1 = c1
        self.c2 = c2
        self.w = w
        self.max_iter = max_iter
        self.max_time = max_time
        self.criterion = criterion

        self.pso = PSO(
            pop_size=self.population_size, 
            sampling=LHS(),
            c1=self.c1,
            c2=self.c2,
            w=self.w,
            eliminate_duplicates=True
        )
    
    def fit(self, 
        model: BaseEstimator,
        scaler: StandardScaler,
        target_value: float,    
        target_name: str,
        xl: np.array, 
        xu: np.array, 
        feature_names: List[str], 
        __callable__: Callable[[float, str, str, dict], None],
    ) -> None:

        xu += 1e-6

        self.problem = CustomBoundsProblem(
            xl=xl, 
            xu=xu, 
            model=model, 
            scaler=scaler,
            target_value=target_value, 
            feature_names=feature_names
        )

        minimize(
            self.problem, 
            self.pso, 
            ('n_gen', self.generations), 
            ('time', self.max_time), 
            ('n_eval', self.max_iter), 
            verbose=True,
            callback=CustomCallback(
                criterion=self.criterion, 
                model=model, 
                scaler=scaler, 
                columns=[f"{target_name}(pred)"]+feature_names.tolist(),
                __callable__=__callable__
            )
        )


class DifferentialEvolution(object):

    def __init__(self, 
        population_size: int, 
        F: float, 
        CR: float, 
        max_iter: int, 
        max_time: int,
        criterion: float
    ):
        self.population_size = population_size
        self.F = F
        self.CR = CR
        self.max_iter = max_iter
        self.max_time = max_time
        self.criterion = criterion

        self.de = DE(
            pop_size=self.population_size, 
            sampling=LHS(),
            F=self.F,
            CR=self.CR,
        )
    
    def fit(self, 
        model: BaseEstimator,
        scaler: StandardScaler,
        target_value: float,    
        target_name: str,
        xl: np.array, 
        xu: np.array, 
        feature_names: List[str], 
        __callable__: Callable[[float, str, str, dict], None],
    ) -> None:

        xu += 1e-6

        self.problem = CustomBoundsProblem(
            xl=xl, 
            xu=xu, 
            model=model, 
            scaler=scaler,
            target_value=target_value, 
            feature_names=feature_names
        )

        minimize(
            self.problem, 
            self.de, 
            ('n_gen', self.max_iter), 
            ('time', self.max_time), 
            ('n_eval', self.max_iter), 
            verbose=True,
            callback=CustomCallback(
                criterion=self.criterion, 
                model=model, 
                scaler=scaler, 
                columns=[f"{target_name}(pred)"]+feature_names.tolist(),
                __callable__=__callable__
            )
        )
