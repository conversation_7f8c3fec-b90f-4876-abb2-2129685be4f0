export default {
    path: "/modelApplication",
    redirect: "/modelApplication/componentOptimization",
    meta: {
      icon: "ri:color-filter-ai-line",
      // showLink: false,
      title: "材料配方优化",
      rank: 10
    },
    children: [
      {
        path: "/modelApplication/componentOptimization",
        name: "componentOptimization",
        component: () => import("@/views/model/optimization/index.vue"),
        meta: {
          icon: "ri:tools-fill",
          title: "配方优化",
          keepAlive: true
        }
      },
      {
        path: "/modelApplication/optimizationList",
        name: "optimizationList",
        component: () => import("@/views/model/optimizationList/index.vue"),
        meta: {
          icon: "ri:list-settings-line",
          title: "优化列表",
          keepAlive: false
        }
      },
      // {
      //   path: "/error/500",
      //   name: "500",
      //   component: () => import("@/views/error/500.vue"),
      //   meta: {
      //     title: "500"
      //   }
      // }
    ]
  } satisfies RouteConfigsTable;
  