from flask import Blueprint, request, make_response, current_app, jsonify
import logging
from app.utils.params import camel_to_snake, snake_to_camel
from app.types.model_request import ModelRequest, ModelConfig
from app.routers.models.utils import check_model_request
from app.services.DBService import DBService
from app.services.RegressionService import ModelFile
from app.types.services import ModelTask, SearchTask
from app.services.TaskService import TaskService
from typing import cast, List, Dict
from app.types.flaskapp import FlaskWithExecutor
from app.types.search_request import SearchConfig, SearchRequestMultiple
from app.routers.search.utils import check_search_request, check_search_request_multiple
from app.services.FileService import FileService

multiple_target_search_bp = Blueprint("multiple_target_search", __name__)
logger = logging.getLogger()

@multiple_target_search_bp.route("/run_optimization", methods=["POST"])
def multiple_target_search():
    """
    多目标搜索
    """
    post_data: SearchRequestMultiple = camel_to_snake(request.json, ["featureRanges"])
    try:
        search_config: SearchConfig = check_search_request_multiple(post_data)
        search_task: SearchTask = DBService.create_search_task(
            uid=search_config.uid,
            name=multiple_target_search_bp.name,
            params=search_config
        )
        DBService.add_search_task(search_task)
        logger.info(f"multiple_target_search: 添加搜索任务: {search_task.uid}")
        DBService.update_search_task(search_config.uid, params=search_config.to_dict())

        model_files: Dict[str, ModelFile] = {uid: FileService.load_model(uid) for uid in search_config.model_uid}
        TaskService.submit_search_task_multiple(search_config, model_files)
        return_data = {
            "uid": search_config.uid
        }
        return make_response(jsonify({"code": 200, "msg": "success", "data": snake_to_camel(return_data)}), 200)
    except Exception as e:
        logger.error(f"multiple_target_search: 添加搜索任务失败: {e}")
        return make_response(jsonify({"code": 500, "msg": f"添加搜索任务失败: {e}", "data": None}), 500)