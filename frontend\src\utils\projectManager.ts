import { useWorkspaceStoreHook } from "@/store/modules/workspace";
import { ElMessage } from "element-plus";
import {
  exportProjectWithWorkspace,
  importProjectWithWorkspace,
} from "@/utils/exportUtils";

// 项目状态接口定义
export interface ProjectState {
  version: string;
  exportTime: string;
  multiTags: any[];
  workspace: {
    currentWorkspacePath: string;
    currentFilePath: string;
    openedFiles: string[];
    expandedNodes: string[];
    fileDisplayNames: Record<string, string>;
  };
  tableData: {
    currentTableData: any[][];
    currentTableHeader: any[];
    isModified: boolean;
  };
  // 可以添加更多状态信息
}

// 项目状态管理器
export class ProjectManager {
  private static instance: ProjectManager;

  private constructor() {}

  public static getInstance(): ProjectManager {
    if (!ProjectManager.instance) {
      ProjectManager.instance = new ProjectManager();
    }
    return ProjectManager.instance;
  }

  // 导出项目状态
  public async exportProjectState(): Promise<boolean> {
    try {
      const workspaceStore = useWorkspaceStoreHook();
      const currentWorkspacePath =
        (workspaceStore as any).currentWorkspacePath || "";

      // 使用增强的导出功能
      return await exportProjectWithWorkspace(currentWorkspacePath);
    } catch (error) {
      console.error("导出项目状态失败:", error);
      ElMessage.error("导出项目状态失败");
      return false;
    }
  }

  // 导入项目状态
  public async importProjectState(): Promise<boolean> {
    try {
      return await importProjectWithWorkspace();
    } catch (error) {
      console.error("导入项目状态失败:", error);
      ElMessage.error("导入项目状态失败");
      return false;
    }
  }
}

// 导出单例实例
export const projectManager = ProjectManager.getInstance();
