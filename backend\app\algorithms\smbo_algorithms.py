from typing import Callable, List
import optuna
import pandas as pd
import numpy as np

class MultiObjectiveBayesianOptimization:
    
    def __init__(self, n_iterations: int):
        self.n_iterations = n_iterations
        self.data = pd.DataFrame()

    def fit(self, data_pack: List[dict], feature_ranges: dict, __callable__: Callable[[float, str, str, dict], None]):
        
        def create_objective(data_pack: dict, feature_ranges: dict) -> Callable[[optuna.Trial], float]:
            def objective(trial: optuna.Trial) -> float:
                params_to_optimize = {}
                for fname, frange in feature_ranges.items():
                    params_to_optimize[fname] = trial.suggest_float(
                        fname, frange["min"], frange["max"]
                    )
                params_pools = pd.Series(params_to_optimize).round(2)
                error_weighted_sum = 0
                criterion_satisfied = []
                predictions = {}
                for pack in data_pack:
                    tname = pack["target_name"]
                    model_file = pack["model_file"]
                    scaler = model_file["scaler"][tname]
                    model = model_file["model"][tname]
                    feature_names = model_file["feature_names"][tname]
                    feature_values = params_pools[feature_names].to_frame().T
                    feature_values[:] = scaler.transform(feature_values)
                    pred = model.predict(feature_values).round(2)[0]
                    predictions[f"{tname}(pred)"] = pred

                    error = abs(pred - pack["target_value"]['value']) / (pack["target_value"]['value'] + 1e-10)
                    error_weighted = error * pack["target_value"]['weight']
                    error_weighted_sum += error_weighted
                    criterion_satisfied.append(error_weighted <= pack["target_value"]['criterion'])
                
                if np.all(criterion_satisfied):
                    predictions = pd.Series(predictions)
                    features = params_pools[feature_names]
                    current_data = pd.concat([predictions, features])
                    self.data = pd.concat([self.data, current_data.to_frame().T], axis=0)
                    __callable__(
                        body=[current_data.round(2).astype(str).to_dict()], 
                        result=self.data.round(2).astype(str).to_dict(orient="records"),
                        progress=f"{(trial.number/self.n_iterations)*100:.2f}", 
                        status="running", 
                        message="Search task running"
                    )                   
                return error_weighted_sum

            return objective
        
        study = optuna.create_study()
        objective_func = create_objective(data_pack, feature_ranges)
        study.optimize(objective_func, n_trials=self.n_iterations)
        
        return self