<template>
  <div class="model-categories-container" v-loading="isLoading">
    <div v-for="category in modelCategories" :key="category.key" class="category-section">
      <h3 class="category-title">
        <img :src="getCategoryIcon(category.key)" class="category-icon" alt="model icon" />
        {{ category.label }}
      </h3>
      <div class="model-list">
        <div v-for="model in category.models" :key="model.id" class="model-item-card"
          @click="selectModel(model, category.key)">
          <div class="model-card-header">
            <span class="model-label">{{ model.config.displayName }}</span>
            <img :src="getLargeIcon(category.key)" class="model-large-icon" alt="model type" />
          </div>
          <p class="model-description">{{ model.config.description }}</p>
          <div class="model-card-footer">
            <img :src="rightArrowIcon" class="right-arrow-icon" alt="right arrow" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import {
  type ModelCategory,
  type ModelMetadata
} from "@/utils/dynamicModelLoader";

defineOptions({
  name: "ModelSelect"
});

const props = defineProps<{
  modelCategories: ModelCategory[];
  getCategoryIcon: (categoryKey: string) => string;
  getLargeIcon: (categoryKey: string) => string;
  rightArrowIcon: string;
  isLoading: boolean;
}>();

const emit = defineEmits<{
  (e: "selectModel", model: ModelMetadata, category: string): void;
}>();

const selectModel = (model: ModelMetadata, categoryKey: string) => {
  emit("selectModel", model, categoryKey);
};
</script>

<style scoped>
.model-categories-container {
  padding: 0 10px;
}

.category-section {
  margin-bottom: 20px;
}

.category-title {
  font-size: 16px;
  color: #999999;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  padding: 0 20px;
}

.category-icon {
  width: 20px;
  height: 20px;
  margin-right: 8px;
}

.model-list {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  padding: 0 20px;
}

.model-item-card {
  background: rgba(255, 255, 255, 0.85);
  border: 1px solid var(--el-border-color-light);
  border-radius: 2px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.25s ease;
  position: relative;
  overflow: hidden;
  box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.08);
}

.model-item-card:hover {
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
  border-color: var(--el-color-primary);
}

.model-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.model-label {
  font-size: 18px;
  font-weight: bold;
  color: #3D3D3D;
}

.model-large-icon {
  width: 24px;
  height: 24px;
  color: var(--el-color-primary);
}

.model-description {
  font-size: 14px;
  color: #999999;
  line-height: 1.5;
  margin-bottom: 12px;
  min-height: 40px;
}

.model-card-footer {
  text-align: left;
  padding-top: 8px;
  margin-top: 8px;
}

.right-arrow-icon {
  width: 16px;
  height: 16px;
  transition: transform 0.2s ease;
}

.model-item-card:hover .right-arrow-icon {
  transform: translateX(4px);
}

/* 深色模式适配 */
html.dark .model-item-card {
  background: rgba(30, 30, 30, 0.85);
  border-color: var(--el-border-color);
}

html.dark .model-item-card:hover {
  border-color: var(--el-color-primary);
}
</style>