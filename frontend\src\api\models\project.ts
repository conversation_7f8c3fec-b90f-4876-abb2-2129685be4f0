import request from "@/utils/request";
import { AxiosHeaders } from "axios";

export interface ExportResponse {
  data: Blob;
  headers: Record<string, string>;
}

export interface ImportResponse {
  success: boolean;
  message: string;
}

export const exportProject = (workspaceName?: string) => {
  return request.get("/project/export", {
    responseType: "blob",
    headers: new AxiosHeaders({
      "Content-Type": "application/json",
    }),
    params: workspaceName ? { path: workspaceName } : undefined,
    skipLoading: true,
  });
};

export const importProject = (path: string, projectData: Blob) => {
  const formData = new FormData();
  formData.append("path", path);
  formData.append("projectFile", projectData);
  return request.post<ImportResponse>("/project/import", formData, {
    headers: new AxiosHeaders({
      "Content-Type": "multipart/form-data",
    }),
    skipLoading: true,
  });
};
