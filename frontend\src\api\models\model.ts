import request from "@/utils/request";
import type {
  ModelConfig,
  GetModelListResponse,
  GetModelListParams,
  MLModelResponse,
  PredictionResponse,
  ModelParamsResponse,
  MLModelUploadResponse,
} from "@/types/models";
import { AxiosHeaders } from "axios";

/**
 * 获取模型列表
 */
export const getModelList = (
  params?: GetModelListParams,
): Promise<GetModelListResponse> => {
  return request.get("/get_model_list", {
    params,
    headers: new AxiosHeaders({}),
    skipLoading: true,
  });
};

export const buildTreeModel = (data?: ModelConfig) => {
  return request.post<MLModelResponse>("/tree/build", data, {
    headers: new AxiosHeaders({}),
    skipLoading: true,
  });
};

export const buildLinearModel = (data?: ModelConfig) => {
  return request.post<MLModelResponse>("/linear/build", data, {
    headers: new AxiosHeaders({}),
    skipLoading: true,
  });
};

export const buildMLModel = (data?: ModelConfig) => {
  return request.post<MLModelResponse>("/other/build", data, {
    headers: new AxiosHeaders({}),
    skipLoading: true,
  });
};

export const editTreeModel = (data?: ModelConfig) => {
  return request.post<MLModelResponse>("/tree/edit", data, {
    headers: new AxiosHeaders({}),
    skipLoading: true,
  });
};

export const editLinearModel = (data?: ModelConfig) => {
  return request.post<MLModelResponse>("/linear/edit", data, {
    headers: new AxiosHeaders({}),
    skipLoading: true,
  });
};

export const editMLModel = (data?: ModelConfig) => {
  return request.post<MLModelResponse>("/other/edit", data, {
    headers: new AxiosHeaders({}),
    skipLoading: true,
  });
};

export const downloadModelFiles = (uids: string[]) => {
  return request.post<Blob>(
    "/download_models",
    { uids },
    {
      responseType: "blob", // This is important for downloading binary data
      headers: new AxiosHeaders({
        "Content-Type": "application/json",
      }),
      skipLoading: true,
    },
  );
};

export const predictWithData = (uid: string, data: any[]) => {
  return request.post<PredictionResponse>(
    "/predict",
    { uid, data },
    {
      headers: new AxiosHeaders({
        "Content-Type": "application/json",
      }),
      skipLoading: true,
    },
  );
};

/** 删除模型 */
export const deleteModels = (uids: string[]) => {
  return request.post(
    "/delete_models",
    { uids },
    {
      headers: new AxiosHeaders({
        "Content-Type": "application/json",
      }),
      skipLoading: true,
    },
  );
};

/** 获取模型具体信息 */

export const getModelInfo = (uid: string) => {
  return request.post<PredictionResponse>(
    "/get_model_info",
    { uid },
    {
      headers: new AxiosHeaders({
        "Content-Type": "application/json",
      }),
      skipLoading: true,
    },
  );
};

export const getModelParams = (uid: string) => {
  return request.post<ModelParamsResponse>(
    "/get_model_params",
    { uid },
    {
      headers: new AxiosHeaders({}),
      skipLoading: true,
    },
  );
};

/** 导入模型文件 */
export const uploadModel = (file: File) => {
  const formData = new FormData();
  formData.append("file", file);

  return request.post<MLModelUploadResponse>("/upload_model", formData, {
    headers: new AxiosHeaders({
      "Content-Type": "multipart/form-data",
    }),
    skipLoading: true,
  });
};
