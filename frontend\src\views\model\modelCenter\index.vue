<template>
  <div class="model-view-container">
    <!-- 未选择模型：左侧选择 + 右侧历史 -->
    <template v-if="!selectedModel">
      <div class="model-selection-panel">
        <h3 class="panel-title">模型算法仓</h3>
        <el-scrollbar>
          <ModelSelect
            :modelCategories="modelCategories"
            :getCategoryIcon="getCategoryIcon"
            :getLargeIcon="getLargeIcon"
            :rightArrowIcon="rightArrowIcon"
            :isLoading="isLoading"
            @selectModel="handleSelectModel"
          />
        </el-scrollbar>
      </div>
      <div class="computation-history-panel">
        <ComputationHistory />
      </div>
    </template>

    <!-- 已选择模型：详情页 -->
    <template v-else>
      <div class="model-detail-page">
        <div class="detail-header">
          <button class="back-link" type="button" @click="goBack">
            <el-icon class="back-icon"><ArrowLeft /></el-icon>
            模型中心
          </button>
        </div>
        <!-- 下方：详情面板 -->
        <ModelDetailPanel
          :model="selectedModel"
          :model-type="selectedModelType"
          :model-display-name="selectedModelDisplayName"
        />
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from "vue";
import {
  dynamicModelManager,
  getCategories,
  type ModelCategory,
  type ModelMetadata
} from "@/utils/dynamicModelLoader";

import ModelSelect from "@/components/modelManagement/src/modelSelect/index.vue";
import ComputationHistory from "./ComputationHistory.vue";
import ModelDetailPanel from "./ModelDetailPanel.vue";
import { ArrowLeft } from "@element-plus/icons-vue";

defineOptions({ name: "ModelView" });

const modelCategories = ref<ModelCategory[]>([]);
const isLoading = ref(false);
const selectedModel = ref<ModelMetadata | null>(null);

const getCategoryIcon = (categoryKey: string) => {
  switch (categoryKey) {
    case "linear":
      return new URL("@/assets/svg/linear.svg", import.meta.url).href;
    case "tree":
      return new URL("@/assets/svg/tree.svg", import.meta.url).href;
    case "ml":
    default:
      return new URL("@/assets/svg/other.svg", import.meta.url).href;
  }
};

const getLargeIcon = (categoryKey: string) => {
  switch (categoryKey) {
    case "linear":
      return new URL("@/assets/svg/linear_lg.svg", import.meta.url).href;
    case "tree":
      return new URL("@/assets/svg/tree_lg.svg", import.meta.url).href;
    case "ml":
    default:
      return new URL("@/assets/svg/other_lg.svg", import.meta.url).href;
  }
};

const rightArrowIcon = new URL("@/assets/svg/right_arrow.svg", import.meta.url).href;

const initializeModels = async () => {
  if (modelCategories.value.length > 0) return;
  try {
    isLoading.value = true;
    await dynamicModelManager.initialize();
    modelCategories.value = getCategories();
  } finally {
    isLoading.value = false;
  }
};

onMounted(() => initializeModels());

const handleSelectModel = (model: ModelMetadata) => {
  selectedModel.value = model;
};

const goBack = () => {
  selectedModel.value = null;
};

function resolveModelType(m: any | null | undefined): string | undefined {
  if (!m) return undefined;
  return m.id;
}

function resolveModelDisplayName(m: any | null | undefined): string | undefined {
  if (!m) return undefined;
  return m.config.displayName;
}

const selectedModelType = computed(() => resolveModelType(selectedModel.value));
const selectedModelDisplayName = computed(() => resolveModelDisplayName(selectedModel.value));
</script>

<style scoped>
.model-view-container {
  display: flex;
  justify-content: center;
  height: 100%;
  padding: 20px;
  gap: 20px;
}

.panel-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
  padding-left: 10px;
  padding-right: 10px;
}

html.dark .panel-title { color: #eee; }

.model-selection-panel {
  flex: 2;
  background-color: var(--el-bg-color);
  border-radius: 4px;
  overflow-y: hidden;
  padding: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.computation-history-panel {
  flex: 1;
  background-color: var(--el-bg-color);
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  overflow-y: auto;
  padding: 10px;
}

.model-detail-page {
  display: flex;
  flex-direction: column;
  width: 90%;
  gap: 20px;
}

/* 无边框返回按钮样式 */
.detail-header {
  display: flex;
  justify-content: flex-start;
  margin-bottom: 2px;
}

.back-link {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 4px 2px;
  font-size: 14px;
  color: var(--el-color-primary);
  background: transparent;
  border: none;           /* 关键：无边框 */
  outline: none;
  cursor: pointer;
}

.back-link:hover { opacity: 0.9; }

.back-icon {
  font-size: 16px;
  line-height: 1;
}
</style>
