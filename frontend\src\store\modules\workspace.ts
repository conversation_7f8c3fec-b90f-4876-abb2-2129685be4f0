import { defineStore } from "pinia";
import { store } from "../utils";

export interface WorkspaceState {
  currentWorkspacePath: string;
  currentFilePath: string;
  openedFiles: string[];
  isWorkspaceModified: boolean;
  currentView: "workspace" | "navigation" | "model";
  fileContents: Record<string, string>; // Cache file contents
  modifiedFiles: string[]; // Track modified files (changed from Set to Array for persistence)
  dataModifiedFiles: string[]; // Track files with modified data content
  fileDisplayNames: Record<string, string>; // Custom display names for files
  expandedNodes: string[]; // Track expanded directory nodes in file tree
}

export const useWorkspaceStore = defineStore({
  id: "pure-workspace",
  state: (): WorkspaceState => ({
    currentWorkspacePath: "",
    currentFilePath: "",
    openedFiles: [],
    isWorkspaceModified: false,
    currentView: "workspace",
    fileContents: {},
    modifiedFiles: [],
    dataModifiedFiles: [],
    fileDisplayNames: {},
    expandedNodes: [],
  }),

  getters: {
    getCurrentWorkspacePath: (state) => state.currentWorkspacePath,
    getCurrentFilePath: (state) => state.currentFilePath,
    getOpenedFiles: (state) => state.openedFiles,
    getIsWorkspaceModified: (state) => state.isWorkspaceModified,
    getCurrentView: (state) => state.currentView,
    getFileContent: (state) => (filePath: string) =>
      state.fileContents[filePath] || "",
    isFileModified: (state) => (filePath: string) => {
      // Ensure modifiedFiles is always an array
      const modifiedFiles = Array.isArray(state.modifiedFiles)
        ? state.modifiedFiles
        : [];
      return modifiedFiles.includes(filePath);
    },
    isDataModified: (state) => (filePath: string) => {
      // Check if file has modified data content
      const dataModifiedFiles = Array.isArray(state.dataModifiedFiles)
        ? state.dataModifiedFiles
        : [];
      return dataModifiedFiles.includes(filePath);
    },
    getFileDisplayName: (state) => (filePath: string) => {
      return state.fileDisplayNames[filePath] || state.getFileName(filePath);
    },
    hasWorkspace: (state) => !!state.currentWorkspacePath,
    getWorkspaceName: (state) => {
      if (!state.currentWorkspacePath) return "";
      return (
        state.currentWorkspacePath.split(/[/\\]/).pop() ||
        state.currentWorkspacePath
      );
    },
    getExpandedNodes: (state) => state.expandedNodes,
    isNodeExpanded: (state) => (nodePath: string) =>
      state.expandedNodes.includes(nodePath),
  },

  actions: {
    // Initialize store (ensure arrays are properly initialized)
    initializeStore() {
      if (!Array.isArray(this.modifiedFiles)) {
        this.modifiedFiles = [];
      }
      if (!Array.isArray(this.openedFiles)) {
        this.openedFiles = [];
      }
      if (!Array.isArray(this.dataModifiedFiles)) {
        this.dataModifiedFiles = [];
      }
      if (!Array.isArray(this.expandedNodes)) {
        this.expandedNodes = [];
      }
      if (!this.fileDisplayNames) {
        this.fileDisplayNames = {};
      }
      // Clear any invalid modification states on initialization
      this.isWorkspaceModified = this.modifiedFiles.length > 0;
    },

    // Workspace management
    setWorkspacePath(path: string) {
      this.initializeStore(); // Ensure arrays are initialized
      this.currentWorkspacePath = path;
      this.isWorkspaceModified = true;
    },

    clearWorkspace() {
      this.currentWorkspacePath = "";
      this.currentFilePath = "";
      this.openedFiles = [];
      this.isWorkspaceModified = false;
      this.fileContents = {};
      this.modifiedFiles = [];
      this.dataModifiedFiles = [];
      this.fileDisplayNames = {};
      this.expandedNodes = [];
    },

    // View management
    setCurrentView(view: "workspace" | "navigation") {
      this.currentView = view;
    },

    // File management
    setCurrentFile(filePath: string) {
      this.currentFilePath = filePath;
      this.addFileToWorkspace(filePath);
    },

    // Add file to workspace (like VSCode)
    addFileToWorkspace(filePath: string) {
      if (filePath && !this.openedFiles.includes(filePath)) {
        this.openedFiles.push(filePath);
        console.log("Added file to workspace:", filePath);
        console.log("Current opened files:", this.openedFiles);
      }
    },

    // Remove file from workspace
    removeFileFromWorkspace(filePath: string) {
      const index = this.openedFiles.indexOf(filePath);
      if (index > -1) {
        this.openedFiles.splice(index, 1);
        console.log("Removed file from workspace:", filePath);
        console.log("Current opened files:", this.openedFiles);

        // If the removed file was the current file, switch to another file
        if (this.currentFilePath === filePath) {
          if (this.openedFiles.length > 0) {
            this.currentFilePath =
              this.openedFiles[this.openedFiles.length - 1];
          } else {
            this.currentFilePath = "";
          }
        }
      }
    },

    // Clear all files from workspace (when switching workspace)
    clearAllFiles() {
      console.log("Clearing all files from workspace");
      this.openedFiles = [];
      this.currentFilePath = "";
      this.dataModifiedFiles = [];
      this.modifiedFiles = [];
      this.fileContents = {};
      this.fileDisplayNames = {};
      this.isWorkspaceModified = false;
      this.expandedNodes = [];
    },

    closeFile(filePath: string) {
      const index = this.openedFiles.indexOf(filePath);
      if (index > -1) {
        this.openedFiles.splice(index, 1);
      }

      // Remove from cache
      delete this.fileContents[filePath];
      const modifiedIndex = this.modifiedFiles.indexOf(filePath);
      if (modifiedIndex > -1) {
        this.modifiedFiles.splice(modifiedIndex, 1);
      }

      // If this was the current file, clear it
      if (this.currentFilePath === filePath) {
        this.currentFilePath = "";
      }
    },

    // File content management
    setFileContent(filePath: string, content: string) {
      this.fileContents[filePath] = content;
      // Don't automatically mark as modified when setting content
      // Modification should only be marked when user actually changes content
    },

    markFileAsModified(filePath: string) {
      this.initializeStore(); // Ensure arrays are initialized
      if (!this.modifiedFiles.includes(filePath)) {
        this.modifiedFiles.push(filePath);
      }
      this.isWorkspaceModified = true;
    },

    markFileAsSaved(filePath: string) {
      this.initializeStore(); // Ensure arrays are initialized
      const index = this.modifiedFiles.indexOf(filePath);
      if (index > -1) {
        this.modifiedFiles.splice(index, 1);
      }
      // Check if any files are still modified
      this.isWorkspaceModified = this.modifiedFiles.length > 0;
    },

    // Utility methods
    isExcelFile(filePath: string): boolean {
      const excelExtensions = [".xlsx", ".xls", ".csv"];
      const extension = filePath
        .toLowerCase()
        .substring(filePath.lastIndexOf("."));
      return excelExtensions.includes(extension);
    },

    getFileName(filePath: string): string {
      return filePath.split(/[/\\]/).pop() || filePath;
    },

    // Data modification management
    markDataAsModified(filePath: string) {
      this.initializeStore(); // Ensure arrays are initialized
      if (!this.dataModifiedFiles.includes(filePath)) {
        this.dataModifiedFiles.push(filePath);
      }
    },

    markDataAsSaved(filePath: string) {
      this.initializeStore(); // Ensure arrays are initialized
      const index = this.dataModifiedFiles.indexOf(filePath);
      if (index > -1) {
        this.dataModifiedFiles.splice(index, 1);
      }
    },

    // File display name management
    setFileDisplayName(filePath: string, displayName: string) {
      this.fileDisplayNames[filePath] = displayName;
    },

    clearFileDisplayName(filePath: string) {
      delete this.fileDisplayNames[filePath];
    },

    // Expanded nodes management
    addExpandedNode(nodePath: string) {
      this.initializeStore(); // Ensure arrays are initialized
      if (!this.expandedNodes.includes(nodePath)) {
        this.expandedNodes.push(nodePath);
      }
    },

    removeExpandedNode(nodePath: string) {
      this.initializeStore(); // Ensure arrays are initialized
      const index = this.expandedNodes.indexOf(nodePath);
      if (index > -1) {
        this.expandedNodes.splice(index, 1);
      }
    },

    toggleExpandedNode(nodePath: string) {
      if (this.expandedNodes.includes(nodePath)) {
        this.removeExpandedNode(nodePath);
      } else {
        this.addExpandedNode(nodePath);
      }
    },

    clearExpandedNodes() {
      this.expandedNodes = [];
    },

    // Force clear cache and reset state
    forceClearCache() {
      // Clear localStorage cache
      localStorage.removeItem("pure-workspace");
      // Reset all state
      this.currentWorkspacePath = "";
      this.currentFilePath = "";
      this.openedFiles = [];
      this.isWorkspaceModified = false;
      this.currentView = "navigation";
      this.fileContents = {};
      this.modifiedFiles = [];
      this.dataModifiedFiles = [];
      this.fileDisplayNames = {};
      this.expandedNodes = [];
    },
  },

  // Persist the store state
  persist: {
    key: "pure-workspace",
    storage: localStorage,
    paths: [
      "currentWorkspacePath",
      "currentFilePath",
      "openedFiles",
      "expandedNodes",
      "fileDisplayNames",
      "dataModifiedFiles",
    ],
  },
});

export function useWorkspaceStoreHook() {
  return useWorkspaceStore(store);
}
