{"name": "LinearRegression", "displayName": "多元线性回归", "description": "使用最小二乘法进行线性回归分析", "category": "linear", "type": "regression", "defaultParams": {"fitIntercept": {"value": true, "type": "boolean", "description": "是否计算截距项", "displayName": "计算截距"}, "positive": {"value": false, "type": "boolean", "description": "是否强制系数为正数", "displayName": "强制正系数"}}, "evaluation": {"splitDataset": {"value": false, "description": "是否划分训练/测试集"}, "trainRatio": {"value": 70, "min": 50, "max": 90, "step": 5, "description": "训练集比例(%)"}, "randomState": {"value": 42, "description": "随机种子"}, "useModelValidation": {"value": false, "description": "是否使用交叉验证"}, "validationType": {"value": "k-fold", "options": ["k-fold", "leave-one-out"], "description": "验证方法"}, "kFolds": {"value": 5, "min": 2, "max": 10, "description": "k折交叉验证的k值"}}, "tips": ["线性回归适用于因变量与自变量之间存在线性关系的情况", "当特征数量较多时，建议开启标准化", "如果数据量较大，可以考虑增加并行作业数以提升计算速度"], "introduction": {"detailedDescription": "线性回归通过最小二乘法寻找最佳拟合直线，假设因变量与自变量之间存在线性关系。算法通过最小化残差平方和来估计回归系数，是统计学习中最基础的方法之一。", "usageTips": ["适用于因变量与自变量之间存在线性关系的情况", "对异常值敏感，建议进行数据预处理", "当特征数量较多时，建议开启标准化", "结果易于解释，系数代表特征的重要性"], "scenarios": "适用于因变量与自变量之间存在线性关系的回归分析，是最基础的回归方法。", "mainParams": [{"name": "fit_intercept", "description": "是否计算截距项"}, {"name": "normalize", "description": "是否对特征进行标准化"}, {"name": "positive", "description": "是否强制系数为正数"}]}}