{"name": "ParticleSwarmOptimization", "displayName": "粒子群优化算法", "description": "通过粒子群优化算法设计配方", "type": "single-target-search", "defaultParams": {"populationSize": {"value": 30, "type": "number", "description": "粒子数量", "displayName": "粒子数量", "min": 10, "max": 100, "step": 5}, "generations": {"value": 40, "type": "number", "description": "种群代数", "displayName": "种群代数", "min": 10, "max": 1000, "step": 10}, "c1": {"value": 2.0, "type": "number", "description": "个体学习因子", "displayName": "个体学习因子", "min": 0.5, "max": 4.0, "step": 0.1}, "c2": {"value": 2.0, "type": "number", "description": "社会学习因子", "displayName": "社会学习因子", "min": 0.5, "max": 4.0, "step": 0.1}, "w": {"value": 0.7, "type": "number", "description": "惯性权重", "displayName": "惯性权重", "min": 0.1, "max": 1.0, "step": 0.05}, "maxIter": {"value": 100, "type": "number", "description": "最大迭代次数", "displayName": "最大迭代次数", "min": 20, "max": 1000, "step": 20}, "maxTime": {"value": 10000, "type": "number", "description": "最大时间(秒)", "displayName": "最大时间(秒)", "min": 10, "max": 10000, "step": 100}}, "tips": ["粒子群优化算法是一种基于群体智能的优化算法", "PSO适用于连续优化问题，具有收敛速度快的特点", "个体学习因子和社会学习因子影响算法的探索与开发能力", "惯性权重控制粒子的搜索范围，较大的权重有利于全局搜索"], "introduction": {"detailedDescription": "粒子群优化算法（PSO）是一种基于群体智能的优化算法，通过模拟鸟群觅食行为来寻找最优解。每个粒子代表一个候选解，通过个体最优位置和全局最优位置来更新速度和位置", "usageTips": ["PSO适用于连续优化问题，具有收敛速度快的特点", "个体学习因子和社会学习因子影响算法的探索与开发能力", "惯性权重控制粒子的搜索范围，较大的权重有利于全局搜索", "粒子数量影响算法的搜索能力和计算复杂度"], "scenarios": "适用于连续优化问题，如参数优化、函数优化、工程设计等", "mainParams": [{"name": "population_size", "description": "粒子数量"}, {"name": "max_iter", "description": "最大迭代次数"}, {"name": "c1", "description": "个体学习因子"}, {"name": "c2", "description": "社会学习因子"}, {"name": "w", "description": "惯性权重"}]}}