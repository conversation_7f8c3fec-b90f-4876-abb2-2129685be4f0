from flask import current_app, Blueprint, send_file, make_response, jsonify, request
from app.extensions.socketio import socketio
from app.utils.params import camel_to_snake, snake_to_camel
from app.services.ProgressService import ProgressService
from app.types.flaskapp import FlaskWithExecutor
from app.services.DBService import DBService
from app.services.FileService import FileService
import logging
from typing import cast
from app.routers.models.utils import read_data, transform_model_info
import pandas as pd, numpy as np
from app.services.RegressionService import RegressionService
from app.types.model_request import ModelConfig
from collections import OrderedDict
import uuid

logger = logging.getLogger("socketio")
common_bp = Blueprint("common", __name__)


@socketio.on('get_model_info')
def get_model_info(data: dict):
    data = camel_to_snake(data) # type: ignore
    uid: str = data["uid"]
    is_rabbitmq_ready = data.get("is_rabbitmq_ready")
    if is_rabbitmq_ready is None:
        is_rabbitmq_ready = cast(FlaskWithExecutor, current_app).is_rabbitmq_ready
    try:
        if uid is not None:
            ProgressService.report(uid)
            return_data = {
                "uid": uid,
                "is_rabbitmq_ready": is_rabbitmq_ready
            }
            return {"code": 200, "msg": "success", "data": snake_to_camel(return_data)}
        else:
            return {"code": 400, "msg": "uid is None", "data": None}
    except Exception as e:
        logger.error(f"handle_model_info: 模型信息获取失败: {e}")
        return {"code": 500, "msg": f"模型信息获取失败: {e}", "data": None}
    

@common_bp.route("/download_models", methods=["POST"])
def download_models():
    post_data: dict = camel_to_snake(request.json) # type: ignore
    uids = post_data.get("uids")
    try:
        if uids is not None:
            if len(uids) == 1:
                # 单个文件下载
                uid = uids[0]
                model_path = FileService.get_model_path(uid)
                if model_path.exists():
                    return send_file(
                        model_path,
                        as_attachment=True,
                        download_name=f"{uid}.joblib",
                        mimetype='application/octet-stream'
                    )
                else:
                    return make_response(jsonify({"code": 404, "msg": "模型文件不存在", "data": None}), 404)
            else:
                # 多个文件打包下载
                import zipfile
                import io
                import tempfile
                
                memory_file = io.BytesIO()
                with zipfile.ZipFile(memory_file, 'w', zipfile.ZIP_DEFLATED) as zf:
                    for uid in uids:
                        model_path = FileService.get_model_path(uid)
                        if model_path.exists():
                            zf.write(model_path, f"{uid}.joblib")
                
                memory_file.seek(0)
                return send_file(
                    io.BytesIO(memory_file.read()),
                    as_attachment=True,
                    download_name="models.zip",
                    mimetype='application/zip'
                )
        else:
            return make_response(jsonify({"code": 400, "msg": "invalid uid", "data": None}), 400)
    except Exception as e:
        logger.error(f"download_model: 模型下载失败: {e}")
        return make_response(jsonify({"code": 500, "msg": f"模型下载失败: {e}", "data": None}), 500)

@common_bp.route("/get_model_list", methods=["GET"])
def get_model_list():
    query_params = camel_to_snake(request.args) # type: ignore
    model_list = DBService.get_model_list(query_params)
    compact_model_list = []
    try:
        for model in model_list:
            tmp_dict = model.to_dict()
            tmp_dict.pop("result")
            params = tmp_dict.pop("params")
            y_train = pd.DataFrame(params.get("y_train", {}))
            y_test = pd.DataFrame(params.get("y_test", {}))
            y = pd.concat([y_train, y_test])
            tmp_dict["target_values"] = y.values.tolist()
            tmp_dict["target_name"] = y.columns.tolist()
            x_train = pd.DataFrame(params.get("x_train", {}))
            x_test = pd.DataFrame(params.get("x_test", {}))
            x = pd.concat([x_train, x_test])
            tmp_dict["feature_ranges"] = x.describe().loc[["min", "max"]].to_dict(into=OrderedDict)
            compact_model_list.append(tmp_dict)
        return make_response(jsonify({"code": 200, "msg": "success", "data": snake_to_camel(compact_model_list)}), 200)
    except Exception as e:
        logger.error(f"get_model_list: 模型列表获取失败: {e}")
        return make_response(jsonify({"code": 500, "msg": f"模型列表获取失败: {e}", "data": None}), 500)

@common_bp.route("/predict", methods=["POST"])
def predict():
    post_data: dict = request.json # type: ignore
    model_uid = post_data.get("uid")
    test_data = post_data.get("data")
    try:
        test_data = pd.DataFrame.from_dict(test_data)
        if model_uid is not None:
            model_file = FileService.load_model(model_uid)
            predictions = pd.DataFrame(index=test_data.index)
            for yname in model_file['model'].keys():
                model = model_file['model'][yname]
                scaler = model_file['scaler'][yname]
                feature_names = model_file['feature_names'][yname]
                if len(list(set(feature_names).difference(set(test_data.columns)))) > 0:
                    raise ValueError(f"测试数据缺少特征: {list(set(feature_names).difference(set(test_data.columns)))}")
                test_data_ = test_data[feature_names].copy()
                test_data_[:] = scaler.transform(test_data_)
                predictions[yname] = model.predict(test_data_)
            predictions = predictions.to_dict(into=OrderedDict)
        return make_response(jsonify({"code": 200, "msg": "success", "data": {
            "predictions": predictions,
        }}), 200)
    except Exception as e:
        logger.error(f"predict: 预测失败: {e}")
        return make_response(jsonify({"code": 500, "msg": f"预测失败: {e}", "data": None}), 500)
    
@common_bp.route("/delete_models", methods=["POST"])
def delete_models():
    post_data: dict = request.json # type: ignore
    model_uids = post_data.get("uids")
    try:
        DBService.delete_models(model_uids)
        FileService.delete_models(model_uids)
        return make_response(jsonify({"code": 200, "msg": "success", "data": None}), 200)
    except Exception as e:
        logger.error(f"delete_models: 模型删除失败: {e}")
        return make_response(jsonify({"code": 500, "msg": f"模型删除失败: {e}", "data": None}), 500)

@common_bp.route("/get_model_info", methods=["POST"])
def get_model_info():
    post_data: dict = camel_to_snake(request.json) # type: ignore
    uid = post_data.get("uid")
    if uid is not None:
        model_info = DBService.get_model_info(uid)
        result = transform_model_info(model_info.result)
        return make_response(jsonify({"code": 200, "msg": "success", "data": snake_to_camel(result)}), 200)
    else:
        return make_response(jsonify({"code": 400, "msg": "invalid uid", "data": None}), 400)

@common_bp.route("/get_model_params", methods=["POST"])
def get_model_params():
    post_data: dict = camel_to_snake(request.json) # type: ignore
    uid = post_data.get("uid")
    if uid is not None:
        model_params = DBService.get_model_params(uid)
        return make_response(jsonify({"code": 200, "msg": "success", "data": snake_to_camel(model_params)}), 200)
    else:
        return make_response(jsonify({"code": 400, "msg": "invalid uid", "data": None}), 400)

@common_bp.route("/upload_model", methods=["POST"])
def upload_model():
    """
    上传模型文件
    
    Returns:
        上传结果
    """
    try:
        # 检查是否有文件上传
        if 'file' not in request.files:
            return make_response(jsonify({"code": 400, "msg": "没有文件上传", "data": None}), 400)
        
        uploaded_file = request.files['file']
        
        # 检查文件名
        if uploaded_file.filename == '':
            return make_response(jsonify({"code": 400, "msg": "没有选择文件", "data": None}), 400)
        
        # 检查文件扩展名
        if not uploaded_file.filename.endswith('.joblib'):
            return make_response(jsonify({"code": 400, "msg": "只支持.joblib格式的模型文件", "data": None}), 400)
        
        model_id = str(uuid.uuid4())
        
        # 保存文件
        model_path = FileService.get_model_path(model_id)
        uploaded_file.save(model_path)
        
        # 验证文件是否可以正确加载（可选，用于验证文件完整性）
        try:
            # 尝试加载模型验证文件完整性
            model_file = FileService.load_model(model_id)
            model_config = ModelConfig.from_dict(model_file["info"]["model_params"])
            model_task = DBService.create_model_task(model_id, "upload", model_config, status="completed", progress=100, result=model_file["info"], error=None)
            model_id = DBService.add_model_task(model_task)
            return make_response(jsonify({
                "code": 200, 
                "msg": "模型上传成功", 
                "data": {
                    "model_id": model_id,
                }
            }), 200)
            
        except Exception as load_error:
            # 如果加载失败，删除已保存的文件
            FileService.delete_model(model_id)
            logger.error(f"upload_models: 模型文件验证失败: {load_error}")
            return make_response(jsonify({
                "code": 400, 
                "msg": "模型文件格式错误或损坏，请检查文件", 
                "data": None
            }), 400)
            
    except Exception as e:
        logger.error(f"upload_models: 模型上传失败: {e}")
        return make_response(jsonify({"code": 500, "msg": f"模型上传失败: {e}", "data": None}), 500)
    