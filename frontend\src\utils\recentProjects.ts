import { ElMessage } from "element-plus";

// 最近项目接口定义
export interface RecentProject {
  path: string;
  name: string;
  lastOpened: string;
  type: 'file' | 'directory';
}

// 最近项目管理器
export class RecentProjectsManager {
  private static instance: RecentProjectsManager;
  private maxRecentProjects = 10; // 最多保存10个最近项目
  
  private constructor() {}
  
  public static getInstance(): RecentProjectsManager {
    if (!RecentProjectsManager.instance) {
      RecentProjectsManager.instance = new RecentProjectsManager();
    }
    return RecentProjectsManager.instance;
  }

  // 添加最近项目
  public async addRecentProject(filePath: string, type: 'file' | 'directory'): Promise<void> {
    try {
      console.log(`开始添加最近项目: ${filePath}, 类型: ${type}`);
      
      // 获取当前列表
      const recentProjects = await this.getRecentProjects();
      console.log("当前最近项目列表:", recentProjects);
      
      const fileName = this.getFileName(filePath);
      
      // 移除已存在的同路径项目
      const filteredProjects = recentProjects.filter(project => project.path !== filePath);
      console.log("过滤后的项目列表:", filteredProjects);
      
      // 添加新项目到开头
      const newProject: RecentProject = {
        path: filePath,
        name: fileName,
        lastOpened: new Date().toISOString(),
        type
      };
      
      filteredProjects.unshift(newProject);
      
      // 限制最大数量
      const limitedProjects = filteredProjects.slice(0, this.maxRecentProjects);
      console.log(`限制数量后(${this.maxRecentProjects}个):`, limitedProjects);
      
      // 保存到AppData
      const result = await this.saveRecentProjects(limitedProjects);
      console.log("保存结果:", result);
      
      if (result.success) {
        console.log("最近项目保存成功");
      } else {
        console.error("保存失败:", result.message || "未知错误");
        // 尝试重试一次
        console.log("尝试重试保存...");
        const retryResult = await this.saveRecentProjects(limitedProjects);
        console.log("重试结果:", retryResult);
      }
    } catch (error) {
      console.error("添加最近项目失败:", error);
      throw error; // 重新抛出错误，让调用者知道失败
    }
  }

  // 获取最近项目列表
  public async getRecentProjects(): Promise<RecentProject[]> {
    try {
      console.log("开始获取最近项目列表");
      
      if (!window.ipcRenderer) {
        console.error("IPC渲染器不可用");
        return [];
      }
      
      const result = await window.ipcRenderer.invoke("recentFiles:get");
      console.log("IPC获取最近项目结果:", result);
      
      if (!result) {
        console.error("IPC返回空结果");
        return [];
      }
      
      if (result.success && Array.isArray(result.files)) {
        console.log(`成功获取 ${result.files.length} 个项目`);
        return result.files;
      } else {
        console.error("IPC返回格式错误:", result);
        return [];
      }
    } catch (error) {
      console.error("获取最近项目失败:", error);
      return [];
    }
  }

  // 保存最近项目列表
  private async saveRecentProjects(projects: RecentProject[]): Promise<any> {
    try {
      console.log(`准备保存 ${projects.length} 个项目到IPC`);
      
      if (!window.ipcRenderer) {
        console.error("IPC渲染器不可用");
        return { success: false, message: "IPC渲染器不可用" };
      }
      
      const result = await window.ipcRenderer.invoke("recentFiles:save", projects);
      console.log("IPC保存最近项目结果:", result);
      
      if (!result) {
        console.error("IPC返回空结果");
        return { success: false, message: "IPC返回空结果" };
      }
      
      return result;
    } catch (error) {
      console.error("保存最近项目失败:", error);
      return { success: false, error: error };
    }
  }

  // 移除最近项目
  public async removeRecentProject(filePath: string): Promise<void> {
    try {
      const recentProjects = await this.getRecentProjects();
      const filteredProjects = recentProjects.filter(project => project.path !== filePath);
      await this.saveRecentProjects(filteredProjects);
    } catch (error) {
      console.error("移除最近项目失败:", error);
    }
  }

  // 清空最近项目
  public async clearRecentProjects(): Promise<void> {
    try {
      await this.saveRecentProjects([]);
    } catch (error) {
      console.error("清空最近项目失败:", error);
    }
  }

  // 验证最近项目是否仍然存在
  public async validateRecentProjects(): Promise<RecentProject[]> {
    try {
      const recentProjects = await this.getRecentProjects();
      const validProjects: RecentProject[] = [];
      
      for (const project of recentProjects) {
        const exists = await window.ipcRenderer.invoke("fs:validatePath", project.path);
        if (exists.exists) {
          validProjects.push(project);
        }
      }
      
      if (validProjects.length !== recentProjects.length) {
        await this.saveRecentProjects(validProjects);
      }
      return validProjects;
    } catch (error) {
      console.error("验证最近项目失败:", error);
      return [];
    }
  }

  // 获取文件名
  private getFileName(filePath: string): string {
    return filePath.split(/[/\\]/).pop() || filePath;
  }


}

// 导出单例实例
export const recentProjectsManager = RecentProjectsManager.getInstance();
