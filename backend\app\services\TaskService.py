from app.types.model_request import ModelConfig
from sklearn.base import BaseEstimator
from flask import current_app
from typing import cast
from functools import partial
from app.types.flaskapp import FlaskWithExecutor
from app.services.DBService import DBService
from app.services.FileService import FileService
from app.services.RabbitmqService import ProgressReporter, setup_rabbitmq
from app.services.RegressionService import RegressionService, Eval, ModelInfo, ModelFile
from datetime import datetime
from typing import Optional, Dict, Any, List
import json
from app.utils.Regression import Regression
import time
import logging
import traceback
from app.types.search_request import SearchConfig
from app.services.SearchService import SearchService, SearchServiceMultiple
from app.services.RegressionService import ModelFile
from app.routers.models.utils import transform_model_info
from sklearn.preprocessing import StandardScaler
from app.types.services import ModelTask
import numpy as np

logger = logging.getLogger()

def build_model_task(model_config: ModelConfig) -> None:

    db_updater = partial(
        DBService.update_model_task, 
        uid=model_config.uid, 
        updated_at=datetime.now()
    )
    if model_config.is_rabbitmq_ready:
        progress_reporter = ProgressReporter(model_config.uid)
        reporter = partial(
            progress_reporter.report_model_build_task, 
            body={'uid': model_config.uid},
            status="running",
            message="model build running"
        )
    else:
        progress_reporter = None
        reporter = partial(
            lambda progress, status, message: None
        )

    def __callable__(stage: str="", progress: float=0, sub_progress: str="", yname: str="", current_percent: float=0, step: float=1) -> None:
        progress = progress*step + current_percent
        print(stage, progress, yname)
        if stage == "train":
            db_updater(status="running", progress=progress)
            reporter(progress=str(progress), status="running", message=f"[{yname}]模型训练中")
        elif stage == "loocv":
            db_updater(status="running", progress=progress)
            reporter(progress=str(progress), status="running", message=f"[{yname}]留一法交叉验证中:{sub_progress}")
        elif stage == "cv":
            db_updater(status="running", progress=progress)
            reporter(progress=str(progress), status="running", message=f"[{yname}]交叉验证中:{sub_progress}")
        elif stage == "test":
            db_updater(status="running", progress=progress)
            reporter(progress=str(progress), status="running", message=f"[{yname}]测试集评估中:{sub_progress}")
        elif stage == "optimize":
            db_updater(status="running", progress=progress)
            reporter(progress=str(progress), status="running", message=f"[{yname}]参数优化中:{sub_progress}")
        elif stage == "completed":
            db_updater(status="completed", progress=progress)
            reporter(progress=str(progress), status="completed", message=f"[{yname}]模型训练完成")
        elif stage == "failed":
            db_updater(status="failed", progress=progress)
            reporter(progress=str(progress), status="failed", message=f"[{yname}]模型训练失败")
        elif stage == "feature_analysis":
            db_updater(status="running", progress=progress)
            reporter(progress=str(progress), status="running", message=f"[{yname}]特征分析中:{sub_progress}")

    db_updater(status="running", progress="start")
    alg, model_config.alg_param = RegressionService.check_model_config(model_config)
    info: ModelInfo = RegressionService.init_model_info(model_config)
    file: ModelFile = RegressionService.init_model_file(model_config)
    steps: int = RegressionService.cal_steps(model_config)
    try:
        step = 0
        for yname in model_config.y_train.columns:
            scaler = StandardScaler()
            x_train = model_config.x_train.copy()
            x_train[:] = scaler.fit_transform(model_config.x_train)
            file["scaler"][yname] = scaler
            file["feature_names"][yname] = scaler.feature_names_in_
            y_train = model_config.y_train[yname]
            params = model_config.alg_param.copy()

            if model_config.optimize:
                optimize = RegressionService.optimize(
                    alg=alg,
                    x_train=x_train.copy(),
                    y_train=y_train.copy(),
                    params=params,
                    __callable__=partial(__callable__, yname=yname, current_percent=step/steps*100, step=1/steps)
                )
                info["optimized_result"][yname] = optimize["optimized_result"].to_dict(orient="records")
                info["optimized_params"][yname] = optimize["optimized_params"]
                params = optimize["optimized_params"].copy()
                step += 1

            if True or model_config.train: # always train
                model, train_eval = RegressionService.train(
                    alg=alg,
                    x_train=x_train.copy(),
                    y_train=y_train.copy(),
                    params=params,
                    __callable__=partial(__callable__, yname=yname, current_percent=step/steps*100, step=1/steps)
                )
                file["model"][yname] = model
                info["eval"][yname]["train"] = train_eval
                info["feature_analysis"][yname] = RegressionService.feature_analysis(
                    model=model,
                    alg=alg,
                    x_train=x_train,
                    y_train=y_train,
                    feature_names=file["feature_names"][yname],
                    params=params,
                    shap=model_config.shap,
                    __callable__=partial(__callable__, yname=yname, current_percent=step/steps*100, step=1/steps))
                step += 1
            
            if model_config.cv:
                cv_eval: Eval = RegressionService.cross_validation(
                    cv=model_config.cv,
                    alg=alg,
                    x_train=x_train.copy(),
                    y_train=y_train.copy(),
                    params=params,
                    __callable__=partial(__callable__, yname=yname, current_percent=step/steps*100, step=1/steps)
                )
                info["eval"][yname]["cv"] = cv_eval
                step += 1

            if model_config.loocv:
                loocv_eval: Eval = RegressionService.leave_one_out(
                    alg=alg,
                    x_train=x_train.copy(),
                    y_train=y_train.copy(),
                    params=params,
                    __callable__=partial(__callable__, yname=yname, current_percent=step/steps*100, step=1/steps)
                )
                info["eval"][yname]["loocv"] = loocv_eval
                step += 1
            
            if model_config.test:
                x_test = model_config.x_test.copy()
                x_test[:] = scaler.transform(model_config.x_test)
                y_test = model_config.y_test[yname].copy()
                test_eval: Eval = RegressionService.test(
                    model=model,
                    x_test=x_test,
                    y_test=y_test,
                    __callable__=partial(__callable__, yname=yname, current_percent=step/steps*100, step=1/steps)
                )
                info["eval"][yname]["test"] = test_eval
                step += 1

        file["info"] = info
        db_updater(status="completed", progress=100, result=info)
        FileService.save_model(file, str(model_config.uid))
        if model_config.is_rabbitmq_ready and progress_reporter is not None:
            progress_reporter.report_model_build_task(transform_model_info(info), "100", 'completed', "model build completed")
            progress_reporter.close()
    except Exception as e:
        error_traceback = traceback.format_exc()
        db_updater(status="failed", progress=step/steps*100, error=str(e))
        logger.error(f"build_model_task: model build failed: {e}\nTraceback:\n{error_traceback}")
        if model_config.is_rabbitmq_ready and progress_reporter is not None:
            reporter(progress=step/steps*100, status="failed", message="model build failed")
            progress_reporter.close()
    
def update_model_task(model_config: ModelConfig, old_model_task: ModelTask, model_file: ModelFile) -> None:
    db_updater = partial(
        DBService.update_model_task, 
        uid=model_config.uid, 
        updated_at=datetime.now()
    )
    if model_config.is_rabbitmq_ready:
        progress_reporter = ProgressReporter(model_config.uid)
        reporter = partial(
            progress_reporter.report_model_build_task, 
            body={'uid': model_config.uid},
            status="running",
            message="model build running"
        )
    else:
        progress_reporter = None
        reporter = partial(
            lambda progress, status, message: None
        )

    def __callable__(stage: str="", progress: float=0, sub_progress: str="", yname: str="", current_percent: float=0, step: float=1) -> None:
        progress = progress*step + current_percent
        print(stage, progress, yname)
        if stage == "train":
            db_updater(status="running", progress=progress)
            reporter(progress=str(progress), status="running", message=f"[{yname}]模型训练中")
        elif stage == "loocv":
            db_updater(status="running", progress=progress)
            reporter(progress=str(progress), status="running", message=f"[{yname}]留一法交叉验证中:{sub_progress}")
        elif stage == "cv":
            db_updater(status="running", progress=progress)
            reporter(progress=str(progress), status="running", message=f"[{yname}]交叉验证中:{sub_progress}")
        elif stage == "test":
            db_updater(status="running", progress=progress)
            reporter(progress=str(progress), status="running", message=f"[{yname}]测试集评估中:{sub_progress}")
        elif stage == "optimize":
            db_updater(status="running", progress=progress)
            reporter(progress=str(progress), status="running", message=f"[{yname}]参数优化中:{sub_progress}")
        elif stage == "completed":
            db_updater(status="completed", progress=progress)
            reporter(progress=str(progress), status="completed", message=f"[{yname}]模型训练完成")
        elif stage == "failed":
            db_updater(status="failed", progress=progress)
            reporter(progress=str(progress), status="failed", message=f"[{yname}]模型训练失败")
        elif stage == "feature_analysis":
            db_updater(status="running", progress=progress)
            reporter(progress=str(progress), status="running", message=f"[{yname}]特征分析中:{sub_progress}")
    
    db_updater(status="running", progress="start")
    info: ModelInfo = model_file["info"].copy()
    # 模型开启优化且参数与旧模型不同，需要开启优化
    re_optimize = (model_config.alg_param != old_model_task.params["alg_param"]) and model_config.optimize
    # 参数优化后，模型需要重新训练
    re_train = re_optimize
    # 参数优化后，若已有cv/loo/test，模型需要重新cv/loo/test
    re_cv = ((model_config.cv != old_model_task.params["cv"]) and model_config.cv) or (model_config.cv and re_optimize)
    re_loocv = ((model_config.loocv != old_model_task.params["loocv"]) and model_config.loocv) or (model_config.loocv and re_optimize)
    re_test = ((model_config.test != old_model_task.params["test"]) and model_config.test) or (model_config.test and re_optimize)
    single_shap = (model_config.shap and not old_model_task.params["shap"])
    alg, model_config.alg_param = RegressionService.check_model_config(model_config)
    file: ModelFile = model_file
    steps: int = (np.array([re_optimize, re_train, re_cv, re_loocv, re_test, single_shap]).astype(int).sum() + 1) * len(model_config.y_train.columns)
    try:
        step = 0
        for yname in model_config.y_train.columns:
            scaler = model_file["scaler"][yname]
            x_train = model_config.x_train.copy()
            x_train[:] = scaler.fit_transform(model_config.x_train)
            y_train = model_config.y_train[yname]
            params = model_config.alg_param.copy()

            if re_optimize:
                optimize = RegressionService.optimize(
                    alg=alg,
                    x_train=x_train.copy(),
                    y_train=y_train.copy(),
                    params=params,
                    __callable__=partial(__callable__, yname=yname, current_percent=step/steps*100, step=1/steps)
                )
                info["optimized_result"][yname] = optimize["optimized_result"].to_dict(orient="records")
                info["optimized_params"][yname] = optimize["optimized_params"]
                params = optimize["optimized_params"].copy()
                step += 1
            
            if re_train: # 参数优化后，重新训练
                model, train_eval = RegressionService.train(
                    alg=alg,
                    x_train=x_train.copy(),
                    y_train=y_train.copy(),
                    params=params,
                    __callable__=partial(__callable__, yname=yname, current_percent=step/steps*100, step=1/steps)
                )
                file["model"][yname] = model
                info["eval"][yname]["train"] = train_eval
                info["feature_analysis"][yname] = RegressionService.feature_analysis(
                    model=model,
                    alg=alg,
                    x_train=x_train,
                    y_train=y_train,
                    feature_names=file["feature_names"][yname],
                    params=params,
                    shap=model_config.shap,
                    __callable__=partial(__callable__, yname=yname, current_percent=step/steps*100, step=1/steps))
                step += 1
            else:
                model = file["model"][yname]
            
            if re_cv:
                cv_eval: Eval = RegressionService.cross_validation(
                    cv=model_config.cv,
                    alg=alg,
                    x_train=x_train.copy(),
                    y_train=y_train.copy(),
                    params=params,
                    __callable__=partial(__callable__, yname=yname, current_percent=step/steps*100, step=1/steps)
                )
                info["eval"][yname]["cv"] = cv_eval
                step += 1
            
            if re_loocv:
                loocv_eval: Eval = RegressionService.leave_one_out(
                    alg=alg,
                    x_train=x_train.copy(),
                    y_train=y_train.copy(),
                    params=params,
                    __callable__=partial(__callable__, yname=yname, current_percent=step/steps*100, step=1/steps)
                )
                info["eval"][yname]["loocv"] = loocv_eval
                step += 1
            
            if re_test:
                x_test = model_config.x_test.copy()
                x_test[:] = scaler.transform(model_config.x_test)
                y_test = model_config.y_test[yname].copy()
                test_eval: Eval = RegressionService.test(
                    model=model,
                    x_test=x_test,
                    y_test=y_test,
                    __callable__=partial(__callable__, yname=yname, current_percent=step/steps*100, step=1/steps)
                )
                info["eval"][yname]["test"] = test_eval
                step += 1
            
            if single_shap:
                info["feature_analysis"][yname] = RegressionService.feature_analysis(
                    model=model,
                    alg=alg,
                    x_train=x_train,
                    y_train=y_train,
                    feature_names=file["feature_names"][yname],
                    params=params,
                    shap=model_config.shap,
                    __callable__=partial(__callable__, yname=yname, current_percent=step/steps*100, step=1/steps))
                step += 1

        info["abc"] = 1
        file["info"] = info
        db_updater(status="completed", progress=100, result=info)
        FileService.save_model(file, str(model_config.uid))
        if model_config.is_rabbitmq_ready and progress_reporter is not None:
            progress_reporter.report_model_build_task(transform_model_info(info), "100", 'completed', "model build completed")
            progress_reporter.close()
    except Exception as e:
        error_traceback = traceback.format_exc()
        db_updater(status="failed", progress=step/steps*100, error=str(e))
        logger.error(f"update_model_task: model update failed: {e}\nTraceback:\n{error_traceback}")
        if model_config.is_rabbitmq_ready and progress_reporter is not None:
            reporter(progress=step/steps*100, status="failed", message="model update failed")
            progress_reporter.close()

def build_search_task(search_config: SearchConfig, model_file: ModelFile) -> None:
    
    db_updater = partial(
        DBService.update_search_task, 
        uid=search_config.uid, 
        updated_at=datetime.now()
    )
    db_updater(status="running", progress="start")
    if search_config.is_rabbitmq_ready:
        progress_reporter = ProgressReporter(search_config.uid)
        reporter = partial(
            progress_reporter.report_search_task,
            status="running",
            message="search task running"
        )
    else:
        progress_reporter = None
        reporter = partial(
            lambda progress, status, message: None
        )
    try:
        info: Dict[str, Any] = model_file["info"]
        model: BaseEstimator = model_file["model"][search_config.target_name]
        scaler: StandardScaler = model_file["scaler"][search_config.target_name]
        if hasattr(scaler, "feature_names_in_"):
            feature_names = scaler.feature_names_in_
        elif hasattr(model, "feature_names_in_"):
            feature_names = model.feature_names_in_
        else:
            feature_names = info["model_params"]["x_train"].columns.tolist()
        alg, search_config.alg_param, search_config.feature_ranges = SearchService.check_search_config(search_config, feature_names)

        def __callable__(body: dict={}, result: dict={}, progress: float=0, status: str=None, message: str=None) -> None:
            db_updater(result=result,status=status, progress=progress)
            reporter(body=body, progress=str(progress), status=status, message=message)

        SearchService.run_optimize(alg, model, scaler, search_config, __callable__)
        
        db_updater(status="completed", progress="completed")
        if search_config.is_rabbitmq_ready and progress_reporter is not None:
            progress_reporter.report_search_task(body={}, progress=100, status="completed", message="search task completed")
            progress_reporter.close()
    except Exception as e:
        error_traceback = traceback.format_exc()
        db_updater(status="failed", progress="failed", error=str(e))
        logger.error(f"build_search_task: search task failed: {e}\nTraceback:\n{error_traceback}")
        if search_config.is_rabbitmq_ready and progress_reporter is not None:
            progress_reporter.report_search_task(body={}, progress=None, status="failed", message="search task failed")
            progress_reporter.close()

def build_search_task_multiple(search_config: SearchConfig, model_files: List[ModelFile]) -> None:
        
    db_updater = partial(
        DBService.update_search_task, 
        uid=search_config.uid, 
        updated_at=datetime.now()
    )
    db_updater(status="running", progress="start")
    if search_config.is_rabbitmq_ready:
        progress_reporter = ProgressReporter(search_config.uid)
        reporter = partial(
            progress_reporter.report_search_task,
            status="running",
            message="search task running"
        )
    else:
        progress_reporter = None
        reporter = partial(
            lambda progress, status, message: None
        )
    def __callable__(body: dict={}, result: dict={}, progress: float=0, status: str=None, message: str=None) -> None:
        db_updater(result=result, status=status, progress=progress)
        reporter(body=body, progress=str(progress), status=status, message=message)
    try:
        alg, search_config.alg_param = SearchServiceMultiple.check_search_config_multiple(search_config)
        data_pack = []
        for target_name, target_value, model_uid in zip(
            search_config.target_name, search_config.target_value, search_config.model_uid
        ):
            data_pack.append({
                "target_name": target_name,
                "target_value": target_value,
                "model_file": model_files[model_uid],
                "model_uid": model_uid
            })
        SearchServiceMultiple.run_optimize_multiple(alg, search_config, data_pack, search_config.feature_ranges, __callable__)
        
        db_updater(status="completed", progress="completed")
        if search_config.is_rabbitmq_ready and progress_reporter is not None:
            progress_reporter.report_search_task(body={}, progress=100, status="completed", message="search task completed")
            progress_reporter.close()
    except Exception as e:
        error_traceback = traceback.format_exc()
        db_updater(status="failed", progress="failed", error=str(e))
        logger.error(f"build_search_task_multiple: search task failed: {e}\nTraceback:\n{error_traceback}")
        if search_config.is_rabbitmq_ready and progress_reporter is not None:
            progress_reporter.report_search_task(body={}, progress=None, status="failed", message="search task failed")
            progress_reporter.close()

class TaskService:

    @staticmethod
    def submit_update_model_task(model_config: ModelConfig, old_model_task: ModelTask, model_file: ModelFile) -> None:
        """
        提交模型更新任务
        model_config:
            uid: str
            x_train: Union[pd.DataFrame, pd.Series]
            y_train: Union[pd.DataFrame, pd.Series]
            x_test: Optional[Union[pd.DataFrame, pd.Series]]
            y_test: Optional[Union[pd.DataFrame, pd.Series]]
            cv: Optional[Union[bool, int]]
            loocv: Optional[bool]
            test: Optional[bool]
            asynchronous: Optional[bool]
            alg_name: str
            alg_param: Optional[Dict[str, Any]]
        old_model_task:
            uid: str
            params: Optional[Dict[str, Any]]
            status: str
            progress: Optional[int]
            result: Optional[Dict[str, Any]]
            error: Optional[str]
            is_rabbitmq_ready: bool
            asynchronous: bool
            source: str
            origin_params: Optional[Dict[str, Any]]
        """
        if model_config.asynchronous:
            cast(FlaskWithExecutor, current_app).executor.submit(update_model_task, model_config, old_model_task, model_file)
        else:
            update_model_task(model_config, old_model_task, model_file)

    @staticmethod
    def submit_model_task(model_config: ModelConfig) -> None:
        """
        提交模型任务
        model_config:
            uid: str
            x_train: Union[pd.DataFrame, pd.Series]
            y_train: Union[pd.DataFrame, pd.Series]
            x_test: Optional[Union[pd.DataFrame, pd.Series]]
            y_test: Optional[Union[pd.DataFrame, pd.Series]]
            cv: Optional[Union[bool, int]]
            loocv: Optional[bool]
            test: Optional[bool]
            asynchronous: Optional[bool]
            alg_name: str
            alg_param: Optional[Dict[str, Any]]
        """
        if model_config.asynchronous:
            cast(FlaskWithExecutor, current_app).executor.submit(build_model_task, model_config)
        else:
            build_model_task(model_config)
        
    @staticmethod
    def submit_search_task(search_config: SearchConfig, model_file: ModelFile) -> None:
        """
        提交搜索任务
        search_config:
            uid: str
            model_uid: str
            target_name: str
            target_value: float
            feature_ranges: dict
            alg_name: str
            alg_param: Optional[Dict[str, Any]]
        """
        cast(FlaskWithExecutor, current_app).executor.submit(build_search_task, search_config, model_file)
    
    @staticmethod
    def submit_search_task_multiple(search_config: SearchConfig, model_files: List[ModelFile]) -> None:
        """
        提交多目标搜索任务
        search_config:
            uid: str
            model_uid: List[str]
            target_name: List[str]
            target_value: List[float]
            feature_ranges: List[dict]
            alg_name: str
            alg_param: Optional[Dict[str, Any]]
        """
        cast(FlaskWithExecutor, current_app).executor.submit(build_search_task_multiple, search_config, model_files)