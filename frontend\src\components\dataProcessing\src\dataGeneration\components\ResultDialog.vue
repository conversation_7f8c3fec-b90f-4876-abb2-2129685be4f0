<template>
  <el-dialog
    v-model="isVisible"
    title="虚拟样本生成结果"
    width="90%"
    append-to-body
    class="result-dialog"
  >
    <div class="result-content">
      <!-- 结果概览卡片 -->
      <div class="result-overview">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-card class="result-card">
              <div class="card-content">
                <div class="card-icon">
                  <el-icon size="24" color="#409EFF">
                    <DataAnalysis />
                  </el-icon>
                </div>
                <div class="card-info">
                  <div class="card-value">{{ resultData?.metadata?.originalRowCount || 0 }}</div>
                  <div class="card-label">原始数据行数</div>
                </div>
              </div>
            </el-card>
          </el-col>
          
          <el-col :span="8">
            <el-card class="result-card">
              <div class="card-content">
                <div class="card-icon">
                  <el-icon size="24" color="#67C23A">
                    <Plus />
                  </el-icon>
                </div>
                <div class="card-info">
                  <div class="card-value">{{ getGeneratedSampleCount() }}</div>
                  <div class="card-label">生成样本数量</div>
                </div>
              </div>
            </el-card>
          </el-col>
          
          <el-col :span="8">
            <el-card class="result-card">
              <div class="card-content">
                <div class="card-icon">
                  <el-icon size="24" color="#F56C6C">
                    <Setting />
                  </el-icon>
                </div>
                <div class="card-info">
                  <div class="card-value">{{ getMethodDisplayName(resultData?.metadata?.method) }}</div>
                  <div class="card-label">使用的方法</div>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <!-- 约束条件信息 -->
      <div class="constraints-info" v-if="resultData?.constraints?.length">
        <el-card class="info-card">
          <template #header>
            <div class="card-header">
              <div class="header-left">
                <el-icon><Document /></el-icon>
                <span>约束条件</span>
              </div>
            </div>
          </template>
          <div class="constraints-list">
            <div
              v-for="(constraint, index) in resultData.constraints"
              :key="index"
              class="constraint-item"
            >
              <span class="constraint-label">条件 {{ index + 1 }}:</span>
              <div class="constraint-content">
                <MathJaxRenderer 
                  :formula="constraint" 
                  :display-mode="false"
                />
              </div>
            </div>
          </div>
        </el-card>
      </div>
      
      <!-- 生成的虚拟样本表格 -->
      <div class="result-table-section">
        <el-card class="table-card">
          <template #header>
            <div class="card-header">
              <div class="header-left">
                <el-icon><Grid /></el-icon>
                <span>生成的虚拟样本</span>
              </div>
              <div class="header-right">
                <el-button 
                  type="primary" 
                  size="small"
                  @click="handleExport"
                  :disabled="!resultData?.generatedSamples?.length"
                  :icon="Download"
                >
                  导出结果
                </el-button>
              </div>
            </div>
          </template>
          
          <ReTable 
            v-if="resultData?.generatedSamples?.length"
            :data="resultData.generatedSamples"
            :page-size="20"
          >
            <template #default="{ data: paginatedData }">
              <el-table 
                :data="paginatedData" 
                height="400"
                class="result-table"
                :default-sort="{ prop: '0', order: 'ascending' }"
              >
                <el-table-column
                  v-for="(col, index) in resultData?.columns || []"
                  :key="index"
                  :prop="index.toString()"
                  :label="col.title || col.data"
                  show-overflow-tooltip
                  sortable
                  :sort-method="(a, b) => sortTableData(a, b, index)"
                />
              </el-table>
            </template>
          </ReTable>
          
          <el-empty 
            v-else 
            description="暂无生成的虚拟样本数据" 
            :image-size="80"
          />
        </el-card>
      </div>
    </div>
    
    <template #footer>
      <div class="footer-buttons">
        <div class="left-space"></div>
        <div class="right-buttons">
          <el-button 
            @click="handleSaveToWorkspace"
            :disabled="!resultData?.generatedSamples?.length"
            :icon="FolderAdd"
          >
            保存到当前工作区
          </el-button>
          <el-button 
            type="primary" 
            @click="handleImportAsTempFile"
            :disabled="!resultData?.generatedSamples?.length"
            :icon="Upload"
          >
            编辑
          </el-button>
          <el-button @click="handleClose">关闭</el-button>
        </div>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { computed } from "vue";
import type { PropType } from "vue";
import { DataAnalysis, Plus, Setting, Document, Grid, Download, Upload, FolderAdd } from "@element-plus/icons-vue";
import type { ResultData } from "./types";
import { MathJax as MathJaxRenderer } from "@/components/mathJax";
import ReTable from "@/components/ReTable/index.vue";

// Props 定义
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  resultData: {
    type: Object as PropType<ResultData | null>,
    default: null
  }
});

// Emits 定义
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'export-data': []
  'import-as-temp-file': [data: any[][], columns: any[]]
  'save-to-workspace': [data: any[][], columns: any[]]
}>();

// 响应式数据
const isVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
});

// 方法
const handleClose = () => {
  isVisible.value = false;
};

const handleExport = () => {
  emit('export-data');
};

// 获取生成样本数量
const getGeneratedSampleCount = () => {
  if (!props.resultData?.generatedSamples) return 0;
  return props.resultData.generatedSamples.length;
};

// 获取方法显示名称
const getMethodDisplayName = (method: string | undefined) => {
  if (!method) return '未知';
  
  const methodMap: Record<string, string> = {
    'RandomGeneration': '随机生成',
    'GridGeneration': '网格生成',
  };
  return methodMap[method] || method;
};

// 表格数据排序
const sortTableData = (a: any, b: any, columnIndex: number) => {
  const aVal = a[columnIndex];
  const bVal = b[columnIndex];
  
  // 尝试转换为数字进行排序
  const aNum = parseFloat(aVal);
  const bNum = parseFloat(bVal);
  
  if (!isNaN(aNum) && !isNaN(bNum)) {
    return aNum - bNum;
  }
  
  // 如果无法转换为数字，则按字符串排序
  return String(aVal).localeCompare(String(bVal), 'zh-CN');
};

// 导入为临时文件
const handleImportAsTempFile = () => {
  if (!props.resultData?.generatedSamples || !props.resultData?.columns) return;
  
  // 通过emit将数据传递给父组件
  emit('import-as-temp-file', props.resultData.generatedSamples, props.resultData.columns);
};

// 保存到当前工作区
const handleSaveToWorkspace = () => {
  if (!props.resultData?.generatedSamples || !props.resultData?.columns) return;
  
  // 通过emit将数据传递给父组件
  emit('save-to-workspace', props.resultData.generatedSamples, props.resultData.columns);
};
</script>

<style scoped>
/* 结果对话框样式 */
.result-dialog :deep(.el-dialog__body) {
  padding: 20px;
  max-height: 80vh;
  overflow-y: auto;
}

.result-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* 结果概览卡片 */
.result-overview {
  margin: 20px 0;
}

.result-card {
  border-radius: 12px;
  transition: all 0.3s ease;
}

.result-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.card-content {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 10px 0;
}

.card-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
  border-radius: 12px;
  background: rgba(64, 158, 255, 0.1);
}

.card-info {
  flex: 1;
}

.card-value {
  font-size: 24px;
  font-weight: 700;
  color: var(--el-text-color-primary);
  line-height: 1.2;
  margin-bottom: 4px;
}

.card-label {
  font-size: 14px;
  color: var(--el-text-color-secondary);
  line-height: 1.2;
}

/* 信息卡片 */
.info-card {
  border-radius: 12px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  font-size: 16px;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 10px;
}

/* 约束条件信息卡片头部样式统一 */
.constraints-info .card-header {
  justify-content: flex-start;
}

.constraints-info .header-left {
  font-size: 16px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

/* 约束条件显示样式优化 */
.constraints-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.constraint-item {
  display: flex;
  align-items: flex-start;
  gap: 10px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid var(--el-border-color-lighter);
}

.constraint-label {
  font-size: 13px;
  color: var(--el-text-color-secondary);
  font-weight: 500;
  min-width: 60px;
  flex-shrink: 0;
}

.constraint-content {
  flex: 1;
  min-height: 24px;
  display: flex;
  align-items: center;
}

.constraint-content :deep(.MathJax) {
  font-size: 16px;
}

/* 响应式优化 */
@media (max-width: 1400px) {
  .constraint-item {
    flex-direction: column;
    gap: 8px;
  }
  
  .constraint-label {
    min-width: auto;
  }
}

/* 表格卡片样式 */
.table-card {
  border-radius: 12px;
}

.result-table {
  border-radius: 8px;
}

/* 底部按钮样式 */
.footer-buttons {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.left-space {
  flex: 1;
}

.right-buttons {
  display: flex;
  gap: 12px;
  align-items: center;
}

.right-buttons .el-button {
  margin: 0;
}
</style>
