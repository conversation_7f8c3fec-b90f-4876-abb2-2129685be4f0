import { release } from "node:os";
import { fileURLToPath } from "node:url";
import { join, dirname } from "node:path";
import { app, dialog } from "electron";
import { readFileSync } from "node:fs";

// Import our new modules
import { ProcessManager } from "./modules/ProcessManager";
import { WindowManager } from "./modules/WindowManager";
import { FileSystemHandler } from "./modules/FileSystemHandler";
import { DialogHandler } from "./modules/DialogHandler";
import { MenuManager } from "./modules/MenuManager";
import { IPCHandler } from "./modules/IPCHandler";

// The built directory structure
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
process.env.DIST_ELECTRON = join(__dirname, "..");
process.env.DIST = join(process.env.DIST_ELECTRON, "../dist");
process.env.PUBLIC = process.env.VITE_DEV_SERVER_URL
  ? join(process.env.DIST_ELECTRON, "../public")
  : process.env.DIST;

// 是否为开发环境
const isDev = process.env["NODE_ENV"] === "development";

// Disable GPU Acceleration for Windows 7
if (release().startsWith("6.1")) app.disableHardwareAcceleration();

// Set application name for Windows 10+ notifications
if (process.platform === "win32") app.setAppUserModelId(app.getName());

if (!app.requestSingleInstanceLock()) {
  app.quit();
  process.exit(0);
}

// Initialize all managers
const processManager = new ProcessManager();
const windowManager = new WindowManager();
const fileSystemHandler = new FileSystemHandler();
const dialogHandler = new DialogHandler();
const menuManager = new MenuManager(isDev);
const ipcHandler = new IPCHandler(
  windowManager,
  fileSystemHandler,
  dialogHandler,
  menuManager,
);

let isShuttingDown = false;

/**
 * 异步启动所有服务并创建主窗口的函数
 */
async function startServicesAndMainWindow() {
  console.log("Services starting...");
  const BackendEnabled = import.meta.env.VITE_START_BACKEND;

  if (BackendEnabled === "true") {
    // 在启动前检查并清理可能的残留进程
    console.log("Checking for residual processes from previous runs...");
    const loadingWindow = windowManager.getLoadingWindow();
    loadingWindow?.webContents.send("update-progress", 10, "正在清理环境...");

    try {
      // 清理残留进程
      await processManager.terminateAllBackendProcesses();
      await processManager.terminateAllRabbitMQProcesses();
      console.log("Residual process cleanup completed");
    } catch (error) {
      console.error("Error during residual process cleanup:", error);
    }

    // 首先启动RabbitMQ服务
    console.log("Starting RabbitMQ...");
    loadingWindow?.webContents.send(
      "update-progress",
      25,
      "正在启动消息队列服务...",
    );
    const rabbitmqStarted = await processManager.startRabbitMQ();

    if (!rabbitmqStarted) {
      console.error("Failed to start RabbitMQ, aborting startup");
      dialog.showErrorBox(
        "启动失败",
        "RabbitMQ服务启动失败，请检查配置并重试。",
      );
      app.quit();
      return;
    }

    // 等待一段时间让RabbitMQ完全初始化
    console.log("Waiting for RabbitMQ to fully initialize...");
    loadingWindow?.webContents.send(
      "update-progress",
      50,
      "正在初始化消息队列...",
    );
    await new Promise((resolve) => setTimeout(resolve, 5000));

    // 然后启动后端服务
    console.log("Starting backend service...");
    loadingWindow?.webContents.send(
      "update-progress",
      75,
      "正在启动后端服务...",
    );
    const backendStarted = await processManager.startBackendService();

    if (!backendStarted) {
      console.error("Failed to start backend service, aborting startup");
      dialog.showErrorBox("启动失败", "后端服务启动失败，请检查配置并重试。");
      await processManager.terminateAllRabbitMQProcesses();
      app.quit();
      return;
    }

    // 等待后端服务完全初始化
    console.log("Waiting for backend service to fully initialize...");
    loadingWindow?.webContents.send(
      "update-progress",
      90,
      "正在初始化后端服务...",
    );
    await new Promise((resolve) => setTimeout(resolve, 3000));
  }

  // 创建主窗口
  console.log("All services started successfully, creating UI...");
  const loadingWindow = windowManager.getLoadingWindow();
  loadingWindow?.webContents.send(
    "update-progress",
    100,
    "服务启动完成，请稍后...",
  );
  await windowManager.createMainWindow("/welcome");

  const mainWindow = windowManager.getMainWindow();
  if (mainWindow) {
    mainWindow.once("ready-to-show", () => {
      setTimeout(() => {
        windowManager.closeLoadingWindow();
        windowManager.showMainWindow();
      }, 300);
    });
  }
}

/**
 * 创建闪屏窗口并启动服务
 */
function initializeApp() {
  const loadingWindow = windowManager.createLoadingWindow();

  // 当加载页面渲染完成后，才开始启动后端服务
  loadingWindow.webContents.on("did-finish-load", () => {
    // 读取 package.json 的版本号并发送到加载窗口
    try {
      const packageJsonPath = join(app.getAppPath(), "package.json");
      const pkg = JSON.parse(readFileSync(packageJsonPath, "utf-8"));
      loadingWindow?.webContents.send("set-version", pkg.version);
    } catch (error) {
      console.error("Failed to read package.json version:", error);
    }

    loadingWindow?.show();
    startServicesAndMainWindow();
  });

  // 创建应用菜单
  menuManager.createMenu();
}

// 应用事件处理
app.whenReady().then(() => {
  initializeApp();
});

app.on("window-all-closed", () => {
  if (process.platform !== "darwin") {
    app.quit();
  }
});

// 在应用退出前关闭后端服务
app.on("before-quit", async (event) => {
  const BackendEnabled = import.meta.env.VITE_START_BACKEND;
  if (!isShuttingDown && BackendEnabled === "true") {
    event.preventDefault();
    isShuttingDown = true;
    processManager.setShuttingDown(true);

    console.log("Application is quitting, terminating all services...");

    const terminateWithTimeout = async (
      terminateFunc: () => Promise<void>,
      name: string,
      timeout: number,
    ) => {
      return Promise.race([
        terminateFunc(),
        new Promise<void>((resolve) => {
          setTimeout(() => {
            console.warn(`${name} termination timeout after ${timeout}ms`);
            resolve();
          }, timeout);
        }),
      ]);
    };

    try {
      await Promise.all([
        terminateWithTimeout(
          async () => await processManager.terminateAllRabbitMQProcesses(),
          "RabbitMQ",
          5000,
        ),
        terminateWithTimeout(
          async () => await processManager.terminateAllBackendProcesses(),
          "Backend",
          5000,
        ),
      ]);
      console.log("All processes terminated successfully");
    } catch (error) {
      console.error("Error during service termination:", error);
    }

    // 确保所有服务都已终止后再退出
    setTimeout(() => {
      console.log("Clean exit completed, quitting application");
      app.exit(0);
    }, 1000);
  }
});

// 确保进程在退出时清理
process.on("exit", () => {
  const BackendEnabled = import.meta.env.VITE_START_BACKEND;
  if (BackendEnabled === "true") {
    console.log("Node process exiting, attempting final cleanup");
    // 同步清理操作会在这里执行
  }
});

// 处理未捕获的异常
process.on("uncaughtException", async (error) => {
  console.error("Uncaught exception:", error);
  isShuttingDown = true;
  processManager.setShuttingDown(true);

  try {
    await Promise.race([
      Promise.all([
        processManager.terminateAllBackendProcesses(),
        processManager.terminateAllRabbitMQProcesses(),
      ]),
      new Promise((resolve) => setTimeout(resolve, 3000)),
    ]);
    console.log("Emergency cleanup completed");
  } catch (e) {
    console.error("Emergency cleanup error:", e);
  }

  app.exit(1);
});

// 处理未处理的拒绝Promise
process.on("unhandledRejection", (reason) => {
  console.error("Unhandled Promise rejection:", reason);
});

app.on("second-instance", () => {
  const mainWindow = windowManager.getMainWindow();
  if (mainWindow && !mainWindow.isDestroyed()) {
    if (mainWindow.isMinimized()) mainWindow.restore();
    mainWindow.focus();
  }
});

app.on("activate", () => {
  const mainWindow = windowManager.getMainWindow();
  if (!mainWindow || mainWindow.isDestroyed()) {
    windowManager.createMainWindow("/welcome");
  } else {
    if (mainWindow.isMinimized()) mainWindow.restore();
    mainWindow.focus();
  }
});

// 导出管理器实例以供其他模块使用
export {
  processManager,
  windowManager,
  fileSystemHandler,
  dialogHandler,
  menuManager,
  ipcHandler,
};
