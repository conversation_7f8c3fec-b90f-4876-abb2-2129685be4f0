"use strict";
const electron = require("electron");
electron.contextBridge.exposeInMainWorld("api", {
  onUpdateProgress: (callback) => electron.ipcRenderer.on("update-progress", callback),
  onSetVersion: (callback) => electron.ipcRenderer.on("set-version", callback),
  quitApp: () => electron.ipcRenderer.send("app-quit")
  // 修改：使用现有的 'app-quit' 事件
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoibG9hZGluZy5qcyIsInNvdXJjZXMiOlsiLi4vLi4vZWxlY3Ryb24vcHJlbG9hZC9sb2FkaW5nLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNvbnRleHRCcmlkZ2UsIGlwY1JlbmRlcmVyLCBJcGNSZW5kZXJlckV2ZW50IH0gZnJvbSAnZWxlY3Ryb24nO1xyXG5cclxuY29udGV4dEJyaWRnZS5leHBvc2VJbk1haW5Xb3JsZCgnYXBpJywge1xyXG4gIG9uVXBkYXRlUHJvZ3Jlc3M6IChjYWxsYmFjazogKGV2ZW50OiBJcGNSZW5kZXJlckV2ZW50LCBwcm9ncmVzczogbnVtYmVyLCB0ZXh0OiBzdHJpbmcpID0+IHZvaWQpID0+IFxyXG4gICAgaXBjUmVuZGVyZXIub24oJ3VwZGF0ZS1wcm9ncmVzcycsIGNhbGxiYWNrKSxcclxuICBvblNldFZlcnNpb246IChjYWxsYmFjazogKGV2ZW50OiBJcGNSZW5kZXJlckV2ZW50LCB2ZXJzaW9uOiBzdHJpbmcpID0+IHZvaWQpID0+XHJcbiAgICBpcGNSZW5kZXJlci5vbignc2V0LXZlcnNpb24nLCBjYWxsYmFjayksXHJcbiAgcXVpdEFwcDogKCkgPT4gaXBjUmVuZGVyZXIuc2VuZCgnYXBwLXF1aXQnKSwgLy8g5L+u5pS577ya5L2/55So546w5pyJ55qEICdhcHAtcXVpdCcg5LqL5Lu2XHJcbn0pO1xyXG4iXSwibmFtZXMiOlsiY29udGV4dEJyaWRnZSIsImlwY1JlbmRlcmVyIl0sIm1hcHBpbmdzIjoiOztBQUVBQSxTQUFBQSxjQUFjLGtCQUFrQixPQUFPO0FBQUEsRUFDckMsa0JBQWtCLENBQUMsYUFDakJDLFNBQUFBLFlBQVksR0FBRyxtQkFBbUIsUUFBUTtBQUFBLEVBQzVDLGNBQWMsQ0FBQyxhQUNiQSxTQUFBQSxZQUFZLEdBQUcsZUFBZSxRQUFRO0FBQUEsRUFDeEMsU0FBUyxNQUFNQSxxQkFBWSxLQUFLLFVBQVU7QUFBQTtBQUM1QyxDQUFDOyJ9
