import re
from typing import Union

SAFE_WORDS = {"ranges"}

def camel_to_snake(data, safe_words: set = None) -> Union[dict, list]:
    """
    将驼峰命名转换为下划线命名
    
    Args:
        data: 要转换的数据（字典、列表或其他类型）
        safe_words: 安全词集合，这些键名对应的值不会被转换
    
    Returns:
        转换后的数据
    """
    if safe_words is None:
        safe_words = SAFE_WORDS
    
    if isinstance(data, dict):
        return {
            re.sub(r'(?<!^)(?=[A-Z])', '_', k).lower(): (
                v if k in safe_words else camel_to_snake(v, safe_words)
            ) 
            for k, v in data.items()
        }
    elif isinstance(data, list):
        return [camel_to_snake(item, safe_words) for item in data]
    return data

def snake_to_camel(data, safe_words: set = None):
    """
    将下划线命名转换为驼峰命名
    
    Args:
        data: 要转换的数据（字典、列表或其他类型）
        safe_words: 安全词集合，这些键名对应的值不会被转换
    
    Returns:
        转换后的数据
    """
    if safe_words is None:
        safe_words = SAFE_WORDS
    
    if isinstance(data, dict):
        return {
            ''.join([k.split('_')[0]] + [x.capitalize() for x in k.split('_')[1:]]): (
                v if k in safe_words else snake_to_camel(v, safe_words)
            ) 
            for k, v in data.items()
        }
    elif isinstance(data, list):
        return [snake_to_camel(item, safe_words) for item in data]
    return data