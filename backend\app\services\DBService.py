from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from app.types.services import Base, ModelTask
from typing import Optional, List
from app.configs import BasicConfig
from datetime import datetime
from app.types.model_request import ModelConfig
from app.types.search_request import SearchConfig
from app.types.services import SearchTask
import json

db_path = "sqlite:///" + str(BasicConfig.DB_PATH)

class DBService(object):

    @staticmethod
    def init_db():
        engine = create_engine(db_path, echo=False)
        Base.metadata.create_all(engine)
        engine.dispose()

    @staticmethod
    def get_session():
        engine = create_engine(db_path, echo=False)
        Session = sessionmaker(bind=engine)
        return Session()
    
    @staticmethod
    def create_model_task(uid: str, name: str, params: ModelConfig, category: str="", status: str = "pending", progress: Optional[int] = None, result: Optional[dict] = None, error: Optional[str] = None, source: str = "local", origin_params: Optional[dict] = None) -> ModelTask:
        model_task: ModelTask = ModelTask(
            uid=uid,
            name=name,
            category=category,
            params=params.to_dict(),
            status=status,
            progress=progress,
            created_at=datetime.now(),
            updated_at=datetime.now(),
            result=result,
            error=error,
            is_rabbitmq_ready=params.is_rabbitmq_ready,
            asynchronous=params.asynchronous,
            source=source,
            origin_params=origin_params
        )
        return model_task
    
    @staticmethod
    def add_model_task(model_task: ModelTask):
        session = DBService.get_session()
        session.add(model_task)
        session.commit()
        # 获取新创建任务的ID
        _id = model_task.id
        session.close()
        return _id  
    
    @staticmethod
    def update_model_task(uid: str, **kwargs):
        session = DBService.get_session()
        update_values = {getattr(ModelTask, k): v for k, v in kwargs.items()}
        session.query(ModelTask).filter(ModelTask.uid == uid).update(update_values)
        session.commit()
        session.close()
    
    @staticmethod
    def get_model_info(uid: str) -> Optional[ModelTask]:
        session = DBService.get_session()
        model_info = session.query(ModelTask).filter(ModelTask.uid == uid).first()
        session.close()
        return model_info
    
    @staticmethod
    def get_model_list(query_params: dict) -> List[ModelTask]:
        """
        使用示例:
        
        # 按日期筛选
        query_params = {
            'build_date': '2024-01-15',  # 筛选2024年1月15日创建或更新的模型
            'status': 'completed'         # 状态为completed
        }
        
        # 按模型名称筛选
        query_params = {
            'model_name': 'xgboost',      # 筛选result.alg字段为'xgboost'的模型
            'category': 'tree'            # 类别为tree
        }
        
        # 按关键词搜索
        query_params = {
            'search_keyword': 'regression', # 在name、params、result、origin_params中搜索'regression'
            'model_type': 'reg'            # 模型类型为regression
        }
        
        # 组合使用
        query_params = {
            'build_date': '2024-01-15',
            'model_name': 'xgboost',
            'search_keyword': 'regression',
            'status': 'completed'
        }
        
        models = DBService.get_model_list(query_params)
        """
        session = DBService.get_session()
        query = session.query(ModelTask)
        
        # 处理build_date筛选（created_at和updated_at）
        if 'build_date' in query_params:
            build_date = query_params.pop('build_date')
            if isinstance(build_date, str):
                from datetime import datetime
                try:
                    # 尝试解析日期字符串，支持多种格式
                    if len(build_date) == 10:  # YYYY-MM-DD
                        start_date = datetime.strptime(build_date, '%Y-%m-%d')
                        end_date = datetime.strptime(build_date, '%Y-%m-%d').replace(hour=23, minute=59, second=59)
                    elif len(build_date) == 19:  # YYYY-MM-DD HH:MM:SS
                        start_date = datetime.strptime(build_date, '%Y-%m-%d %H:%M:%S')
                        end_date = start_date
                    else:
                        # 默认按天处理
                        start_date = datetime.strptime(build_date, '%Y-%m-%d')
                        end_date = datetime.strptime(build_date, '%Y-%m-%d').replace(hour=23, minute=59, second=59)
                    
                    query = query.filter(
                        (ModelTask.created_at >= start_date) & (ModelTask.created_at <= end_date) |
                        (ModelTask.updated_at >= start_date) & (ModelTask.updated_at <= end_date)
                    )
                except ValueError:
                    # 如果日期解析失败，忽略此筛选条件
                    pass
        
        # 处理model_name筛选（result内的alg字段）
        if 'model_name' in query_params:
            model_name = query_params.pop('model_name')
            if model_name:
                # 使用SQLite的json_extract函数查询JSON字段中的alg值
                from sqlalchemy import text
                query = query.filter(
                    text("json_extract(result, '$.alg') = :model_name")
                ).params(model_name=model_name)
        
        # 处理search_keyword筛选（name、params、result、origin_params）
        if 'search_keyword' in query_params:
            search_keyword = query_params.pop('search_keyword')
            if search_keyword:
                from sqlalchemy import or_, text
                # 使用LIKE进行模糊匹配
                query = query.filter(
                    or_(
                        ModelTask.name.like(f'%{search_keyword}%'),
                        text("json_extract(params, '$') LIKE :keyword"),
                        text("json_extract(result, '$') LIKE :keyword"),
                        text("json_extract(origin_params, '$') LIKE :keyword")
                    )
                ).params(keyword=f'%{search_keyword}%')
        
        # 处理其他常规筛选条件
        for key, value in query_params.items():
            if hasattr(ModelTask, key) and value is not None:
                if key in ['params', 'result', 'origin_params']:
                    # 对于JSON字段，使用json_extract进行精确匹配
                    from sqlalchemy import text
                    query = query.filter(
                        text(f"json_extract({key}, '$') = :{key}_value")
                    ).params(**{f"{key}_value": str(value)})
                else:
                    # 对于普通字段，使用常规筛选
                    query = query.filter(getattr(ModelTask, key) == value)
        
        # 按创建时间降序排列
        model_list = query.order_by(ModelTask.created_at.desc()).all()
        session.close()
        return model_list
    
    @staticmethod
    def delete_models(uids: List[str]):
        session = DBService.get_session()
        session.query(ModelTask).filter(ModelTask.uid.in_(uids)).delete(synchronize_session=False)
        session.commit()
        session.close()
    
    @staticmethod
    def get_model_params(uid: str) -> Optional[dict]:
        session = DBService.get_session()
        model_params = session.query(ModelTask).filter(ModelTask.uid == uid).first()
        session.close()
        return model_params.origin_params

    """
    SearchTask
    """
    @staticmethod
    def create_search_task(uid: str, name: str, params: SearchConfig) -> SearchTask:
        if isinstance(params.model_uid, list):
            model_uid = json.dumps(params.model_uid)
        else:
            model_uid = params.model_uid
        search_task: SearchTask = SearchTask(
            uid=uid,
            model_uid=model_uid,
            name=f"{name}_search",
            params=params.to_dict(),
            status="running",
            progress=None,
            created_at=datetime.now(),
            updated_at=datetime.now(),
            result=None,
            error=None,
            is_rabbitmq_ready=params.is_rabbitmq_ready,
            asynchronous=params.asynchronous
        )
        return search_task
    
    @staticmethod
    def add_search_task(search_task: SearchTask):
        session = DBService.get_session()
        session.add(search_task)
        session.commit()
        # 获取新创建任务的ID
        _id = search_task.id
        session.close()
        return _id
    
    @staticmethod
    def update_search_task(uid: str, **kwargs):
        session = DBService.get_session()
        update_values = {getattr(SearchTask, k): v for k, v in kwargs.items()}
        session.query(SearchTask).filter(SearchTask.uid == uid).update(update_values)
        session.commit()
        session.close()
    
    @staticmethod
    def get_search_info(uid: str) -> Optional[SearchTask]:
        session = DBService.get_session()
        search_info = session.query(SearchTask).filter(SearchTask.uid == uid).first()
        session.close()
        return search_info

    @staticmethod
    def get_search_list() -> List[SearchTask]:
        session = DBService.get_session()
        search_list = session.query(SearchTask).order_by(SearchTask.created_at.desc()).all()
        session.close()
        return search_list
    
    @staticmethod
    def delete_searches(uids: List[str]):
        session = DBService.get_session()
        session.query(SearchTask).filter(SearchTask.uid.in_(uids)).delete(synchronize_session=False)
        session.commit()
        session.close()
    