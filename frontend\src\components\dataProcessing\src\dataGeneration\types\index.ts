/**
 * 虚拟样本生成相关类型定义
 */

import type { TableColumn } from "@/components/dataProcessing/src/dataTable";

// 可点击项目类型
export interface FeatureItem {
  type: "feature";
  value: TableColumn;
}

export interface OperatorItem {
  type: "operator";
  value: string;
}

export interface NumberItem {
  type: "number";
  value: number;
}

export type ClickableItem = FeatureItem | OperatorItem | NumberItem;

// 约束条件
export interface Constraint {
  id: number;
  latex: string;
}

// 生成参数
export interface GenerationParams {
  algorithm: string;
  algorithmParams: Record<string, any>;
}

// 特征范围配置
export interface FeatureRange {
  min: number;
  max: number;
  count: number;
}

// 高级设置
export interface AdvancedSettings {
  ranges: Record<string, FeatureRange>;
}

// 结果数据
export interface ResultData {
  generatedSamples: any[][];
  originalData: any[][];
  constraints: string[];
  columns: TableColumn[];
  metadata: {
    sampleCount: number;
    generationTime: number;
    method: string;
    originalRowCount: number;
  };
}

// 算法配置接口
export interface AlgorithmConfig {
  id: string;
  config: {
    displayName: string;
    description: string;
    defaultParams: Record<string, any>;
  };
}
