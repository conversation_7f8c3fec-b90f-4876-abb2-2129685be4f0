from flask import Blueprint, request, make_response, current_app, jsonify
import logging
from app.utils.params import camel_to_snake, snake_to_camel
from app.types.model_request import ModelRequest, ModelConfig
from app.routers.models.utils import check_model_request, check_model_edit_request
from app.services.DBService import DBService
from app.types.services import ModelTask
from app.services.TaskService import TaskService
from typing import cast
from app.types.flaskapp import FlaskWithExecutor
from app.services.FileService import FileService

linear_bp = Blueprint("linear", __name__)
logger = logging.getLogger()

@linear_bp.route("/build", methods=["POST"])
def linear_build(): 
    post_data: ModelRequest = camel_to_snake(request.json) # type: ignore
    try:
        model_config: ModelConfig = check_model_request(post_data)
        model_task: ModelTask = DBService.create_model_task(
            uid=model_config.uid, 
            name=model_config.name, 
            category="linear",
            params=model_config,
            origin_params=post_data
        )
        DBService.add_model_task(model_task=model_task)
        logger.info(f"linear_build: 添加模型任务: {model_task.uid}")

        TaskService.submit_model_task(model_config)
        return_data = {
            "uid": model_task.uid,
            "asynchronous": model_config.asynchronous,
            "is_rabbitmq_ready": cast(FlaskWithExecutor, current_app).is_rabbitmq_ready
        }
        return make_response(jsonify({"code": 200, "msg": "success", "data": snake_to_camel(return_data)}), 200)
    except Exception as e:
        logger.error(f"linear_build: 添加模型任务失败: {e}")
        return make_response(jsonify({"code": 500, "msg": f"添加模型任务失败: {e}", "data": None}), 500)

@linear_bp.route("/edit", methods=["POST"])
def linear_edit():
    post_data: ModelRequest = camel_to_snake(request.json) # type: ignore
    try:
        model_uid: str = post_data.get("uid")
        old_model_task: ModelTask = DBService.get_model_info(uid=model_uid)
        old_model_config: ModelConfig = ModelConfig.from_dict(old_model_task.params)
        updated_model_config: ModelConfig = check_model_edit_request(new_model_request=post_data, old_model_config=old_model_config)
        DBService.update_model_task(uid=model_uid, params=updated_model_config.to_dict(), origin_params=post_data)
        model_file = FileService.load_model(model_uid)
        TaskService.submit_update_model_task(updated_model_config, old_model_task, model_file)
        return_data = {
            "uid": updated_model_config.uid,
            "asynchronous": updated_model_config.asynchronous,
            "is_rabbitmq_ready": cast(FlaskWithExecutor, current_app).is_rabbitmq_ready
        }
        return make_response(jsonify({"code": 200, "msg": "success", "data": snake_to_camel(return_data)}), 200)
    except Exception as e:
        logger.error(f"linear_edit: 编辑模型任务失败: {e}")
        return make_response(jsonify({"code": 500, "msg": f"编辑模型任务失败: {e}", "data": None}), 500)