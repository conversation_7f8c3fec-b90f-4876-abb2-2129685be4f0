from app.types.search_request import SearchRequest, SearchConfig, SearchRequestMultiple
from app.types.flaskapp import FlaskWithExecutor
import uuid
from flask import current_app
from typing import cast

def check_search_request(search_request: SearchRequest) -> SearchConfig:
    """
    检查搜索请求
    """
    model_uid = search_request.get("model_uid")
    target = search_request.get("target")
    feature_ranges = search_request.get("feature_ranges")
    target_name = target.get("name")
    target_value = target.get("value")
    search = search_request.get("search")
    alg_name = search.get("algorithm")
    alg_param = search.get("params")
    return SearchConfig(
        uid=str(uuid.uuid4()),
        model_uid=model_uid,
        target_name=target_name,
        target_value=target_value,
        feature_ranges=feature_ranges,
        alg_name=alg_name,
        alg_param=alg_param,
        asynchronous=True,
        is_rabbitmq_ready=cast(FlaskWithExecutor, current_app).is_rabbitmq_ready
    )

def check_search_request_multiple(search_request: SearchRequestMultiple) -> SearchConfig:
    """
    检查多目标搜索请求
    """
    targets = search_request.get("targets")
    feature_ranges = search_request.get("feature_ranges")

    model_uid = []
    target_name = []
    target_value = []
    for target in targets:
        model_uid.append(target.get("model_uid"))
        target_name.append(target.get("target").get("name"))
        target_value.append({
            "value": target.get("target").get("value"),
            "criterion": target.get("criterion"),
            "weight": target.get("weight")
        })
    
    search = search_request.get("search")
    alg_name = search.get("algorithm")
    alg_param = search.get("params")
    return SearchConfig(
        uid=str(uuid.uuid4()),
        model_uid=model_uid,
        target_name=target_name,
        target_value=target_value,
        feature_ranges=feature_ranges,
        alg_name=alg_name,
        alg_param=alg_param,
        asynchronous=True,
        is_rabbitmq_ready=cast(FlaskWithExecutor, current_app).is_rabbitmq_ready
    )