{"version": 3, "file": "index.js", "sources": ["../../electron/main/utils.ts", "../../electron/main/modules/ProcessManager.ts", "../../electron/main/modules/WindowManager.ts", "../../electron/main/modules/FileSystemHandler.ts", "../../electron/main/modules/DialogHandler.ts", "../../electron/main/modules/MenuManager.ts", "../../electron/main/modules/IPCHandler.ts", "../../electron/main/index.ts"], "sourcesContent": ["import { exec } from \"node:child_process\";\r\n\r\n// 检查是否有运行中的runserver.exe进程，若有则返回进程ID，否则返回空数组\r\nexport async function checkExistingBackendProcesses(\r\n  processName: string = \"runserver.exe\",\r\n): Promise<number[]> {\r\n  return new Promise((resolve) => {\r\n    // 暂只检测windows平台，若需要检测其他平台，请修改此处的判断条件\r\n    if (process.platform !== \"win32\") {\r\n      resolve([]);\r\n      return;\r\n    }\r\n\r\n    // wmic命令用于获取windows系统中的进程信息\r\n    // 检测进程名为runserver.exe的进程ID\r\n    const cmd = `wmic process where \"name='${processName}'\" get processid`;\r\n\r\n    exec(cmd, (error, stdout) => {\r\n      if (error) {\r\n        console.log(\r\n          \"No existing backend processes found or error running command\",\r\n        );\r\n        resolve([]);\r\n        return;\r\n      }\r\n\r\n      // 解析进程ID\r\n      const pidPattern = /(\\d+)/g;\r\n      const pids: number[] = [];\r\n      let match;\r\n\r\n      while ((match = pidPattern.exec(stdout)) !== null) {\r\n        const pid = parseInt(match[1], 10);\r\n        if (!isNaN(pid) && pid !== process.pid) {\r\n          pids.push(pid);\r\n        }\r\n      }\r\n\r\n      console.log(`Found ${pids.length} existing backend processes:`, pids);\r\n      resolve(pids);\r\n    });\r\n  });\r\n}\r\n\r\n// 检查是否有运行中的RabbitMQ进程\r\n// rmq启动方式为rabbitmq-server.bat，该bat文件会启动erl.exe和epmd.exe\r\nexport async function checkExistingRabbitMQProcesses(): Promise<number[]> {\r\n  return new Promise((resolve) => {\r\n    if (process.platform !== \"win32\") {\r\n      resolve([]);\r\n      return;\r\n    }\r\n\r\n    const cmd =\r\n      \"wmic process where \\\"name='erl.exe' or name='epmd.exe'\\\" get processid,name\";\r\n\r\n    exec(cmd, (error, stdout) => {\r\n      if (error) {\r\n        console.log(\r\n          \"No existing RabbitMQ processes found or error running command\",\r\n        );\r\n        resolve([]);\r\n        return;\r\n      }\r\n\r\n      // 解析进程ID和名称\r\n      const processLines = stdout.trim().split(\"\\n\").slice(1); // 跳过标题行\r\n      const pids: number[] = [];\r\n\r\n      for (const line of processLines) {\r\n        const match = line.trim().match(/(\\d+)\\s+(.+)/);\r\n        if (match) {\r\n          const pid = parseInt(match[1], 10);\r\n          const name = match[2].trim();\r\n          if (!isNaN(pid) && pid !== process.pid) {\r\n            console.log(\r\n              `Found RabbitMQ related process: ${name} (PID: ${pid})`,\r\n            );\r\n            pids.push(pid);\r\n          }\r\n        }\r\n      }\r\n\r\n      console.log(`Found ${pids.length} existing RabbitMQ processes`);\r\n      resolve(pids);\r\n    });\r\n  });\r\n}\r\n", "import { spawn, type ChildProcess, exec } from \"node:child_process\";\r\nimport { existsSync } from \"node:fs\";\r\nimport { join, dirname } from \"node:path\";\r\nimport { app } from \"electron\";\r\nimport {\r\n  checkExistingBackendProcesses,\r\n  checkExistingRabbitMQProcesses,\r\n} from \"../utils\";\r\n\r\n/**\r\n * 进程管理器 - 负责管理后端服务和RabbitMQ进程\r\n */\r\nexport class ProcessManager {\r\n  private backendProcess: ChildProcess | null = null;\r\n  private backendPID: number | null = null;\r\n  private runningBackendProcesses: number[] = [];\r\n  private rabbitmqProcess: ChildProcess | null = null;\r\n  private isShuttingDown = false;\r\n\r\n  /**\r\n   * 终止指定PID的进程\r\n   */\r\n  async killProcess(pid: number): Promise<boolean> {\r\n    return new Promise((resolve) => {\r\n      if (!pid || pid <= 0) {\r\n        resolve(false);\r\n        return;\r\n      }\r\n\r\n      console.log(`Attempting to kill process with PID: ${pid}`);\r\n\r\n      try {\r\n        if (process.platform === \"win32\") {\r\n          exec(`taskkill /pid ${pid} /T /F`, (err) => {\r\n            if (err) {\r\n              console.error(`Failed to kill process ${pid}:`, err);\r\n              resolve(false);\r\n            } else {\r\n              console.log(`Successfully terminated process ${pid}`);\r\n              const index = this.runningBackendProcesses.indexOf(pid);\r\n              if (index !== -1) {\r\n                this.runningBackendProcesses.splice(index, 1);\r\n              }\r\n              resolve(true);\r\n            }\r\n          });\r\n        } else {\r\n          try {\r\n            process.kill(pid, \"SIGTERM\");\r\n            setTimeout(() => {\r\n              try {\r\n                process.kill(pid, 0);\r\n                process.kill(pid, \"SIGKILL\");\r\n                console.log(`Had to use SIGKILL for ${pid}`);\r\n              } catch {\r\n                // 进程已经终止\r\n              }\r\n\r\n              const index = this.runningBackendProcesses.indexOf(pid);\r\n              if (index !== -1) {\r\n                this.runningBackendProcesses.splice(index, 1);\r\n              }\r\n\r\n              resolve(true);\r\n            }, 1000);\r\n          } catch (e) {\r\n            console.error(`Failed to kill process ${pid}:`, e);\r\n            resolve(false);\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.error(`Error in killProcess for PID ${pid}:`, error);\r\n        resolve(false);\r\n      }\r\n    });\r\n  }\r\n\r\n  /**\r\n   * 启动后端服务\r\n   */\r\n  async startBackendService(): Promise<boolean> {\r\n    return new Promise(async (resolve) => {\r\n      try {\r\n        // 先检查是否有已运行的后端进程\r\n        const existingPids = await checkExistingBackendProcesses();\r\n\r\n        // 终止所有已存在的进程\r\n        for (const pid of existingPids) {\r\n          await this.killProcess(pid);\r\n        }\r\n\r\n        // 如果当前已有进程，则不再启动新的\r\n        if (this.backendProcess !== null && this.backendPID !== null) {\r\n          console.log(\r\n            `Backend service already running with PID: ${this.backendPID}`,\r\n          );\r\n          resolve(true);\r\n          return;\r\n        }\r\n\r\n        const backendExecutablePath = app.isPackaged\r\n          ? join(process.resourcesPath, \"backend\", \"runserver.exe\")\r\n          : join(app.getAppPath(), \"../backend/dist/backend\", \"runserver.exe\");\r\n\r\n        console.log(\"Resource path:\", process.resourcesPath);\r\n        console.log(`Starting backend service from: ${backendExecutablePath}`);\r\n\r\n        if (!existsSync(backendExecutablePath)) {\r\n          console.error(\r\n            `Backend executable not found at: ${backendExecutablePath}`,\r\n          );\r\n          resolve(false);\r\n          return;\r\n        }\r\n\r\n        console.log(`Backend executable exists, attempting to spawn process`);\r\n\r\n        this.backendProcess = spawn(backendExecutablePath, [], {\r\n          windowsHide: false,\r\n          stdio: \"pipe\",\r\n          cwd: app.isPackaged\r\n            ? join(process.resourcesPath, \"backend\")\r\n            : undefined,\r\n          detached: false,\r\n        });\r\n\r\n        if (!this.backendProcess || !this.backendProcess.pid) {\r\n          console.error(\"Failed to start backend service: Invalid process\");\r\n          resolve(false);\r\n          return;\r\n        }\r\n\r\n        this.backendPID = this.backendProcess.pid;\r\n        this.runningBackendProcesses.push(this.backendPID);\r\n\r\n        console.log(\"Backend service started with PID:\", this.backendPID);\r\n\r\n        // 监听后端输出，确认启动成功\r\n        let startupTimeout: NodeJS.Timeout;\r\n        let isStarted = false;\r\n        let hasFlaskApp = false;\r\n        let hasRunningOn = false;\r\n\r\n        const checkStartup = (data: Buffer) => {\r\n          const output = data.toString();\r\n          console.log(\"Backend output:\", output);\r\n\r\n          if (\r\n            output.includes(\"Serving Flask app\") ||\r\n            output.includes(\"Flask 应\")\r\n          ) {\r\n            hasFlaskApp = true;\r\n            console.log(\"Flask app detected\");\r\n          }\r\n\r\n          if (\r\n            output.includes(\"Running on http://\") ||\r\n            output.includes(\"* Running on all addresses\") ||\r\n            output.includes(\"Press CTRL+C to quit\")\r\n          ) {\r\n            hasRunningOn = true;\r\n            console.log(\"Server listening detected\");\r\n          }\r\n\r\n          if (hasFlaskApp && hasRunningOn && !isStarted) {\r\n            isStarted = true;\r\n            clearTimeout(startupTimeout);\r\n            console.log(\r\n              \"Backend service started successfully - Flask app is serving and listening\",\r\n            );\r\n            resolve(true);\r\n          }\r\n\r\n          if (\r\n            (output.includes(\"Running on http://\") &&\r\n              output.includes(\"Press CTRL+C to quit\")) ||\r\n            output.includes(\"Server started\") ||\r\n            output.includes(\"Backend ready\") ||\r\n            output.includes(\"服务已启动\")\r\n          ) {\r\n            if (!isStarted) {\r\n              isStarted = true;\r\n              clearTimeout(startupTimeout);\r\n              console.log(\"Backend service started successfully\");\r\n              resolve(true);\r\n            }\r\n          }\r\n        };\r\n\r\n        const checkStartupError = (data: Buffer) => {\r\n          const output = data.toString();\r\n          console.log(\"Backend error:\", output);\r\n\r\n          if (\r\n            output.includes(\"Serving Flask app\") ||\r\n            output.includes(\"Flask 应\")\r\n          ) {\r\n            hasFlaskApp = true;\r\n            console.log(\"Flask app detected (from stderr)\");\r\n          }\r\n\r\n          if (\r\n            output.includes(\"Running on http://\") ||\r\n            output.includes(\"* Running on all addresses\") ||\r\n            output.includes(\"Press CTRL+C to quit\")\r\n          ) {\r\n            hasRunningOn = true;\r\n            console.log(\"Server listening detected (from stderr)\");\r\n          }\r\n\r\n          if (hasFlaskApp && hasRunningOn && !isStarted) {\r\n            isStarted = true;\r\n            clearTimeout(startupTimeout);\r\n            console.log(\r\n              \"Backend service started successfully - Flask app is serving and listening (from stderr)\",\r\n            );\r\n            resolve(true);\r\n          }\r\n\r\n          if (\r\n            output.includes(\"Address already in use\") ||\r\n            output.includes(\"Permission denied\") ||\r\n            output.includes(\"Fatal error\") ||\r\n            output.includes(\"Failed to start\") ||\r\n            (output.includes(\"Error:\") &&\r\n              !output.includes(\"INFO\") &&\r\n              !output.includes(\"WARNING\"))\r\n          ) {\r\n            if (!isStarted) {\r\n              console.error(\"Backend startup error detected:\", output);\r\n              clearTimeout(startupTimeout);\r\n              resolve(false);\r\n            }\r\n          }\r\n        };\r\n\r\n        if (this.backendProcess.stdout) {\r\n          this.backendProcess.stdout.on(\"data\", checkStartup);\r\n        }\r\n\r\n        if (this.backendProcess.stderr) {\r\n          this.backendProcess.stderr.on(\"data\", checkStartupError);\r\n        }\r\n\r\n        this.backendProcess.on(\"error\", (err) => {\r\n          console.error(\"Failed to start backend service:\", err);\r\n\r\n          if (this.backendPID !== null) {\r\n            const index = this.runningBackendProcesses.indexOf(this.backendPID);\r\n            if (index !== -1) {\r\n              this.runningBackendProcesses.splice(index, 1);\r\n            }\r\n          }\r\n\r\n          this.backendProcess = null;\r\n          this.backendPID = null;\r\n\r\n          if (!isStarted) {\r\n            clearTimeout(startupTimeout);\r\n            resolve(false);\r\n          }\r\n        });\r\n\r\n        this.backendProcess.on(\"close\", (code) => {\r\n          console.log(`Backend service exited with code ${code}`);\r\n\r\n          if (this.backendPID !== null) {\r\n            const index = this.runningBackendProcesses.indexOf(this.backendPID);\r\n            if (index !== -1) {\r\n              this.runningBackendProcesses.splice(index, 1);\r\n            }\r\n          }\r\n\r\n          this.backendProcess = null;\r\n          this.backendPID = null;\r\n\r\n          if (!isStarted && !this.isShuttingDown) {\r\n            clearTimeout(startupTimeout);\r\n            resolve(false);\r\n          }\r\n        });\r\n\r\n        startupTimeout = setTimeout(() => {\r\n          if (!isStarted) {\r\n            console.error(\"Backend service startup timeout\");\r\n            console.log(\r\n              `Startup status: hasFlaskApp=${hasFlaskApp}, hasRunningOn=${hasRunningOn}`,\r\n            );\r\n            resolve(false);\r\n          }\r\n        }, 45000);\r\n      } catch (error) {\r\n        console.error(\"Error starting backend service:\", error);\r\n        this.backendProcess = null;\r\n        this.backendPID = null;\r\n        resolve(false);\r\n      }\r\n    });\r\n  }\r\n\r\n  /**\r\n   * 启动RabbitMQ服务\r\n   */\r\n  async startRabbitMQ(): Promise<boolean> {\r\n    return new Promise(async (resolve) => {\r\n      try {\r\n        const existingPids = await checkExistingRabbitMQProcesses();\r\n\r\n        if (existingPids.length > 0) {\r\n          console.log(\r\n            `Terminating ${existingPids.length} existing RabbitMQ processes before starting new one`,\r\n          );\r\n          for (const pid of existingPids) {\r\n            await this.killProcess(pid);\r\n          }\r\n          await new Promise((resolve) => setTimeout(resolve, 2000));\r\n        }\r\n\r\n        const rabbitmqScriptPath = app.isPackaged\r\n          ? join(process.resourcesPath, \"start_rabbitmq.bat\")\r\n          : join(app.getAppPath(), \"../start_rabbitmq.bat\");\r\n\r\n        console.log(`Starting RabbitMQ service from: ${rabbitmqScriptPath}`);\r\n\r\n        if (!existsSync(rabbitmqScriptPath)) {\r\n          console.error(`RabbitMQ script not found at: ${rabbitmqScriptPath}`);\r\n          resolve(false);\r\n          return;\r\n        }\r\n\r\n        console.log(`RabbitMQ script exists, attempting to start service`);\r\n\r\n        const workingDir = app.isPackaged\r\n          ? dirname(rabbitmqScriptPath)\r\n          : join(app.getAppPath(), \"..\");\r\n\r\n        console.log(`RabbitMQ working directory: ${workingDir}`);\r\n\r\n        this.rabbitmqProcess = spawn(rabbitmqScriptPath, [], {\r\n          windowsHide: false,\r\n          stdio: \"pipe\",\r\n          cwd: workingDir,\r\n          shell: true,\r\n        });\r\n\r\n        if (!this.rabbitmqProcess || !this.rabbitmqProcess.pid) {\r\n          console.error(\"Failed to start RabbitMQ: Invalid process\");\r\n          resolve(false);\r\n          return;\r\n        }\r\n\r\n        console.log(\r\n          \"RabbitMQ process started with PID:\",\r\n          this.rabbitmqProcess.pid,\r\n        );\r\n\r\n        let startupTimeout: NodeJS.Timeout;\r\n        let isStarted = false;\r\n\r\n        const checkStartup = (data: Buffer) => {\r\n          const output = data.toString();\r\n          console.log(\"RabbitMQ output:\", output);\r\n\r\n          if (\r\n            output.includes(\"completed with\") ||\r\n            output.includes(\"started\") ||\r\n            (output.includes(\"RabbitMQ\") && output.includes(\"running\")) ||\r\n            output.includes(\"broker running\") ||\r\n            output.includes(\"Starting broker\")\r\n          ) {\r\n            isStarted = true;\r\n            clearTimeout(startupTimeout);\r\n            console.log(\"RabbitMQ service started successfully\");\r\n            resolve(true);\r\n          }\r\n\r\n          if (\r\n            output.includes(\"error\") ||\r\n            output.includes(\"failed\") ||\r\n            output.includes(\"could not start\")\r\n          ) {\r\n            console.error(\"RabbitMQ startup error detected:\", output);\r\n            clearTimeout(startupTimeout);\r\n            resolve(false);\r\n          }\r\n        };\r\n\r\n        if (this.rabbitmqProcess.stdout) {\r\n          this.rabbitmqProcess.stdout.on(\"data\", checkStartup);\r\n        }\r\n\r\n        if (this.rabbitmqProcess.stderr) {\r\n          this.rabbitmqProcess.stderr.on(\"data\", (data) => {\r\n            const errorOutput = data.toString();\r\n            console.error(\"RabbitMQ error:\", errorOutput);\r\n            checkStartup(data);\r\n          });\r\n        }\r\n\r\n        this.rabbitmqProcess.on(\"error\", (err) => {\r\n          console.error(\"Failed to start RabbitMQ service:\", err);\r\n          this.rabbitmqProcess = null;\r\n\r\n          if (!isStarted) {\r\n            clearTimeout(startupTimeout);\r\n            resolve(false);\r\n          }\r\n        });\r\n\r\n        this.rabbitmqProcess.on(\"close\", (code) => {\r\n          console.log(`RabbitMQ service exited with code ${code}`);\r\n          this.rabbitmqProcess = null;\r\n\r\n          if (!isStarted && !this.isShuttingDown) {\r\n            clearTimeout(startupTimeout);\r\n            resolve(false);\r\n          }\r\n        });\r\n\r\n        startupTimeout = setTimeout(() => {\r\n          if (!isStarted) {\r\n            console.error(\"RabbitMQ service startup timeout\");\r\n            resolve(false);\r\n          }\r\n        }, 60000);\r\n      } catch (error) {\r\n        console.error(\"Error starting RabbitMQ service:\", error);\r\n        this.rabbitmqProcess = null;\r\n        resolve(false);\r\n      }\r\n    });\r\n  }\r\n\r\n  /**\r\n   * 终止RabbitMQ服务\r\n   */\r\n  async terminateRabbitMQ(): Promise<void> {\r\n    return new Promise((resolve) => {\r\n      if (this.rabbitmqProcess === null) {\r\n        console.log(\"RabbitMQ process is already null\");\r\n        resolve();\r\n        return;\r\n      }\r\n\r\n      console.log(\"Terminating RabbitMQ service...\");\r\n\r\n      try {\r\n        if (process.platform === \"win32\" && this.rabbitmqProcess.pid) {\r\n          exec(`taskkill /F /T /PID ${this.rabbitmqProcess.pid}`, (err) => {\r\n            if (err) {\r\n              console.error(\"Error terminating RabbitMQ process:\", err);\r\n            } else {\r\n              console.log(\"RabbitMQ service terminated successfully.\");\r\n            }\r\n            this.rabbitmqProcess = null;\r\n            resolve();\r\n          });\r\n        } else {\r\n          this.rabbitmqProcess.kill();\r\n          console.log(\"RabbitMQ service terminated successfully.\");\r\n          this.rabbitmqProcess = null;\r\n          resolve();\r\n        }\r\n      } catch (error) {\r\n        console.error(\"Error terminating RabbitMQ service:\", error);\r\n        this.rabbitmqProcess = null;\r\n        resolve();\r\n      }\r\n    });\r\n  }\r\n\r\n  /**\r\n   * 终止所有后端进程\r\n   */\r\n  async terminateAllBackendProcesses() {\r\n    console.log(\"Terminating all backend processes...\");\r\n\r\n    if (this.backendProcess !== null && this.backendPID !== null) {\r\n      try {\r\n        await this.killProcess(this.backendPID);\r\n      } catch (error) {\r\n        console.error(\r\n          `Error terminating current backend process ${this.backendPID}:`,\r\n          error,\r\n        );\r\n      }\r\n      this.backendProcess = null;\r\n      this.backendPID = null;\r\n    }\r\n\r\n    const processes = [...this.runningBackendProcesses];\r\n    for (const pid of processes) {\r\n      await this.killProcess(pid);\r\n    }\r\n\r\n    const remainingPids = await checkExistingBackendProcesses();\r\n    for (const pid of remainingPids) {\r\n      await this.killProcess(pid);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 终止所有RabbitMQ相关进程\r\n   */\r\n  async terminateAllRabbitMQProcesses() {\r\n    console.log(\"Terminating all RabbitMQ processes...\");\r\n\r\n    if (this.rabbitmqProcess !== null && this.rabbitmqProcess.pid) {\r\n      try {\r\n        console.log(\r\n          `Terminating current RabbitMQ process with PID: ${this.rabbitmqProcess.pid}`,\r\n        );\r\n        await this.terminateRabbitMQ();\r\n      } catch (error) {\r\n        console.error(`Error terminating current RabbitMQ process:`, error);\r\n      }\r\n      this.rabbitmqProcess = null;\r\n    }\r\n\r\n    try {\r\n      const remainingPids = await checkExistingRabbitMQProcesses();\r\n      console.log(\r\n        `Found ${remainingPids.length} remaining RabbitMQ processes to terminate`,\r\n      );\r\n      for (const pid of remainingPids) {\r\n        await this.killProcess(pid);\r\n      }\r\n    } catch (error) {\r\n      console.error(`Error checking for remaining RabbitMQ processes:`, error);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 设置关闭标志\r\n   */\r\n  setShuttingDown(value: boolean) {\r\n    this.isShuttingDown = value;\r\n  }\r\n\r\n  /**\r\n   * 获取后端进程状态\r\n   */\r\n  getBackendStatus() {\r\n    return {\r\n      process: this.backendProcess,\r\n      pid: this.backendPID,\r\n      runningProcesses: [...this.runningBackendProcesses],\r\n    };\r\n  }\r\n\r\n  /**\r\n   * 获取RabbitMQ进程状态\r\n   */\r\n  getRabbitMQStatus() {\r\n    return {\r\n      process: this.rabbitmqProcess,\r\n      pid: this.rabbitmqProcess?.pid || null,\r\n    };\r\n  }\r\n}\r\n", "import { BrowserWindow } from \"electron\";\r\nimport { join } from \"node:path\";\r\n\r\n/**\r\n * 窗口管理器 - 负责创建和管理各种窗口\r\n */\r\nexport class WindowManager {\r\n  private mainWindow: BrowserWindow | null = null;\r\n  private loadingWindow: BrowserWindow | null = null;\r\n  private allWindows = new Map<string, BrowserWindow>();\r\n  private taskWindowMap = new Map<string, BrowserWindow>();\r\n  private preFetchedResults = new Map<string, any>();\r\n  private pendingModelCompleteEvents = new Map<string, any>();\r\n\r\n  private preload = join(__dirname, \"../preload/index.js\");\r\n  private url = process.env.VITE_DEV_SERVER_URL;\r\n  private indexHtml = join(process.env.DIST, \"index.html\");\r\n\r\n  /**\r\n   * 创建闪屏窗口\r\n   */\r\n  createLoadingWindow() {\r\n    const loadingPreload = join(__dirname, \"../preload/loading.js\");\r\n\r\n    this.loadingWindow = new BrowserWindow({\r\n      width: 450,\r\n      height: 300,\r\n      icon: join(process.env.PUBLIC, \"favicon.ico\"),\r\n      frame: false,\r\n      transparent: true,\r\n      resizable: false,\r\n      webPreferences: {\r\n        preload: loadingPreload,\r\n        contextIsolation: true,\r\n        nodeIntegration: false,\r\n      },\r\n    });\r\n\r\n    const loadingHtmlPath = join(process.env.PUBLIC, \"loading.html\");\r\n    this.loadingWindow.loadFile(loadingHtmlPath);\r\n\r\n    this.loadingWindow.on(\"closed\", () => {\r\n      this.loadingWindow = null;\r\n    });\r\n\r\n    return this.loadingWindow;\r\n  }\r\n\r\n  /**\r\n   * 创建主窗口\r\n   */\r\n  async createMainWindow(initialRoute?: string) {\r\n    this.mainWindow = new BrowserWindow({\r\n      show: false,\r\n      width: 1024,\r\n      height: 768,\r\n      minWidth: 1024,\r\n      minHeight: 768,\r\n      title: \"ML Desktop\",\r\n      icon: join(process.env.PUBLIC, \"favicon.ico\"),\r\n      frame: false,\r\n      transparent: true,\r\n      resizable: true,\r\n      webPreferences: {\r\n        preload: this.preload,\r\n        nodeIntegration: false,\r\n        contextIsolation: true,\r\n      },\r\n    });\r\n\r\n    const targetUrl = initialRoute ? `${this.url}#${initialRoute}` : this.url;\r\n    const targetIndexHtml = initialRoute\r\n      ? { pathname: this.indexHtml, hash: initialRoute }\r\n      : this.indexHtml;\r\n\r\n    if (process.env.VITE_DEV_SERVER_URL) {\r\n      this.mainWindow.loadURL(targetUrl);\r\n      this.mainWindow.webContents.openDevTools({ mode: \"bottom\" });\r\n    } else {\r\n      this.mainWindow.loadFile(\r\n        typeof targetIndexHtml === \"string\"\r\n          ? targetIndexHtml\r\n          : targetIndexHtml.pathname,\r\n        typeof targetIndexHtml === \"string\"\r\n          ? {}\r\n          : { hash: targetIndexHtml.hash },\r\n      );\r\n    }\r\n\r\n    // 设置窗口打开处理器\r\n    this.mainWindow.webContents.setWindowOpenHandler(({ url }) => {\r\n      const isModelResultWindow = url.includes(\"/modelManagement/\");\r\n      const childWindow = new BrowserWindow({\r\n        width: 1024,\r\n        height: 768,\r\n        minWidth: 1024,\r\n        minHeight: 768,\r\n        autoHideMenuBar: true,\r\n        frame: !isModelResultWindow,\r\n        transparent: isModelResultWindow,\r\n        backgroundColor: isModelResultWindow ? \"#00000000\" : \"#fff\",\r\n        icon: join(process.env.PUBLIC, \"favicon.ico\"),\r\n        webPreferences: {\r\n          preload: this.preload,\r\n          nodeIntegration: false,\r\n          contextIsolation: true,\r\n        },\r\n        ...(isModelResultWindow\r\n          ? {\r\n              show: false,\r\n            }\r\n          : {}),\r\n      });\r\n\r\n      childWindow.loadURL(url);\r\n\r\n      // 设置拖拽优化逻辑\r\n      this.setupDragOptimization(childWindow);\r\n\r\n      if (isModelResultWindow) {\r\n        childWindow.once(\"ready-to-show\", () => {\r\n          childWindow.show();\r\n        });\r\n      }\r\n\r\n      return { action: \"deny\" };\r\n    });\r\n\r\n    // 设置主窗口拖拽优化逻辑\r\n    this.setupDragOptimization(this.mainWindow);\r\n\r\n    // 设置窗口状态事件监听\r\n    this.setupWindowStateEvents(this.mainWindow);\r\n\r\n    return this.mainWindow;\r\n  }\r\n\r\n  /**\r\n   * 创建子窗口\r\n   */\r\n  createChildWindow(url: string): BrowserWindow {\r\n    const childWindow = new BrowserWindow({\r\n      icon: join(process.env.PUBLIC, \"favicon.ico\"),\r\n      webPreferences: {\r\n        preload: this.preload,\r\n        nodeIntegration: false,\r\n        contextIsolation: true,\r\n      },\r\n    });\r\n\r\n    if (process.env.VITE_DEV_SERVER_URL) {\r\n      childWindow.loadURL(`${this.url}#${url}`);\r\n    } else {\r\n      childWindow.loadFile(this.indexHtml, { hash: url });\r\n    }\r\n\r\n    // 设置拖拽优化逻辑\r\n    this.setupDragOptimization(childWindow);\r\n\r\n    return childWindow;\r\n  }\r\n\r\n  /**\r\n   * 获取主窗口\r\n   */\r\n  getMainWindow(): BrowserWindow | null {\r\n    return this.mainWindow;\r\n  }\r\n\r\n  /**\r\n   * 获取加载窗口\r\n   */\r\n  getLoadingWindow(): BrowserWindow | null {\r\n    return this.loadingWindow;\r\n  }\r\n\r\n  /**\r\n   * 关闭加载窗口\r\n   */\r\n  closeLoadingWindow() {\r\n    if (this.loadingWindow && !this.loadingWindow.isDestroyed()) {\r\n      this.loadingWindow.close();\r\n      this.loadingWindow = null;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 显示主窗口\r\n   */\r\n  showMainWindow() {\r\n    if (this.mainWindow && !this.mainWindow.isDestroyed()) {\r\n      this.mainWindow.show();\r\n      this.mainWindow.maximize();\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 注册任务窗口映射\r\n   */\r\n  registerTaskWindow(uid: string, window: BrowserWindow) {\r\n    this.taskWindowMap.set(uid, window);\r\n  }\r\n\r\n  /**\r\n   * 获取任务窗口\r\n   */\r\n  getTaskWindow(uid: string): BrowserWindow | undefined {\r\n    return this.taskWindowMap.get(uid);\r\n  }\r\n\r\n  /**\r\n   * 缓存预取结果\r\n   */\r\n  cachePreFetchedResult(uid: string, result: any) {\r\n    this.preFetchedResults.set(uid, result);\r\n  }\r\n\r\n  /**\r\n   * 获取预取结果\r\n   */\r\n  getPreFetchedResult(uid: string): any {\r\n    return this.preFetchedResults.get(uid);\r\n  }\r\n\r\n  /**\r\n   * 删除预取结果\r\n   */\r\n  deletePreFetchedResult(uid: string) {\r\n    this.preFetchedResults.delete(uid);\r\n  }\r\n\r\n  /**\r\n   * 缓存待处理的模型完成事件\r\n   */\r\n  cachePendingModelCompleteEvent(uid: string, data: any) {\r\n    this.pendingModelCompleteEvents.set(uid, data);\r\n  }\r\n\r\n  /**\r\n   * 获取待处理的模型完成事件\r\n   */\r\n  getPendingModelCompleteEvent(uid: string): any {\r\n    return this.pendingModelCompleteEvents.get(uid);\r\n  }\r\n\r\n  /**\r\n   * 删除待处理的模型完成事件\r\n   */\r\n  deletePendingModelCompleteEvent(uid: string) {\r\n    this.pendingModelCompleteEvents.delete(uid);\r\n  }\r\n\r\n  /**\r\n   * 窗口控制 - 最小化\r\n   */\r\n  minimizeWindow(window?: BrowserWindow) {\r\n    const targetWindow = window || this.mainWindow;\r\n    if (targetWindow && !targetWindow.isDestroyed()) {\r\n      targetWindow.minimize();\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 窗口控制 - 最大化/还原\r\n   */\r\n  toggleMaximizeWindow(window?: BrowserWindow) {\r\n    const targetWindow = window || this.mainWindow;\r\n    if (targetWindow && !targetWindow.isDestroyed()) {\r\n      if (targetWindow.isMaximized()) {\r\n        targetWindow.unmaximize();\r\n      } else {\r\n        targetWindow.maximize();\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 窗口控制 - 关闭\r\n   */\r\n  closeWindow(window?: BrowserWindow) {\r\n    const targetWindow = window || this.mainWindow;\r\n    if (targetWindow && !targetWindow.isDestroyed()) {\r\n      targetWindow.close();\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 设置窗口拖拽优化逻辑\r\n   * 当最大化窗口被拖拽时，先还原窗口大小再移动\r\n   */\r\n  private setupDragOptimization(window: BrowserWindow) {\r\n    if (!window || window.isDestroyed()) return;\r\n\r\n    let isProcessingMove = false;\r\n\r\n    // 使用 will-move 事件来处理拖拽优化\r\n    window.on(\"will-move\", (event) => {\r\n      if (window.isMaximized() && !isProcessingMove) {\r\n        // 防止递归调用\r\n        isProcessingMove = true;\r\n\r\n        // 阻止默认移动行为\r\n        event.preventDefault();\r\n\r\n        // 先还原窗口\r\n        window.unmaximize();\r\n\r\n        // 在下一个事件循环中重置标志位\r\n        // 这样可以确保 unmaximize 完成后再重置\r\n        process.nextTick(() => {\r\n          isProcessingMove = false;\r\n        });\r\n      }\r\n    });\r\n\r\n    // 监听窗口状态变化，确保标志位正确重置\r\n    window.on(\"unmaximize\", () => {\r\n      // 当窗口从最大化状态还原时，确保标志位被重置\r\n      isProcessingMove = false;\r\n    });\r\n  }\r\n\r\n  /**\r\n   * 设置窗口状态事件监听\r\n   * 向渲染进程发送窗口状态变化事件\r\n   */\r\n  private setupWindowStateEvents(window: BrowserWindow) {\r\n    if (!window || window.isDestroyed()) return;\r\n\r\n    // 监听窗口最大化事件\r\n    window.on(\"maximize\", () => {\r\n      window.webContents.send(\"window-maximized\");\r\n    });\r\n\r\n    // 监听窗口还原事件\r\n    window.on(\"unmaximize\", () => {\r\n      window.webContents.send(\"window-unmaximized\");\r\n    });\r\n\r\n    // 监听窗口还原事件（从最小化状态）\r\n    window.on(\"restore\", () => {\r\n      window.webContents.send(\"window-restored\");\r\n    });\r\n  }\r\n\r\n  /**\r\n   * 清理资源\r\n   */\r\n  cleanup() {\r\n    this.allWindows.clear();\r\n    this.taskWindowMap.clear();\r\n    this.preFetchedResults.clear();\r\n    this.pendingModelCompleteEvents.clear();\r\n  }\r\n}\r\n", "import fs from \"fs\";\r\nimport path from \"path\";\r\nimport { app, dialog, type BrowserWindow } from \"electron\";\r\n\r\nconst mammoth = require(\"mammoth\");\r\n\r\n// 文件信息接口\r\nexport interface FileInfo {\r\n  path: string;\r\n  name: string;\r\n  extension: string;\r\n  type: string;\r\n  exists: boolean;\r\n}\r\n\r\n/**\r\n * 文件系统处理器 - 负责所有文件系统相关操作\r\n */\r\nexport class FileSystemHandler {\r\n  private static instance: FileSystemHandler;\r\n\r\n  static getInstance(): FileSystemHandler {\r\n    if (!FileSystemHandler.instance) {\r\n      FileSystemHandler.instance = new FileSystemHandler();\r\n    }\r\n    return FileSystemHandler.instance;\r\n  }\r\n\r\n  // 文件类型定义\r\n  private readonly fileTypes = {\r\n    excel: [\".xlsx\", \".xls\", \".csv\", \".xlsm\", \".xlsb\"],\r\n    markdown: [\".md\", \".markdown\"],\r\n    text: [\".txt\", \".log\"],\r\n    code: [\".ts\", \".js\", \".py\", \".json\", \".html\", \".css\"],\r\n  };\r\n\r\n  // 文件过滤器\r\n  readonly filters = {\r\n    excel: [\r\n      {\r\n        name: \"Excel文件\",\r\n        extensions: [\"xlsx\", \"xls\", \"csv\", \"xlsm\", \"xlsb\"],\r\n      },\r\n    ],\r\n    markdown: [\r\n      {\r\n        name: \"Markdown文件\",\r\n        extensions: [\"md\", \"markdown\"],\r\n      },\r\n    ],\r\n    text: [\r\n      {\r\n        name: \"文本文件\",\r\n        extensions: [\"txt\", \"log\"],\r\n      },\r\n    ],\r\n    code: [\r\n      {\r\n        name: \"代码文件\",\r\n        extensions: [\"ts\", \"js\", \"py\", \"json\", \"html\", \"css\"],\r\n      },\r\n    ],\r\n    all: [\r\n      {\r\n        name: \"所有文件\",\r\n        extensions: [\"*\"],\r\n      },\r\n    ],\r\n  };\r\n\r\n  /**\r\n   * 检测文件类型\r\n   */\r\n  private getFileType(filePath: string): {\r\n    type: string;\r\n    category: string;\r\n    supported: boolean;\r\n  } {\r\n    const ext = path.extname(filePath).toLowerCase();\r\n\r\n    const textExtensions = [\r\n      \".txt\",\r\n      \".md\",\r\n      \".json\",\r\n      \".xml\",\r\n      \".html\",\r\n      \".css\",\r\n      \".js\",\r\n      \".ts\",\r\n      \".vue\",\r\n      \".py\",\r\n      \".java\",\r\n      \".cpp\",\r\n      \".c\",\r\n      \".h\",\r\n      \".sql\",\r\n      \".log\",\r\n      \".ini\",\r\n      \".cfg\",\r\n      \".conf\",\r\n      \".yaml\",\r\n      \".yml\",\r\n    ];\r\n\r\n    const officeExtensions = [\r\n      \".doc\",\r\n      \".docx\",\r\n      \".xls\",\r\n      \".xlsx\",\r\n      \".ppt\",\r\n      \".pptx\",\r\n    ];\r\n    const pdfExtensions = [\".pdf\"];\r\n    const imageExtensions = [\r\n      \".jpg\",\r\n      \".jpeg\",\r\n      \".png\",\r\n      \".gif\",\r\n      \".bmp\",\r\n      \".svg\",\r\n      \".ico\",\r\n    ];\r\n    const archiveExtensions = [\".zip\", \".rar\", \".7z\", \".tar\", \".gz\"];\r\n    const executableExtensions = [\r\n      \".exe\",\r\n      \".msi\",\r\n      \".dmg\",\r\n      \".app\",\r\n      \".deb\",\r\n      \".rpm\",\r\n    ];\r\n    const mediaExtensions = [\".mp3\", \".mp4\", \".avi\", \".mov\", \".wav\", \".flac\"];\r\n\r\n    if (textExtensions.includes(ext)) {\r\n      return { type: ext, category: \"text\", supported: true };\r\n    } else if (officeExtensions.includes(ext)) {\r\n      return { type: ext, category: \"office\", supported: true };\r\n    } else if (pdfExtensions.includes(ext)) {\r\n      return { type: ext, category: \"pdf\", supported: true };\r\n    } else if (imageExtensions.includes(ext)) {\r\n      return { type: ext, category: \"image\", supported: true };\r\n    } else if (archiveExtensions.includes(ext)) {\r\n      return { type: ext, category: \"archive\", supported: false };\r\n    } else if (executableExtensions.includes(ext)) {\r\n      return { type: ext, category: \"executable\", supported: false };\r\n    } else if (mediaExtensions.includes(ext)) {\r\n      return { type: ext, category: \"media\", supported: false };\r\n    } else {\r\n      return { type: ext, category: \"unknown\", supported: false };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 获取图像文件的 MIME 类型\r\n   */\r\n  private getMimeType(extension: string): string {\r\n    const mimeTypes: Record<string, string> = {\r\n      \".jpg\": \"image/jpeg\",\r\n      \".jpeg\": \"image/jpeg\",\r\n      \".png\": \"image/png\",\r\n      \".gif\": \"image/gif\",\r\n      \".bmp\": \"image/bmp\",\r\n      \".svg\": \"image/svg+xml\",\r\n      \".ico\": \"image/x-icon\",\r\n      \".webp\": \"image/webp\",\r\n    };\r\n    return mimeTypes[extension.toLowerCase()] || \"image/jpeg\";\r\n  }\r\n\r\n  /**\r\n   * 获取不支持文件类型的提示信息\r\n   */\r\n  private getUnsupportedMessage(fileInfo: { type: string; category: string }) {\r\n    switch (fileInfo.category) {\r\n      case \"image\":\r\n        return \"图片文件不支持文本编辑，请使用图片查看器打开\";\r\n      case \"archive\":\r\n        return \"压缩文件不支持直接编辑，请先解压缩\";\r\n      case \"executable\":\r\n        return \"可执行文件不支持编辑\";\r\n      case \"media\":\r\n        return \"音视频文件不支持文本编辑，请使用媒体播放器打开\";\r\n      default:\r\n        return \"不支持的文件类型，无法在文本编辑器中打开\";\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 提取 DOCX 文档的文本内容\r\n   */\r\n  private async extractDocxText(filePath: string): Promise<string> {\r\n    try {\r\n      try {\r\n        const result = await mammoth.extractRawText({ path: filePath });\r\n        return result.value || \"无法提取文档内容\";\r\n      } catch {\r\n        console.log(\"Mammoth not available\");\r\n        return `Word 文档预览\\n\\n文件路径: ${filePath}\\n\\n注意：无法提取文档内容，请使用 Microsoft Word 或其他兼容软件打开此文件。\\n\\n要完整支持 Word 文档，请安装 mammoth 库：npm install mammoth`;\r\n      }\r\n    } catch (error: any) {\r\n      console.error(\"Error extracting DOCX text:\", error);\r\n      return `Word 文档预览\\n\\n文件路径: ${filePath}\\n\\n错误：无法读取文档内容 - ${error?.message || \"Unknown error\"}`;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 读取目录内容\r\n   */\r\n  async readDirectory(dirPath: string) {\r\n    const files = await fs.promises.readdir(dirPath, { withFileTypes: true });\r\n    return files.map((dirent) => ({\r\n      name: dirent.name,\r\n      isDirectory: dirent.isDirectory(),\r\n      path: path.join(dirPath, dirent.name),\r\n    }));\r\n  }\r\n\r\n  /**\r\n   * 创建新目录\r\n   */\r\n  async createDirectory(targetPath: string) {\r\n    await fs.promises.mkdir(targetPath, { recursive: true });\r\n    return { success: true };\r\n  }\r\n\r\n  /**\r\n   * 创建新文件\r\n   */\r\n  async createFile(filePath: string) {\r\n    await fs.promises.writeFile(filePath, \"\");\r\n    return { success: true };\r\n  }\r\n\r\n  /**\r\n   * 删除文件/目录\r\n   */\r\n  async deletePath(targetPath: string) {\r\n    const stats = await fs.promises.stat(targetPath);\r\n    if (stats.isDirectory()) {\r\n      await fs.promises.rmdir(targetPath, { recursive: true });\r\n    } else {\r\n      await fs.promises.unlink(targetPath);\r\n    }\r\n    return { success: true };\r\n  }\r\n\r\n  /**\r\n   * 重命名文件/目录\r\n   */\r\n  async rename(oldPath: string, newPath: string) {\r\n    try {\r\n      await fs.promises.rename(oldPath, newPath);\r\n      return { success: true };\r\n    } catch (error) {\r\n      console.error(\"Error renaming file/directory:\", error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 检查文件/目录是否存在\r\n   */\r\n  async access(filePath: string) {\r\n    try {\r\n      await fs.promises.access(filePath);\r\n      return { exists: true };\r\n    } catch {\r\n      return { exists: false };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 读取文件内容\r\n   */\r\n  async readFile(filePath: string) {\r\n    try {\r\n      const content = await fs.promises.readFile(filePath, \"utf-8\");\r\n      return content;\r\n    } catch (error) {\r\n      console.error(\"Error reading file:\", error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 检测文件类型和读取内容\r\n   */\r\n  async readFileWithType(filePath: string) {\r\n    try {\r\n      const fileInfo = this.getFileType(filePath);\r\n\r\n      if (!fileInfo.supported) {\r\n        return {\r\n          success: false,\r\n          fileInfo,\r\n          error: `不支持的文件类型: ${fileInfo.type}`,\r\n          message: this.getUnsupportedMessage(fileInfo),\r\n        };\r\n      }\r\n\r\n      let content = \"\";\r\n      let imageData = null;\r\n      let rawData: Buffer | null = null;\r\n\r\n      if (fileInfo.category === \"text\") {\r\n        content = await fs.promises.readFile(filePath, \"utf-8\");\r\n      } else if (fileInfo.category === \"office\") {\r\n        if (fileInfo.type === \".docx\") {\r\n          content = await this.extractDocxText(filePath);\r\n        } else if (fileInfo.type === \".doc\") {\r\n          content = \"暂不支持 .doc 格式，请转换为 .docx 格式\";\r\n        } else {\r\n          content = `不支持的Office 文档 (${fileInfo.type})，请使用Office工具进行编辑`;\r\n        }\r\n      } else if (fileInfo.category === \"pdf\") {\r\n        rawData = await fs.promises.readFile(filePath);\r\n        content = \"\";\r\n      } else if (fileInfo.category === \"image\") {\r\n        const imageBuffer = await fs.promises.readFile(filePath);\r\n        const base64Data = imageBuffer.toString(\"base64\");\r\n        const mimeType = this.getMimeType(fileInfo.type);\r\n        imageData = `data:${mimeType};base64,${base64Data}`;\r\n        content = \"\";\r\n      }\r\n\r\n      return {\r\n        success: true,\r\n        fileInfo,\r\n        content,\r\n        imageData,\r\n        rawData,\r\n      };\r\n    } catch (error: any) {\r\n      console.error(\"Error reading file with type:\", error);\r\n      return {\r\n        success: false,\r\n        fileInfo: this.getFileType(filePath),\r\n        error: error?.message || \"Unknown error\",\r\n        message: \"读取文件时发生错误\",\r\n      };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 读取文件内容为Buffer\r\n   */\r\n  async readFileBuffer(filePath: string) {\r\n    try {\r\n      const buffer = await fs.promises.readFile(filePath);\r\n      return buffer;\r\n    } catch (error) {\r\n      console.error(\"Error reading file buffer:\", error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 写入文件内容\r\n   */\r\n  async writeFile(filePath: string, content: string) {\r\n    try {\r\n      await fs.promises.writeFile(filePath, content, \"utf-8\");\r\n      return { success: true };\r\n    } catch (error) {\r\n      console.error(\"Error writing file:\", error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 写入文件内容 (Buffer)\r\n   */\r\n  async writeFileBuffer(filePath: string, arrayBuffer: ArrayBuffer) {\r\n    try {\r\n      const buffer = Buffer.from(arrayBuffer);\r\n      await fs.promises.writeFile(filePath, buffer);\r\n      return { success: true };\r\n    } catch (error) {\r\n      console.error(\"Error writing file buffer:\", error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 写入文件内容 (Base64Buffer)\r\n   */\r\n  async writeFileBase64Buffer(filePath: string, arrayBuffer: string) {\r\n    try {\r\n      const buffer = Buffer.from(arrayBuffer, \"base64\");\r\n      await fs.promises.writeFile(filePath, buffer);\r\n      return { success: true };\r\n    } catch (error) {\r\n      console.error(\"Error writing file buffer:\", error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 验证文件路径是否存在\r\n   */\r\n  async validatePath(filePath: string) {\r\n    try {\r\n      const exists = await fs.promises\r\n        .access(filePath)\r\n        .then(() => true)\r\n        .catch(() => false);\r\n      return { exists, path: filePath };\r\n    } catch (error) {\r\n      return { exists: false, path: filePath, error: error };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 获取最近文件路径\r\n   */\r\n  private getRecentFilesPath() {\r\n    return path.join(app.getPath(\"userData\"), \"recent-files.json\");\r\n  }\r\n\r\n  /**\r\n   * 获取最近文件\r\n   */\r\n  async getRecentFiles() {\r\n    try {\r\n      const recentFilesPath = this.getRecentFilesPath();\r\n      const exists = await fs.promises\r\n        .access(recentFilesPath)\r\n        .then(() => true)\r\n        .catch(() => false);\r\n\r\n      if (!exists) {\r\n        return { success: true, files: [] };\r\n      }\r\n\r\n      const fileContent = await fs.promises.readFile(recentFilesPath, \"utf-8\");\r\n      const files = JSON.parse(fileContent);\r\n\r\n      return { success: true, files: Array.isArray(files) ? files : [] };\r\n    } catch (error) {\r\n      console.error(\"获取最近文件失败:\", error);\r\n      return { success: false, files: [] };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 保存最近文件\r\n   */\r\n  async saveRecentFiles(files: any[]) {\r\n    try {\r\n      const recentFilesPath = this.getRecentFilesPath();\r\n      await fs.promises.writeFile(\r\n        recentFilesPath,\r\n        JSON.stringify(files, null, 2),\r\n        \"utf-8\",\r\n      );\r\n      return { success: true };\r\n    } catch (error) {\r\n      console.error(\"保存最近文件失败:\", error);\r\n      return { success: false, message: \"保存失败\" };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 创建文件信息对象\r\n   */\r\n  private createFileInfo(filePath: string): FileInfo {\r\n    return {\r\n      path: filePath,\r\n      name: path.basename(filePath),\r\n      extension: path.extname(filePath).toLowerCase(),\r\n      type: this.getFileTypeCategory(filePath),\r\n      exists: fs.existsSync(filePath),\r\n    };\r\n  }\r\n\r\n  /**\r\n   * 获取文件类型分类\r\n   */\r\n  private getFileTypeCategory(filePath: string): string {\r\n    const ext = path.extname(filePath).toLowerCase();\r\n    for (const [type, extensions] of Object.entries(this.fileTypes)) {\r\n      if (extensions.includes(ext)) {\r\n        return type;\r\n      }\r\n    }\r\n    return \"other\";\r\n  }\r\n\r\n  /**\r\n   * 打开文件对话框\r\n   */\r\n  async openFile(\r\n    window: BrowserWindow,\r\n    options: {\r\n      filters?: { name: string; extensions: string[] }[];\r\n      isDataImport?: boolean;\r\n    } = {},\r\n  ): Promise<FileInfo | null> {\r\n    try {\r\n      const { canceled, filePaths } = await dialog.showOpenDialog(window, {\r\n        properties: [\"openFile\"],\r\n        filters: options.filters || (this.filters.all as any),\r\n      });\r\n\r\n      if (!canceled && filePaths.length > 0) {\r\n        const fileInfo = this.createFileInfo(filePaths[0]);\r\n\r\n        if (\r\n          options.isDataImport &&\r\n          !this.fileTypes.excel.includes(fileInfo.extension as any)\r\n        ) {\r\n          await dialog.showMessageBox(window, {\r\n            type: \"error\",\r\n            title: \"文件类型错误\",\r\n            message: \"请选择Excel文件\",\r\n          });\r\n          return null;\r\n        }\r\n\r\n        // 发送统一的文件选择事件\r\n        window.webContents.send(\"file-selected\", fileInfo);\r\n        return fileInfo;\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error opening file:\", error);\r\n      dialog.showErrorBox(\"错误\", \"打开文件时发生错误\");\r\n    }\r\n    return null;\r\n  }\r\n\r\n  /**\r\n   * 导入项目\r\n   */\r\n  async importProject(\r\n    window: BrowserWindow,\r\n  ): Promise<{ path: string; name: string } | null> {\r\n    try {\r\n      const { canceled, filePaths } = await dialog.showOpenDialog(window, {\r\n        properties: [\"openDirectory\"],\r\n        title: \"选择项目文件夹\",\r\n      });\r\n\r\n      if (!canceled && filePaths.length > 0) {\r\n        const projectPath = filePaths[0];\r\n        const projectInfo = {\r\n          path: projectPath,\r\n          name: path.basename(projectPath),\r\n        };\r\n        window.webContents.send(\"project-imported\", projectInfo);\r\n        return projectInfo;\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error importing project:\", error);\r\n      dialog.showErrorBox(\"错误\", \"导入项目时发生错误\");\r\n    }\r\n    return null;\r\n  }\r\n\r\n  /**\r\n   * 处理文件菜单的打开文件操作\r\n   */\r\n  async handleOpenFile(win: BrowserWindow) {\r\n    if (!win || win.isDestroyed()) return;\r\n\r\n    // 获取当前窗口URL\r\n    const currentURL = win.webContents.getURL();\r\n    const isDataImportPage = currentURL.includes(\"/dataManagement/imandex\");\r\n\r\n    // 根据当前页面类型选择合适的过滤器\r\n    await this.openFile(win, {\r\n      filters: isDataImportPage\r\n        ? (this.filters.excel as any)\r\n        : (this.filters.all as any),\r\n      isDataImport: isDataImportPage,\r\n    });\r\n  }\r\n\r\n  /**\r\n   * 处理导入项目操作\r\n   */\r\n  async handleImportProject(win: BrowserWindow) {\r\n    if (!win || win.isDestroyed()) return;\r\n    await this.importProject(win);\r\n  }\r\n  /**\r\n   * 获取目录大小信息\r\n   */\r\n  async getDirectorySize(dirPath: string): Promise<{\r\n    size: number;\r\n    fileCount: number;\r\n  }> {\r\n    try {\r\n      let totalSize = 0;\r\n      let fileCount = 0;\r\n\r\n      const calculateSize = async (currentPath: string) => {\r\n        const stats = await fs.promises.stat(currentPath);\r\n\r\n        if (stats.isFile()) {\r\n          totalSize += stats.size;\r\n          fileCount++;\r\n        } else if (stats.isDirectory()) {\r\n          const entries = await fs.promises.readdir(currentPath);\r\n          for (const entry of entries) {\r\n            const entryPath = path.join(currentPath, entry);\r\n            await calculateSize(entryPath);\r\n          }\r\n        }\r\n      };\r\n\r\n      await calculateSize(dirPath);\r\n      return { size: totalSize, fileCount };\r\n    } catch (error) {\r\n      console.error(\"Error calculating directory size:\", error);\r\n      return { size: 0, fileCount: 0 };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 递归读取目录结构\r\n   */\r\n  async readDirectoryRecursive(dirPath: string): Promise<{\r\n    files: Array<{\r\n      relativePath: string;\r\n      fullPath: string;\r\n      isFile: boolean;\r\n    }>;\r\n  }> {\r\n    try {\r\n      const files: Array<{\r\n        relativePath: string;\r\n        fullPath: string;\r\n        isFile: boolean;\r\n      }> = [];\r\n\r\n      const readRecursive = async (currentPath: string, relativePath = \"\") => {\r\n        const entries = await fs.promises.readdir(currentPath, {\r\n          withFileTypes: true,\r\n        });\r\n\r\n        for (const entry of entries) {\r\n          const fullPath = path.join(currentPath, entry.name);\r\n          const relPath = relativePath\r\n            ? path.join(relativePath, entry.name)\r\n            : entry.name;\r\n\r\n          if (entry.isFile()) {\r\n            files.push({\r\n              relativePath: relPath,\r\n              fullPath: fullPath,\r\n              isFile: true,\r\n            });\r\n          } else if (entry.isDirectory()) {\r\n            files.push({\r\n              relativePath: relPath,\r\n              fullPath: fullPath,\r\n              isFile: false,\r\n            });\r\n            await readRecursive(fullPath, relPath);\r\n          }\r\n        }\r\n      };\r\n\r\n      await readRecursive(dirPath);\r\n      return { files };\r\n    } catch (error) {\r\n      console.error(\"Error reading directory recursively:\", error);\r\n      return { files: [] };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 检查路径是否存在\r\n   */\r\n  async pathExists(filePath: string): Promise<boolean> {\r\n    try {\r\n      await fs.promises.access(filePath);\r\n      return true;\r\n    } catch {\r\n      return false;\r\n    }\r\n  }\r\n}\r\n", "import { dialog, type BrowserWindow } from \"electron\";\r\nimport fs from \"fs\";\r\n\r\n/**\r\n * 对话框处理器 - 负责处理各种系统对话框\r\n */\r\nexport class DialogHandler {\r\n  /**\r\n   * 选择目录对话框\r\n   */\r\n  async openDirectory() {\r\n    const result = await dialog.showOpenDialog({\r\n      properties: [\"openDirectory\"],\r\n    });\r\n    return result.filePaths[0];\r\n  }\r\n\r\n  /**\r\n   * 选择文件对话框\r\n   */\r\n  async openFile() {\r\n    const result = await dialog.showOpenDialog({\r\n      properties: [\"openFile\"],\r\n    });\r\n    if (result.canceled || result.filePaths.length === 0) {\r\n      return null;\r\n    }\r\n    return result.filePaths[0];\r\n  }\r\n\r\n  /**\r\n   * 选择保存文件对话框\r\n   */\r\n  async saveFile(options: any) {\r\n    const result = await dialog.showSaveDialog({\r\n      ...options,\r\n      filters: [\r\n        { name: \"JSON Files\", extensions: [\"json\"] },\r\n        { name: \"All Files\", extensions: [\"*\"] },\r\n      ],\r\n    });\r\n    return result;\r\n  }\r\n\r\n  /**\r\n   * 项目导出对话框\r\n   */\r\n  async exportProject(projectData: any) {\r\n    try {\r\n      const result = await dialog.showSaveDialog({\r\n        title: \"导出项目\",\r\n        defaultPath: `project_${new Date().toISOString().slice(0, 19).replace(/:/g, \"-\")}.json`,\r\n        filters: [\r\n          { name: \"JSON Files\", extensions: [\"json\"] },\r\n          { name: \"All Files\", extensions: [\"*\"] },\r\n        ],\r\n      });\r\n\r\n      if (result.canceled || !result.filePath) {\r\n        return { success: false, message: \"用户取消导出\" };\r\n      }\r\n\r\n      await fs.promises.writeFile(\r\n        result.filePath,\r\n        JSON.stringify(projectData, null, 2),\r\n        \"utf-8\",\r\n      );\r\n      return {\r\n        success: true,\r\n        filePath: result.filePath,\r\n        message: \"项目导出成功\",\r\n      };\r\n    } catch (error) {\r\n      console.error(\"导出项目失败:\", error);\r\n      return { success: false, message: `导出失败` };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 项目导入对话框\r\n   */\r\n  async importProject() {\r\n    try {\r\n      const result = await dialog.showOpenDialog({\r\n        title: \"导入项目\",\r\n        properties: [\"openFile\"],\r\n        filters: [\r\n          { name: \"项目文件\", extensions: [\"zip\"] },\r\n          { name: \"All Files\", extensions: [\"*\"] },\r\n        ],\r\n      });\r\n\r\n      if (result.canceled || result.filePaths.length === 0) {\r\n        return { success: false, message: \"用户取消导入\" };\r\n      }\r\n\r\n      const filePath = result.filePaths[0];\r\n      // 新版二进制格式导入\r\n      const fileBuffer = await fs.promises.readFile(filePath);\r\n      return {\r\n        success: true,\r\n        fileBuffer,\r\n        filePath,\r\n        message: \"项目文件读取成功\",\r\n      };\r\n    } catch (error) {\r\n      console.error(\"导入项目失败:\", error);\r\n      return { success: false, message: `导入失败` };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * 选择新的文件路径\r\n   */\r\n  async selectNewPath(options: any) {\r\n    const result = await dialog.showOpenDialog({\r\n      title: options.title || \"选择新路径\",\r\n      properties: options.properties || [\"openDirectory\"],\r\n      defaultPath: options.defaultPath || \"\",\r\n    });\r\n\r\n    if (result.canceled || result.filePaths.length === 0) {\r\n      return null;\r\n    }\r\n\r\n    return result.filePaths[0];\r\n  }\r\n\r\n  /**\r\n   * 显示打开对话框 (带窗口上下文)\r\n   */\r\n  async showOpenDialog(window: BrowserWindow, options: any) {\r\n    if (!window) {\r\n      return { canceled: true, filePaths: [] };\r\n    }\r\n    return await dialog.showOpenDialog(window, options);\r\n  }\r\n\r\n  /**\r\n   * 显示保存对话框 (带窗口上下文)\r\n   */\r\n  async showSaveDialog(window: BrowserWindow, options: any) {\r\n    if (!window) {\r\n      return { canceled: true, filePath: \"\" };\r\n    }\r\n    return await dialog.showSaveDialog(window, options);\r\n  }\r\n\r\n  /**\r\n   * 显示消息框\r\n   */\r\n  async showMessageBox(window: BrowserWindow, options: any) {\r\n    if (!window) {\r\n      return { response: 0 };\r\n    }\r\n    return await dialog.showMessageBox(window, options);\r\n  }\r\n\r\n  /**\r\n   * 显示错误对话框\r\n   */\r\n  showErrorBox(title: string, content: string) {\r\n    dialog.showErrorBox(title, content);\r\n  }\r\n}\r\n", "import {\r\n  Menu,\r\n  type MenuItemConstructorOptions,\r\n  type MenuItem,\r\n  app,\r\n  BrowserWindow,\r\n  dialog,\r\n} from \"electron\";\r\nimport { existsSync, readFileSync } from \"node:fs\";\r\nimport path from \"path\";\r\n\r\n/**\r\n * 菜单管理器 - 负责创建和管理应用菜单\r\n */\r\nexport class MenuManager {\r\n  private isDev: boolean;\r\n\r\n  constructor(isDev: boolean = false) {\r\n    this.isDev = isDev;\r\n  }\r\n\r\n  /**\r\n   * 创建应用菜单\r\n   */\r\n  createMenu(label = \"进入全屏幕\") {\r\n    const menu = Menu.buildFromTemplate(\r\n      this.buildAppMenu(label) as (MenuItemConstructorOptions | MenuItem)[],\r\n    );\r\n    Menu.setApplicationMenu(menu);\r\n  }\r\n\r\n  /**\r\n   * 构建应用菜单模板\r\n   */\r\n  private buildAppMenu(\r\n    fullscreenLabel: string,\r\n  ): Array<MenuItemConstructorOptions | MenuItem> {\r\n    const devMenuItems: MenuItemConstructorOptions[] = [];\r\n    if (this.isDev) {\r\n      devMenuItems.push(\r\n        { label: \"开发者工具\", role: \"toggleDevTools\" },\r\n        { label: \"强制刷新\", role: \"forceReload\" },\r\n      );\r\n    }\r\n\r\n    const template: Array<MenuItemConstructorOptions | MenuItem> = [\r\n      {\r\n        label: \"文件\",\r\n        submenu: [\r\n          {\r\n            label: \"打开文件夹...\",\r\n            accelerator: \"CmdOrCtrl+Shift+O\",\r\n            toolTip: \"打开现有项目文件夹\",\r\n            click: async () => {\r\n              const win = BrowserWindow.getFocusedWindow();\r\n              if (win && !win.isDestroyed()) {\r\n                win.focus();\r\n                win.webContents.send(\"menu-triggered-import-project\");\r\n              }\r\n            },\r\n          },\r\n          {\r\n            label: \"打开文件...\",\r\n            accelerator: \"CmdOrCtrl+O\",\r\n            toolTip: \"打开单个文件进行编辑\",\r\n            click: async () => {\r\n              const win = BrowserWindow.getFocusedWindow();\r\n              if (win && !win.isDestroyed()) {\r\n                win.focus();\r\n                const currentURL = win.webContents.getURL();\r\n                const isDataImportPage = currentURL.includes(\r\n                  \"/dataManagement/imandex\",\r\n                );\r\n\r\n                if (isDataImportPage) {\r\n                  win.webContents.send(\"menu-triggered-open-file\");\r\n                } else {\r\n                  // Handle file opening logic here\r\n                  win.webContents.send(\"menu-triggered-open-file\");\r\n                }\r\n              }\r\n            },\r\n          },\r\n          { type: \"separator\" },\r\n          {\r\n            label: \"项目管理\",\r\n            submenu: [\r\n              {\r\n                label: \"导出项目\",\r\n                accelerator: \"CmdOrCtrl+Shift+E\",\r\n                toolTip: \"导出当前项目状态，包括标签页、工作区等信息\",\r\n                click: async () => {\r\n                  const win = BrowserWindow.getFocusedWindow();\r\n                  if (win && !win.isDestroyed()) {\r\n                    win.focus();\r\n                    win.webContents.send(\"menu-triggered-export-project\");\r\n                  }\r\n                },\r\n              },\r\n              {\r\n                label: \"导入项目\",\r\n                accelerator: \"CmdOrCtrl+Shift+I\",\r\n                toolTip: \"导入项目状态，恢复之前的工作环境\",\r\n                click: async () => {\r\n                  const win = BrowserWindow.getFocusedWindow();\r\n                  if (win && !win.isDestroyed()) {\r\n                    win.focus();\r\n                    win.webContents.send(\"menu-triggered-import-project-state\");\r\n                  }\r\n                },\r\n              },\r\n            ],\r\n          },\r\n          { type: \"separator\" },\r\n          {\r\n            label: \"退出\",\r\n            role: \"quit\",\r\n            accelerator: \"CmdOrCtrl+Q\",\r\n          },\r\n        ],\r\n      },\r\n      {\r\n        label: \"编辑\",\r\n        submenu: [\r\n          {\r\n            label: \"撤销\",\r\n            role: \"undo\",\r\n            accelerator: \"CmdOrCtrl+Z\",\r\n          },\r\n          {\r\n            label: \"重做\",\r\n            role: \"redo\",\r\n            accelerator: \"CmdOrCtrl+Shift+Z\",\r\n          },\r\n          { type: \"separator\" },\r\n          {\r\n            label: \"剪切\",\r\n            role: \"cut\",\r\n            accelerator: \"CmdOrCtrl+X\",\r\n          },\r\n          {\r\n            label: \"复制\",\r\n            role: \"copy\",\r\n            accelerator: \"CmdOrCtrl+C\",\r\n          },\r\n          {\r\n            label: \"粘贴\",\r\n            role: \"paste\",\r\n            accelerator: \"CmdOrCtrl+V\",\r\n          },\r\n          {\r\n            label: \"删除\",\r\n            role: \"delete\",\r\n            accelerator: \"Delete\",\r\n          },\r\n          {\r\n            label: \"全选\",\r\n            role: \"selectAll\",\r\n            accelerator: \"CmdOrCtrl+A\",\r\n          },\r\n        ],\r\n      },\r\n      {\r\n        label: \"显示\",\r\n        submenu: [\r\n          {\r\n            label: \"放大\",\r\n            role: \"zoomIn\",\r\n            accelerator: \"CmdOrCtrl+Plus\",\r\n          },\r\n          {\r\n            label: \"默认大小\",\r\n            role: \"resetZoom\",\r\n            accelerator: \"CmdOrCtrl+0\",\r\n          },\r\n          {\r\n            label: \"缩小\",\r\n            role: \"zoomOut\",\r\n            accelerator: \"CmdOrCtrl+-\",\r\n          },\r\n          { type: \"separator\" },\r\n          {\r\n            label: fullscreenLabel,\r\n            role: \"togglefullscreen\",\r\n            accelerator: \"F11\",\r\n          },\r\n        ],\r\n      },\r\n      ...(this.isDev\r\n        ? [\r\n            {\r\n              label: \"开发\",\r\n              submenu: devMenuItems,\r\n            },\r\n          ]\r\n        : []),\r\n      {\r\n        label: \"关于\",\r\n        submenu: [\r\n          {\r\n            label: \"关于应用\",\r\n            role: \"about\" as const,\r\n            accelerator: \"F1\",\r\n          },\r\n        ],\r\n      },\r\n    ];\r\n\r\n    return template;\r\n  }\r\n\r\n  /**\r\n   * 创建上下文菜单 - 文件菜单\r\n   */\r\n  createFileMenu(win: BrowserWindow) {\r\n    const fileMenu = Menu.buildFromTemplate([\r\n      {\r\n        label: \"打开文件夹...\",\r\n        click: () => {\r\n          win?.webContents.send(\"menu-triggered-import-project\");\r\n        },\r\n      },\r\n      {\r\n        label: \"打开文件...\",\r\n        click: () => {\r\n          win?.webContents.send(\"menu-triggered-open-file\");\r\n        },\r\n      },\r\n      { type: \"separator\" },\r\n      {\r\n        label: \"退出\",\r\n        click: () => app.quit(),\r\n      },\r\n    ]);\r\n\r\n    fileMenu.popup({ window: win });\r\n  }\r\n\r\n  /**\r\n   * 创建上下文菜单 - 项目菜单\r\n   */\r\n  createProjectMenu(win: BrowserWindow) {\r\n    const projectMenu = Menu.buildFromTemplate([\r\n      {\r\n        label: \"导入项目\",\r\n        click: () => {\r\n          win?.webContents.send(\"menu-triggered-import-project-state\");\r\n        },\r\n      },\r\n      {\r\n        label: \"导出项目\",\r\n        click: () => {\r\n          win?.webContents.send(\"menu-triggered-export-project\");\r\n        },\r\n      },\r\n    ]);\r\n\r\n    projectMenu.popup({ window: win });\r\n  }\r\n\r\n  /**\r\n   * 创建上下文菜单 - 编辑菜单\r\n   */\r\n  createEditMenu(win: BrowserWindow) {\r\n    const editMenu = Menu.buildFromTemplate([\r\n      { label: \"撤销\", role: \"undo\" },\r\n      { label: \"重做\", role: \"redo\" },\r\n      { type: \"separator\" },\r\n      { label: \"剪切\", role: \"cut\" },\r\n      { label: \"复制\", role: \"copy\" },\r\n      { label: \"粘贴\", role: \"paste\" },\r\n      { type: \"separator\" },\r\n      { label: \"全选\", role: \"selectAll\" },\r\n    ]);\r\n\r\n    editMenu.popup({ window: win });\r\n  }\r\n\r\n  /**\r\n   * 创建上下文菜单 - 视图菜单\r\n   */\r\n  createViewMenu(win: BrowserWindow) {\r\n    const viewMenu = Menu.buildFromTemplate([\r\n      { label: \"放大\", role: \"zoomIn\" },\r\n      { label: \"默认大小\", role: \"resetZoom\" },\r\n      { label: \"缩小\", role: \"zoomOut\" },\r\n      { type: \"separator\" },\r\n      {\r\n        label: win?.isFullScreen() ? \"退出全屏\" : \"进入全屏\",\r\n        role: \"togglefullscreen\",\r\n      },\r\n    ]);\r\n\r\n    viewMenu.popup({ window: win });\r\n  }\r\n\r\n  /**\r\n   * 创建上下文菜单 - 开发菜单\r\n   */\r\n  createDevMenu(win: BrowserWindow) {\r\n    if (!this.isDev) return;\r\n\r\n    const devMenu = Menu.buildFromTemplate([\r\n      { label: \"开发者工具\", role: \"toggleDevTools\" },\r\n      { label: \"强制刷新\", role: \"forceReload\" },\r\n    ]);\r\n\r\n    devMenu.popup({ window: win });\r\n  }\r\n\r\n  /**\r\n   * 创建上下文菜单 - 关于菜单\r\n   */\r\n  createAboutMenu(win: BrowserWindow) {\r\n    const aboutMenu = Menu.buildFromTemplate([\r\n      {\r\n        label: \"关于应用\",\r\n        click: () => {\r\n          let version = \"未知版本\";\r\n          try {\r\n            const packagePath = path.join(app.getAppPath(), \"package.json\");\r\n            if (existsSync(packagePath)) {\r\n              const packageData = JSON.parse(readFileSync(packagePath, \"utf8\"));\r\n              version = packageData.version || \"未知版本\";\r\n            }\r\n          } catch (error) {\r\n            console.error(\"读取版本号失败:\", error);\r\n          }\r\n          dialog.showMessageBox(win!, {\r\n            title: \"关于应用\",\r\n            message: \"ML Desktop\",\r\n            detail: `版本 ${version}\\n一个机器学习数据处理和建模的桌面应用。`,\r\n            buttons: [\"确定\"],\r\n            type: \"info\",\r\n          });\r\n        },\r\n      },\r\n    ]);\r\n\r\n    aboutMenu.popup({ window: win });\r\n  }\r\n}\r\n", "import { ipc<PERSON><PERSON>, <PERSON><PERSON>erWindow, app } from \"electron\";\r\nimport { tmpdir } from \"node:os\";\r\nimport { join, dirname } from \"node:path\";\r\nimport type { WindowManager } from \"./WindowManager\";\r\nimport type { FileSystemHandler } from \"./FileSystemHandler\";\r\nimport type { DialogHandler } from \"./DialogHandler\";\r\nimport type { MenuManager } from \"./MenuManager\";\r\n\r\n// 导入 electron-click-drag-plugin\r\nimport dragAddon from \"electron-click-drag-plugin\";\r\n\r\n/**\r\n * IPC 通信处理器 - 负责处理所有 IPC 通信\r\n */\r\nexport class IPCHandler {\r\n  private windowManager: WindowManager;\r\n  private fileSystemHandler: FileSystemHandler;\r\n  private dialogHandler: DialogHandler;\r\n  private menuManager: MenuManager;\r\n\r\n  constructor(\r\n    windowManager: WindowManager,\r\n    fileSystemHandler: FileSystemHandler,\r\n    dialogHandler: DialogHandler,\r\n    menuManager: MenuManager,\r\n  ) {\r\n    this.windowManager = windowManager;\r\n    this.fileSystemHandler = fileSystemHandler;\r\n    this.dialogHandler = dialogHandler;\r\n    this.menuManager = menuManager;\r\n    this.setupIpcHandlers();\r\n  }\r\n\r\n  /**\r\n   * 设置所有 IPC 处理器\r\n   */\r\n  private setupIpcHandlers() {\r\n    this.setupModelHandlers();\r\n    this.setupWindowHandlers();\r\n    this.setupFileSystemHandlers();\r\n    this.setupDialogHandlers();\r\n    this.setupMenuHandlers();\r\n    this.setupUtilityHandlers();\r\n  }\r\n\r\n  /**\r\n   * 设置模型相关的 IPC 处理器\r\n   */\r\n  private setupModelHandlers() {\r\n    // 监听模型进度更新\r\n    ipcMain.on(\"model_progress_update\", (event, data) => {\r\n      const uid = data.uid;\r\n      const targetWindow = this.windowManager.getTaskWindow(uid);\r\n\r\n      if (targetWindow && !targetWindow.isDestroyed()) {\r\n        targetWindow.webContents.send(\"model_progress_update\", data);\r\n      }\r\n    });\r\n\r\n    // 监听模型完成事件\r\n    ipcMain.on(\"model_complete\", (event, data) => {\r\n      const uid = data.uid;\r\n      const targetWindow = this.windowManager.getTaskWindow(uid);\r\n\r\n      this.windowManager.cachePendingModelCompleteEvent(uid, data);\r\n\r\n      if (targetWindow && !targetWindow.isDestroyed()) {\r\n        targetWindow.webContents.send(\"model_complete\", data);\r\n      }\r\n    });\r\n\r\n    // 监听从渲染器发送的预取结果并暂存\r\n    ipcMain.on(\"cache_model_result\", (event, { uid, result }) => {\r\n      if (uid && result) {\r\n        this.windowManager.cachePreFetchedResult(uid, result);\r\n      }\r\n    });\r\n\r\n    // 监听模型结果窗口准备就绪事件\r\n    ipcMain.on(\"model_result_window_ready\", (event, data) => {\r\n      const uid = data.uid;\r\n      if (!uid) return;\r\n\r\n      const resultWindow = BrowserWindow.fromWebContents(event.sender);\r\n      if (resultWindow) {\r\n        this.windowManager.registerTaskWindow(uid, resultWindow);\r\n\r\n        const preFetchedResult = this.windowManager.getPreFetchedResult(uid);\r\n        if (preFetchedResult) {\r\n          resultWindow.webContents.send(\"model_complete\", {\r\n            uid,\r\n            result: preFetchedResult,\r\n          });\r\n          this.windowManager.deletePreFetchedResult(uid);\r\n          return;\r\n        }\r\n\r\n        const pendingData =\r\n          this.windowManager.getPendingModelCompleteEvent(uid);\r\n        if (pendingData) {\r\n          resultWindow.webContents.send(\"model_complete\", pendingData);\r\n          this.windowManager.deletePendingModelCompleteEvent(uid);\r\n        }\r\n      }\r\n    });\r\n\r\n    // 监听模型信息请求\r\n    ipcMain.on(\"request_model_info\", async (event, data) => {\r\n      const uid = data.uid;\r\n      if (!uid) {\r\n        event.sender.send(\"model_info_response\", {\r\n          code: 400,\r\n          msg: \"无效的任务ID\",\r\n        });\r\n        return;\r\n      }\r\n\r\n      const resultWindow = BrowserWindow.fromWebContents(event.sender);\r\n      if (resultWindow) {\r\n        this.windowManager.registerTaskWindow(uid, resultWindow);\r\n      }\r\n\r\n      const mainWindow = this.windowManager.getMainWindow();\r\n      if (!mainWindow || mainWindow.isDestroyed()) {\r\n        event.sender.send(\"model_info_response\", {\r\n          code: 500,\r\n          msg: \"主窗口不可用\",\r\n        });\r\n        return;\r\n      }\r\n\r\n      try {\r\n        const result = await mainWindow.webContents.executeJavaScript(`\r\n          (function() {\r\n            return new Promise((resolve) => {\r\n              const socket = window.socketInstance;\r\n              if (!socket || !socket.connected) {\r\n                resolve({ code: 500, msg: 'WebSocket未连接' });\r\n                return;\r\n              }\r\n              \r\n              socket.emit('get_model_info', { uid: '${uid}' }, (response) => {\r\n                resolve(response || { code: 404, msg: '未收到响应' });\r\n              });\r\n              \r\n              setTimeout(() => {\r\n                resolve({ code: 408, msg: '请求超时' });\r\n              }, 10000);\r\n            });\r\n          })()\r\n        `);\r\n\r\n        event.sender.send(\"model_info_response\", result);\r\n      } catch (error: any) {\r\n        event.sender.send(\"model_info_response\", {\r\n          code: 500,\r\n          msg: \"执行请求失败: \" + (error.message || \"未知错误\"),\r\n        });\r\n      }\r\n    });\r\n  }\r\n\r\n  /**\r\n   * 设置窗口相关的 IPC 处理器\r\n   */\r\n  private setupWindowHandlers() {\r\n    // 新窗口处理\r\n    ipcMain.handle(\"open-win\", (_, arg) => {\r\n      return this.windowManager.createChildWindow(arg);\r\n    });\r\n\r\n    // 主窗口准备显示\r\n    ipcMain.on(\"APP_READY_TO_SHOW_MAIN_WINDOW\", (event, args: any = {}) => {\r\n      this.windowManager.createMainWindow(args.targetRoute);\r\n\r\n      if (args.openedFilePath) {\r\n        const sendFileData = () => {\r\n          const win = this.windowManager.getMainWindow();\r\n          if (args.targetRoute?.includes(\"/dataManagement/imandex\")) {\r\n            win?.webContents.send(\"excel-file-selected\", args.openedFilePath);\r\n          } else {\r\n            win?.webContents.send(\r\n              \"workspace-file-selected\",\r\n              args.openedFilePath,\r\n            );\r\n          }\r\n\r\n          if (args.singleFileMode) {\r\n            win?.webContents.send(\"set-single-file-mode\", args.openedFilePath);\r\n          }\r\n        };\r\n\r\n        const win = this.windowManager.getMainWindow();\r\n        win?.webContents.once(\"did-finish-load\", sendFileData);\r\n        win?.webContents.once(\"dom-ready\", sendFileData);\r\n        setTimeout(sendFileData, 1000);\r\n      }\r\n    });\r\n\r\n    // 窗口控制\r\n    ipcMain.on(\"minimize-window\", () => {\r\n      this.windowManager.minimizeWindow();\r\n    });\r\n\r\n    ipcMain.on(\"maximize-window\", () => {\r\n      this.windowManager.toggleMaximizeWindow();\r\n    });\r\n\r\n    ipcMain.on(\"close-window\", () => {\r\n      this.windowManager.closeWindow();\r\n    });\r\n\r\n    // 当前窗口控制\r\n    ipcMain.on(\"minimize-current-window\", (event) => {\r\n      const currentWindow = BrowserWindow.fromWebContents(event.sender);\r\n      if (currentWindow) {\r\n        this.windowManager.minimizeWindow(currentWindow);\r\n      }\r\n    });\r\n\r\n    ipcMain.on(\"maximize-current-window\", (event) => {\r\n      const currentWindow = BrowserWindow.fromWebContents(event.sender);\r\n      if (currentWindow) {\r\n        this.windowManager.toggleMaximizeWindow(currentWindow);\r\n      }\r\n    });\r\n\r\n    ipcMain.on(\"close-current-window\", (event) => {\r\n      const currentWindow = BrowserWindow.fromWebContents(event.sender);\r\n      if (currentWindow) {\r\n        this.windowManager.closeWindow(currentWindow);\r\n      }\r\n    });\r\n\r\n    // 文件选择事件转发\r\n    ipcMain.on(\"workspace-file-selected\", (event, filePath: string) => {\r\n      const win = this.windowManager.getMainWindow();\r\n      if (win && !win.isDestroyed()) {\r\n        win.webContents.send(\"workspace-file-selected\", filePath);\r\n      }\r\n    });\r\n\r\n    ipcMain.on(\"excel-file-selected\", (event, filePath: string) => {\r\n      const win = this.windowManager.getMainWindow();\r\n      if (win && !win.isDestroyed()) {\r\n        win.webContents.send(\"excel-file-selected\", filePath);\r\n      }\r\n    });\r\n\r\n    // 窗口还原（从最大化状态）\r\n    ipcMain.on(\"unmaximize-window\", () => {\r\n      const mainWindow = this.windowManager.getMainWindow();\r\n      if (mainWindow && !mainWindow.isDestroyed() && mainWindow.isMaximized()) {\r\n        mainWindow.unmaximize();\r\n      }\r\n    });\r\n\r\n    // 设置窗口位置\r\n    ipcMain.on(\r\n      \"set-window-position\",\r\n      (event, position: { x: number; y: number }) => {\r\n        const mainWindow = this.windowManager.getMainWindow();\r\n        if (mainWindow && !mainWindow.isDestroyed()) {\r\n          mainWindow.setPosition(position.x, position.y);\r\n        }\r\n      },\r\n    );\r\n\r\n    // 获取窗口是否最大化状态\r\n    ipcMain.handle(\"is-window-maximized\", () => {\r\n      const mainWindow = this.windowManager.getMainWindow();\r\n      return mainWindow && !mainWindow.isDestroyed()\r\n        ? mainWindow.isMaximized()\r\n        : false;\r\n    });\r\n\r\n    // 开始拖拽窗口\r\n    ipcMain.on(\"start-drag\", (event) => {\r\n      try {\r\n        const window = BrowserWindow.fromWebContents(event.sender);\r\n        if (window && !window.isDestroyed()) {\r\n          const hwndBuffer = window.getNativeWindowHandle();\r\n          // Linux: extract X11 Window ID from the buffer (first 4 bytes, little-endian)\r\n          // macOS/Windows: pass Buffer directly\r\n          const windowId =\r\n            process.platform === \"linux\"\r\n              ? hwndBuffer.readUInt32LE(0)\r\n              : hwndBuffer;\r\n\r\n          dragAddon.startDrag(windowId);\r\n        }\r\n      } catch (error) {\r\n        console.error(\"Failed to start drag:\", error);\r\n      }\r\n    });\r\n\r\n    // 应用退出\r\n    ipcMain.on(\"app-quit\", () => {\r\n      app.quit();\r\n    });\r\n  }\r\n\r\n  /**\r\n   * 设置文件系统相关的 IPC 处理器\r\n   */\r\n  private setupFileSystemHandlers() {\r\n    // 读取目录内容\r\n    ipcMain.handle(\"fs:readDirectory\", async (_, dirPath: string) => {\r\n      return await this.fileSystemHandler.readDirectory(dirPath);\r\n    });\r\n\r\n    // 创建新目录\r\n    ipcMain.handle(\"fs:createDirectory\", async (_, targetPath: string) => {\r\n      return await this.fileSystemHandler.createDirectory(targetPath);\r\n    });\r\n\r\n    // 创建新文件\r\n    ipcMain.handle(\"fs:createFile\", async (_, filePath: string) => {\r\n      return await this.fileSystemHandler.createFile(filePath);\r\n    });\r\n\r\n    // 删除文件/目录\r\n    ipcMain.handle(\"fs:deletePath\", async (_, targetPath: string) => {\r\n      return await this.fileSystemHandler.deletePath(targetPath);\r\n    });\r\n\r\n    // 重命名文件/目录\r\n    ipcMain.handle(\"fs:rename\", async (_, oldPath: string, newPath: string) => {\r\n      return await this.fileSystemHandler.rename(oldPath, newPath);\r\n    });\r\n\r\n    // 检查文件/目录是否存在\r\n    ipcMain.handle(\"fs:access\", async (_, filePath: string) => {\r\n      return await this.fileSystemHandler.access(filePath);\r\n    });\r\n\r\n    // 读取文件内容\r\n    ipcMain.handle(\"fs:readFile\", async (_, filePath: string) => {\r\n      return await this.fileSystemHandler.readFile(filePath);\r\n    });\r\n\r\n    // 检测文件类型和读取内容\r\n    ipcMain.handle(\"fs:readFileWithType\", async (_, filePath: string) => {\r\n      return await this.fileSystemHandler.readFileWithType(filePath);\r\n    });\r\n\r\n    // 读取文件内容为Buffer\r\n    ipcMain.handle(\"fs:readFileBuffer\", async (_, filePath: string) => {\r\n      return await this.fileSystemHandler.readFileBuffer(filePath);\r\n    });\r\n    // 写入文件内容\r\n    ipcMain.handle(\r\n      \"fs:writeFile\",\r\n      async (_, filePath: string, content: string) => {\r\n        return await this.fileSystemHandler.writeFile(filePath, content);\r\n      },\r\n    );\r\n\r\n    // 写入文件内容 (Buffer)\r\n    ipcMain.handle(\r\n      \"fs:writeFileBuffer\",\r\n      async (_, filePath: string, arrayBuffer: ArrayBuffer) => {\r\n        return await this.fileSystemHandler.writeFileBuffer(\r\n          filePath,\r\n          arrayBuffer,\r\n        );\r\n      },\r\n    );\r\n\r\n    // 写入文件内容 (Base64Buffer)\r\n    ipcMain.handle(\r\n      \"fs:writeFileBase64Buffer\",\r\n      async (_, filePath: string, arrayBuffer: string) => {\r\n        return await this.fileSystemHandler.writeFileBase64Buffer(\r\n          filePath,\r\n          arrayBuffer,\r\n        );\r\n      },\r\n    );\r\n\r\n    // 验证文件路径是否存在\r\n    ipcMain.handle(\"fs:validatePath\", async (_, filePath: string) => {\r\n      return await this.fileSystemHandler.validatePath(filePath);\r\n    });\r\n\r\n    // 最近文件管理\r\n    ipcMain.handle(\"recentFiles:get\", async () => {\r\n      return await this.fileSystemHandler.getRecentFiles();\r\n    });\r\n\r\n    ipcMain.handle(\"recentFiles:save\", async (_, files: any[]) => {\r\n      return await this.fileSystemHandler.saveRecentFiles(files);\r\n    });\r\n\r\n    // 获取目录大小\r\n    ipcMain.handle(\"fs:getDirectorySize\", async (_, dirPath: string) => {\r\n      return await this.fileSystemHandler.getDirectorySize(dirPath);\r\n    });\r\n\r\n    // 递归读取目录\r\n    ipcMain.handle(\"fs:readDirectoryRecursive\", async (_, dirPath: string) => {\r\n      return await this.fileSystemHandler.readDirectoryRecursive(dirPath);\r\n    });\r\n\r\n    // 检查路径是否存在\r\n    ipcMain.handle(\"fs:pathExists\", async (_, filePath: string) => {\r\n      return await this.fileSystemHandler.pathExists(filePath);\r\n    });\r\n  }\r\n\r\n  /**\r\n   * 设置对话框相关的 IPC 处理器\r\n   */\r\n  private setupDialogHandlers() {\r\n    // 选择目录对话框\r\n    ipcMain.handle(\"dialog:openDirectory\", async () => {\r\n      return await this.dialogHandler.openDirectory();\r\n    });\r\n\r\n    // 选择文件对话框\r\n    ipcMain.handle(\"dialog:openFile\", async () => {\r\n      return await this.dialogHandler.openFile();\r\n    });\r\n\r\n    // 选择保存文件对话框\r\n    ipcMain.handle(\"dialog:saveFile\", async (_, options: any) => {\r\n      return await this.dialogHandler.saveFile(options);\r\n    });\r\n\r\n    // 项目管理相关\r\n    ipcMain.handle(\"project:export\", async (_, projectData: any) => {\r\n      return await this.dialogHandler.exportProject(projectData);\r\n    });\r\n\r\n    ipcMain.handle(\"project:import\", async () => {\r\n      return await this.dialogHandler.importProject();\r\n    });\r\n\r\n    // 选择新的文件路径\r\n    ipcMain.handle(\"dialog:selectNewPath\", async (_, options: any) => {\r\n      return await this.dialogHandler.selectNewPath(options);\r\n    });\r\n\r\n    // 显示对话框 (带窗口上下文)\r\n    ipcMain.handle(\"dialog:showOpenDialog\", async (event, options: any) => {\r\n      const window = BrowserWindow.fromWebContents(event.sender);\r\n      if (window) {\r\n        return await this.dialogHandler.showOpenDialog(window, options);\r\n      }\r\n      return { canceled: true, filePaths: [] };\r\n    });\r\n\r\n    ipcMain.handle(\"dialog:showSaveDialog\", async (event, options: any) => {\r\n      const window = BrowserWindow.fromWebContents(event.sender);\r\n      if (window) {\r\n        return await this.dialogHandler.showSaveDialog(window, options);\r\n      }\r\n      return { canceled: true, filePath: \"\" };\r\n    });\r\n  }\r\n\r\n  /**\r\n   * 设置菜单相关的 IPC 处理器\r\n   */\r\n  private setupMenuHandlers() {\r\n    // 显示各种菜单\r\n    ipcMain.on(\"show-file-menu\", (event) => {\r\n      const win = BrowserWindow.fromWebContents(event.sender);\r\n      if (win && !win.isDestroyed()) {\r\n        this.menuManager.createFileMenu(win);\r\n      }\r\n    });\r\n\r\n    ipcMain.on(\"show-project-menu\", (event) => {\r\n      const win = BrowserWindow.fromWebContents(event.sender);\r\n      if (win && !win.isDestroyed()) {\r\n        this.menuManager.createProjectMenu(win);\r\n      }\r\n    });\r\n\r\n    ipcMain.on(\"show-edit-menu\", (event) => {\r\n      const win = BrowserWindow.fromWebContents(event.sender);\r\n      if (win && !win.isDestroyed()) {\r\n        this.menuManager.createEditMenu(win);\r\n      }\r\n    });\r\n\r\n    ipcMain.on(\"show-view-menu\", (event) => {\r\n      const win = BrowserWindow.fromWebContents(event.sender);\r\n      if (win && !win.isDestroyed()) {\r\n        this.menuManager.createViewMenu(win);\r\n      }\r\n    });\r\n\r\n    ipcMain.on(\"show-dev-menu\", (event) => {\r\n      const win = BrowserWindow.fromWebContents(event.sender);\r\n      if (win && !win.isDestroyed()) {\r\n        this.menuManager.createDevMenu(win);\r\n      }\r\n    });\r\n\r\n    ipcMain.on(\"show-about-menu\", (event) => {\r\n      const win = BrowserWindow.fromWebContents(event.sender);\r\n      if (win && !win.isDestroyed()) {\r\n        this.menuManager.createAboutMenu(win);\r\n      }\r\n    });\r\n  }\r\n\r\n  /**\r\n   * 设置实用工具相关的 IPC 处理器\r\n   */\r\n  private setupUtilityHandlers() {\r\n    // 获取系统临时目录\r\n    ipcMain.handle(\"os:tmpdir\", async () => {\r\n      return tmpdir();\r\n    });\r\n\r\n    // 路径拼接\r\n    ipcMain.handle(\"path:join\", async (_, ...paths: string[]) => {\r\n      return join(...paths);\r\n    });\r\n\r\n    // 获取路径的目录部分\r\n    ipcMain.handle(\"path:dirname\", async (_, filePath: string) => {\r\n      return dirname(filePath);\r\n    });\r\n  }\r\n}\r\n", "import { release } from \"node:os\";\r\nimport { fileURLToPath } from \"node:url\";\r\nimport { join, dirname } from \"node:path\";\r\nimport { app, dialog } from \"electron\";\r\nimport { readFileSync } from \"node:fs\";\r\n\r\n// Import our new modules\r\nimport { ProcessManager } from \"./modules/ProcessManager\";\r\nimport { WindowManager } from \"./modules/WindowManager\";\r\nimport { FileSystemHandler } from \"./modules/FileSystemHandler\";\r\nimport { DialogHandler } from \"./modules/DialogHandler\";\r\nimport { MenuManager } from \"./modules/MenuManager\";\r\nimport { IPCHandler } from \"./modules/IPCHandler\";\r\n\r\n// The built directory structure\r\nconst __filename = fileURLToPath(import.meta.url);\r\nconst __dirname = dirname(__filename);\r\nprocess.env.DIST_ELECTRON = join(__dirname, \"..\");\r\nprocess.env.DIST = join(process.env.DIST_ELECTRON, \"../dist\");\r\nprocess.env.PUBLIC = process.env.VITE_DEV_SERVER_URL\r\n  ? join(process.env.DIST_ELECTRON, \"../public\")\r\n  : process.env.DIST;\r\n\r\n// 是否为开发环境\r\nconst isDev = process.env[\"NODE_ENV\"] === \"development\";\r\n\r\n// Disable GPU Acceleration for Windows 7\r\nif (release().startsWith(\"6.1\")) app.disableHardwareAcceleration();\r\n\r\n// Set application name for Windows 10+ notifications\r\nif (process.platform === \"win32\") app.setAppUserModelId(app.getName());\r\n\r\nif (!app.requestSingleInstanceLock()) {\r\n  app.quit();\r\n  process.exit(0);\r\n}\r\n\r\n// Initialize all managers\r\nconst processManager = new ProcessManager();\r\nconst windowManager = new WindowManager();\r\nconst fileSystemHandler = new FileSystemHandler();\r\nconst dialogHandler = new DialogHandler();\r\nconst menuManager = new MenuManager(isDev);\r\nconst ipcHandler = new IPCHandler(\r\n  windowManager,\r\n  fileSystemHandler,\r\n  dialogHandler,\r\n  menuManager,\r\n);\r\n\r\nlet isShuttingDown = false;\r\n\r\n/**\r\n * 异步启动所有服务并创建主窗口的函数\r\n */\r\nasync function startServicesAndMainWindow() {\r\n  console.log(\"Services starting...\");\r\n  const BackendEnabled = import.meta.env.VITE_START_BACKEND;\r\n\r\n  if (BackendEnabled === \"true\") {\r\n    // 在启动前检查并清理可能的残留进程\r\n    console.log(\"Checking for residual processes from previous runs...\");\r\n    const loadingWindow = windowManager.getLoadingWindow();\r\n    loadingWindow?.webContents.send(\"update-progress\", 10, \"正在清理环境...\");\r\n\r\n    try {\r\n      // 清理残留进程\r\n      await processManager.terminateAllBackendProcesses();\r\n      await processManager.terminateAllRabbitMQProcesses();\r\n      console.log(\"Residual process cleanup completed\");\r\n    } catch (error) {\r\n      console.error(\"Error during residual process cleanup:\", error);\r\n    }\r\n\r\n    // 首先启动RabbitMQ服务\r\n    console.log(\"Starting RabbitMQ...\");\r\n    loadingWindow?.webContents.send(\r\n      \"update-progress\",\r\n      25,\r\n      \"正在启动消息队列服务...\",\r\n    );\r\n    const rabbitmqStarted = await processManager.startRabbitMQ();\r\n\r\n    if (!rabbitmqStarted) {\r\n      console.error(\"Failed to start RabbitMQ, aborting startup\");\r\n      dialog.showErrorBox(\r\n        \"启动失败\",\r\n        \"RabbitMQ服务启动失败，请检查配置并重试。\",\r\n      );\r\n      app.quit();\r\n      return;\r\n    }\r\n\r\n    // 等待一段时间让RabbitMQ完全初始化\r\n    console.log(\"Waiting for RabbitMQ to fully initialize...\");\r\n    loadingWindow?.webContents.send(\r\n      \"update-progress\",\r\n      50,\r\n      \"正在初始化消息队列...\",\r\n    );\r\n    await new Promise((resolve) => setTimeout(resolve, 5000));\r\n\r\n    // 然后启动后端服务\r\n    console.log(\"Starting backend service...\");\r\n    loadingWindow?.webContents.send(\r\n      \"update-progress\",\r\n      75,\r\n      \"正在启动后端服务...\",\r\n    );\r\n    const backendStarted = await processManager.startBackendService();\r\n\r\n    if (!backendStarted) {\r\n      console.error(\"Failed to start backend service, aborting startup\");\r\n      dialog.showErrorBox(\"启动失败\", \"后端服务启动失败，请检查配置并重试。\");\r\n      await processManager.terminateAllRabbitMQProcesses();\r\n      app.quit();\r\n      return;\r\n    }\r\n\r\n    // 等待后端服务完全初始化\r\n    console.log(\"Waiting for backend service to fully initialize...\");\r\n    loadingWindow?.webContents.send(\r\n      \"update-progress\",\r\n      90,\r\n      \"正在初始化后端服务...\",\r\n    );\r\n    await new Promise((resolve) => setTimeout(resolve, 3000));\r\n  }\r\n\r\n  // 创建主窗口\r\n  console.log(\"All services started successfully, creating UI...\");\r\n  const loadingWindow = windowManager.getLoadingWindow();\r\n  loadingWindow?.webContents.send(\r\n    \"update-progress\",\r\n    100,\r\n    \"服务启动完成，请稍后...\",\r\n  );\r\n  await windowManager.createMainWindow(\"/welcome\");\r\n\r\n  const mainWindow = windowManager.getMainWindow();\r\n  if (mainWindow) {\r\n    mainWindow.once(\"ready-to-show\", () => {\r\n      setTimeout(() => {\r\n        windowManager.closeLoadingWindow();\r\n        windowManager.showMainWindow();\r\n      }, 300);\r\n    });\r\n  }\r\n}\r\n\r\n/**\r\n * 创建闪屏窗口并启动服务\r\n */\r\nfunction initializeApp() {\r\n  const loadingWindow = windowManager.createLoadingWindow();\r\n\r\n  // 当加载页面渲染完成后，才开始启动后端服务\r\n  loadingWindow.webContents.on(\"did-finish-load\", () => {\r\n    // 读取 package.json 的版本号并发送到加载窗口\r\n    try {\r\n      const packageJsonPath = join(app.getAppPath(), \"package.json\");\r\n      const pkg = JSON.parse(readFileSync(packageJsonPath, \"utf-8\"));\r\n      loadingWindow?.webContents.send(\"set-version\", pkg.version);\r\n    } catch (error) {\r\n      console.error(\"Failed to read package.json version:\", error);\r\n    }\r\n\r\n    loadingWindow?.show();\r\n    startServicesAndMainWindow();\r\n  });\r\n\r\n  // 创建应用菜单\r\n  menuManager.createMenu();\r\n}\r\n\r\n// 应用事件处理\r\napp.whenReady().then(() => {\r\n  initializeApp();\r\n});\r\n\r\napp.on(\"window-all-closed\", () => {\r\n  if (process.platform !== \"darwin\") {\r\n    app.quit();\r\n  }\r\n});\r\n\r\n// 在应用退出前关闭后端服务\r\napp.on(\"before-quit\", async (event) => {\r\n  const BackendEnabled = import.meta.env.VITE_START_BACKEND;\r\n  if (!isShuttingDown && BackendEnabled === \"true\") {\r\n    event.preventDefault();\r\n    isShuttingDown = true;\r\n    processManager.setShuttingDown(true);\r\n\r\n    console.log(\"Application is quitting, terminating all services...\");\r\n\r\n    const terminateWithTimeout = async (\r\n      terminateFunc: () => Promise<void>,\r\n      name: string,\r\n      timeout: number,\r\n    ) => {\r\n      return Promise.race([\r\n        terminateFunc(),\r\n        new Promise<void>((resolve) => {\r\n          setTimeout(() => {\r\n            console.warn(`${name} termination timeout after ${timeout}ms`);\r\n            resolve();\r\n          }, timeout);\r\n        }),\r\n      ]);\r\n    };\r\n\r\n    try {\r\n      await Promise.all([\r\n        terminateWithTimeout(\r\n          async () => await processManager.terminateAllRabbitMQProcesses(),\r\n          \"RabbitMQ\",\r\n          5000,\r\n        ),\r\n        terminateWithTimeout(\r\n          async () => await processManager.terminateAllBackendProcesses(),\r\n          \"Backend\",\r\n          5000,\r\n        ),\r\n      ]);\r\n      console.log(\"All processes terminated successfully\");\r\n    } catch (error) {\r\n      console.error(\"Error during service termination:\", error);\r\n    }\r\n\r\n    // 确保所有服务都已终止后再退出\r\n    setTimeout(() => {\r\n      console.log(\"Clean exit completed, quitting application\");\r\n      app.exit(0);\r\n    }, 1000);\r\n  }\r\n});\r\n\r\n// 确保进程在退出时清理\r\nprocess.on(\"exit\", () => {\r\n  const BackendEnabled = import.meta.env.VITE_START_BACKEND;\r\n  if (BackendEnabled === \"true\") {\r\n    console.log(\"Node process exiting, attempting final cleanup\");\r\n    // 同步清理操作会在这里执行\r\n  }\r\n});\r\n\r\n// 处理未捕获的异常\r\nprocess.on(\"uncaughtException\", async (error) => {\r\n  console.error(\"Uncaught exception:\", error);\r\n  isShuttingDown = true;\r\n  processManager.setShuttingDown(true);\r\n\r\n  try {\r\n    await Promise.race([\r\n      Promise.all([\r\n        processManager.terminateAllBackendProcesses(),\r\n        processManager.terminateAllRabbitMQProcesses(),\r\n      ]),\r\n      new Promise((resolve) => setTimeout(resolve, 3000)),\r\n    ]);\r\n    console.log(\"Emergency cleanup completed\");\r\n  } catch (e) {\r\n    console.error(\"Emergency cleanup error:\", e);\r\n  }\r\n\r\n  app.exit(1);\r\n});\r\n\r\n// 处理未处理的拒绝Promise\r\nprocess.on(\"unhandledRejection\", (reason) => {\r\n  console.error(\"Unhandled Promise rejection:\", reason);\r\n});\r\n\r\napp.on(\"second-instance\", () => {\r\n  const mainWindow = windowManager.getMainWindow();\r\n  if (mainWindow && !mainWindow.isDestroyed()) {\r\n    if (mainWindow.isMinimized()) mainWindow.restore();\r\n    mainWindow.focus();\r\n  }\r\n});\r\n\r\napp.on(\"activate\", () => {\r\n  const mainWindow = windowManager.getMainWindow();\r\n  if (!mainWindow || mainWindow.isDestroyed()) {\r\n    windowManager.createMainWindow(\"/welcome\");\r\n  } else {\r\n    if (mainWindow.isMinimized()) mainWindow.restore();\r\n    mainWindow.focus();\r\n  }\r\n});\r\n\r\n// 导出管理器实例以供其他模块使用\r\nexport {\r\n  processManager,\r\n  windowManager,\r\n  fileSystemHandler,\r\n  dialogHandler,\r\n  menuManager,\r\n  ipcHandler,\r\n};\r\n"], "names": ["exec", "app", "join", "existsSync", "spawn", "resolve", "dirname", "BrowserWindow", "dialog", "isDev", "<PERSON><PERSON>", "readFileSync", "windowManager", "fileSystemHandler", "dialogH<PERSON>ler", "menuManager", "ipcMain", "win", "tmpdir", "__filename", "fileURLToPath", "__dirname", "release"], "mappings": ";;;;;;;;;;;;;;;AAGA,eAAsB,8BACpB,cAAsB,iBACH;AACnB,SAAO,IAAI,QAAQ,CAAC,YAAY;AAE9B,QAAI,QAAQ,aAAa,SAAS;AAChC,cAAQ,CAAA,CAAE;AACV;AAAA,IACF;AAIA,UAAM,MAAM,6BAA6B,WAAW;AAEpDA,uBAAAA,KAAK,KAAK,CAAC,OAAO,WAAW;AAC3B,UAAI,OAAO;AACT,gBAAQ;AAAA,UACN;AAAA,QAAA;AAEF,gBAAQ,CAAA,CAAE;AACV;AAAA,MACF;AAGA,YAAM,aAAa;AACnB,YAAM,OAAiB,CAAA;AACvB,UAAI;AAEJ,cAAQ,QAAQ,WAAW,KAAK,MAAM,OAAO,MAAM;AACjD,cAAM,MAAM,SAAS,MAAM,CAAC,GAAG,EAAE;AACjC,YAAI,CAAC,MAAM,GAAG,KAAK,QAAQ,QAAQ,KAAK;AACtC,eAAK,KAAK,GAAG;AAAA,QACf;AAAA,MACF;AAEA,cAAQ,IAAI,SAAS,KAAK,MAAM,gCAAgC,IAAI;AACpE,cAAQ,IAAI;AAAA,IACd,CAAC;AAAA,EACH,CAAC;AACH;AAIA,eAAsB,iCAAoD;AACxE,SAAO,IAAI,QAAQ,CAAC,YAAY;AAC9B,QAAI,QAAQ,aAAa,SAAS;AAChC,cAAQ,CAAA,CAAE;AACV;AAAA,IACF;AAEA,UAAM,MACJ;AAEFA,uBAAAA,KAAK,KAAK,CAAC,OAAO,WAAW;AAC3B,UAAI,OAAO;AACT,gBAAQ;AAAA,UACN;AAAA,QAAA;AAEF,gBAAQ,CAAA,CAAE;AACV;AAAA,MACF;AAGA,YAAM,eAAe,OAAO,KAAA,EAAO,MAAM,IAAI,EAAE,MAAM,CAAC;AACtD,YAAM,OAAiB,CAAA;AAEvB,iBAAW,QAAQ,cAAc;AAC/B,cAAM,QAAQ,KAAK,KAAA,EAAO,MAAM,cAAc;AAC9C,YAAI,OAAO;AACT,gBAAM,MAAM,SAAS,MAAM,CAAC,GAAG,EAAE;AACjC,gBAAM,OAAO,MAAM,CAAC,EAAE,KAAA;AACtB,cAAI,CAAC,MAAM,GAAG,KAAK,QAAQ,QAAQ,KAAK;AACtC,oBAAQ;AAAA,cACN,mCAAmC,IAAI,UAAU,GAAG;AAAA,YAAA;AAEtD,iBAAK,KAAK,GAAG;AAAA,UACf;AAAA,QACF;AAAA,MACF;AAEA,cAAQ,IAAI,SAAS,KAAK,MAAM,8BAA8B;AAC9D,cAAQ,IAAI;AAAA,IACd,CAAC;AAAA,EACH,CAAC;AACH;AC3EO,MAAM,eAAe;AAAA,EAArB;AACG,0CAAsC;AACtC,sCAA4B;AAC5B,mDAAoC,CAAA;AACpC,2CAAuC;AACvC,0CAAiB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKzB,MAAM,YAAY,KAA+B;AAC/C,WAAO,IAAI,QAAQ,CAAC,YAAY;AAC9B,UAAI,CAAC,OAAO,OAAO,GAAG;AACpB,gBAAQ,KAAK;AACb;AAAA,MACF;AAEA,cAAQ,IAAI,wCAAwC,GAAG,EAAE;AAEzD,UAAI;AACF,YAAI,QAAQ,aAAa,SAAS;AAChCA,6BAAAA,KAAK,iBAAiB,GAAG,UAAU,CAAC,QAAQ;AAC1C,gBAAI,KAAK;AACP,sBAAQ,MAAM,0BAA0B,GAAG,KAAK,GAAG;AACnD,sBAAQ,KAAK;AAAA,YACf,OAAO;AACL,sBAAQ,IAAI,mCAAmC,GAAG,EAAE;AACpD,oBAAM,QAAQ,KAAK,wBAAwB,QAAQ,GAAG;AACtD,kBAAI,UAAU,IAAI;AAChB,qBAAK,wBAAwB,OAAO,OAAO,CAAC;AAAA,cAC9C;AACA,sBAAQ,IAAI;AAAA,YACd;AAAA,UACF,CAAC;AAAA,QACH,OAAO;AACL,cAAI;AACF,oBAAQ,KAAK,KAAK,SAAS;AAC3B,uBAAW,MAAM;AACf,kBAAI;AACF,wBAAQ,KAAK,KAAK,CAAC;AACnB,wBAAQ,KAAK,KAAK,SAAS;AAC3B,wBAAQ,IAAI,0BAA0B,GAAG,EAAE;AAAA,cAC7C,QAAQ;AAAA,cAER;AAEA,oBAAM,QAAQ,KAAK,wBAAwB,QAAQ,GAAG;AACtD,kBAAI,UAAU,IAAI;AAChB,qBAAK,wBAAwB,OAAO,OAAO,CAAC;AAAA,cAC9C;AAEA,sBAAQ,IAAI;AAAA,YACd,GAAG,GAAI;AAAA,UACT,SAAS,GAAG;AACV,oBAAQ,MAAM,0BAA0B,GAAG,KAAK,CAAC;AACjD,oBAAQ,KAAK;AAAA,UACf;AAAA,QACF;AAAA,MACF,SAAS,OAAO;AACd,gBAAQ,MAAM,gCAAgC,GAAG,KAAK,KAAK;AAC3D,gBAAQ,KAAK;AAAA,MACf;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,sBAAwC;AAC5C,WAAO,IAAI,QAAQ,OAAO,YAAY;AACpC,UAAI;AAEF,cAAM,eAAe,MAAM,8BAAA;AAG3B,mBAAW,OAAO,cAAc;AAC9B,gBAAM,KAAK,YAAY,GAAG;AAAA,QAC5B;AAGA,YAAI,KAAK,mBAAmB,QAAQ,KAAK,eAAe,MAAM;AAC5D,kBAAQ;AAAA,YACN,6CAA6C,KAAK,UAAU;AAAA,UAAA;AAE9D,kBAAQ,IAAI;AACZ;AAAA,QACF;AAEA,cAAM,wBAAwBC,SAAAA,IAAI,aAC9BC,UAAAA,KAAK,QAAQ,eAAe,WAAW,eAAe,IACtDA,UAAAA,KAAKD,SAAAA,IAAI,WAAA,GAAc,2BAA2B,eAAe;AAErE,gBAAQ,IAAI,kBAAkB,QAAQ,aAAa;AACnD,gBAAQ,IAAI,kCAAkC,qBAAqB,EAAE;AAErE,YAAI,CAACE,QAAAA,WAAW,qBAAqB,GAAG;AACtC,kBAAQ;AAAA,YACN,oCAAoC,qBAAqB;AAAA,UAAA;AAE3D,kBAAQ,KAAK;AACb;AAAA,QACF;AAEA,gBAAQ,IAAI,wDAAwD;AAEpE,aAAK,iBAAiBC,yBAAM,uBAAuB,CAAA,GAAI;AAAA,UACrD,aAAa;AAAA,UACb,OAAO;AAAA,UACP,KAAKH,SAAAA,IAAI,aACLC,UAAAA,KAAK,QAAQ,eAAe,SAAS,IACrC;AAAA,UACJ,UAAU;AAAA,QAAA,CACX;AAED,YAAI,CAAC,KAAK,kBAAkB,CAAC,KAAK,eAAe,KAAK;AACpD,kBAAQ,MAAM,kDAAkD;AAChE,kBAAQ,KAAK;AACb;AAAA,QACF;AAEA,aAAK,aAAa,KAAK,eAAe;AACtC,aAAK,wBAAwB,KAAK,KAAK,UAAU;AAEjD,gBAAQ,IAAI,qCAAqC,KAAK,UAAU;AAGhE,YAAI;AACJ,YAAI,YAAY;AAChB,YAAI,cAAc;AAClB,YAAI,eAAe;AAEnB,cAAM,eAAe,CAAC,SAAiB;AACrC,gBAAM,SAAS,KAAK,SAAA;AACpB,kBAAQ,IAAI,mBAAmB,MAAM;AAErC,cACE,OAAO,SAAS,mBAAmB,KACnC,OAAO,SAAS,SAAS,GACzB;AACA,0BAAc;AACd,oBAAQ,IAAI,oBAAoB;AAAA,UAClC;AAEA,cACE,OAAO,SAAS,oBAAoB,KACpC,OAAO,SAAS,4BAA4B,KAC5C,OAAO,SAAS,sBAAsB,GACtC;AACA,2BAAe;AACf,oBAAQ,IAAI,2BAA2B;AAAA,UACzC;AAEA,cAAI,eAAe,gBAAgB,CAAC,WAAW;AAC7C,wBAAY;AACZ,yBAAa,cAAc;AAC3B,oBAAQ;AAAA,cACN;AAAA,YAAA;AAEF,oBAAQ,IAAI;AAAA,UACd;AAEA,cACG,OAAO,SAAS,oBAAoB,KACnC,OAAO,SAAS,sBAAsB,KACxC,OAAO,SAAS,gBAAgB,KAChC,OAAO,SAAS,eAAe,KAC/B,OAAO,SAAS,OAAO,GACvB;AACA,gBAAI,CAAC,WAAW;AACd,0BAAY;AACZ,2BAAa,cAAc;AAC3B,sBAAQ,IAAI,sCAAsC;AAClD,sBAAQ,IAAI;AAAA,YACd;AAAA,UACF;AAAA,QACF;AAEA,cAAM,oBAAoB,CAAC,SAAiB;AAC1C,gBAAM,SAAS,KAAK,SAAA;AACpB,kBAAQ,IAAI,kBAAkB,MAAM;AAEpC,cACE,OAAO,SAAS,mBAAmB,KACnC,OAAO,SAAS,SAAS,GACzB;AACA,0BAAc;AACd,oBAAQ,IAAI,kCAAkC;AAAA,UAChD;AAEA,cACE,OAAO,SAAS,oBAAoB,KACpC,OAAO,SAAS,4BAA4B,KAC5C,OAAO,SAAS,sBAAsB,GACtC;AACA,2BAAe;AACf,oBAAQ,IAAI,yCAAyC;AAAA,UACvD;AAEA,cAAI,eAAe,gBAAgB,CAAC,WAAW;AAC7C,wBAAY;AACZ,yBAAa,cAAc;AAC3B,oBAAQ;AAAA,cACN;AAAA,YAAA;AAEF,oBAAQ,IAAI;AAAA,UACd;AAEA,cACE,OAAO,SAAS,wBAAwB,KACxC,OAAO,SAAS,mBAAmB,KACnC,OAAO,SAAS,aAAa,KAC7B,OAAO,SAAS,iBAAiB,KAChC,OAAO,SAAS,QAAQ,KACvB,CAAC,OAAO,SAAS,MAAM,KACvB,CAAC,OAAO,SAAS,SAAS,GAC5B;AACA,gBAAI,CAAC,WAAW;AACd,sBAAQ,MAAM,mCAAmC,MAAM;AACvD,2BAAa,cAAc;AAC3B,sBAAQ,KAAK;AAAA,YACf;AAAA,UACF;AAAA,QACF;AAEA,YAAI,KAAK,eAAe,QAAQ;AAC9B,eAAK,eAAe,OAAO,GAAG,QAAQ,YAAY;AAAA,QACpD;AAEA,YAAI,KAAK,eAAe,QAAQ;AAC9B,eAAK,eAAe,OAAO,GAAG,QAAQ,iBAAiB;AAAA,QACzD;AAEA,aAAK,eAAe,GAAG,SAAS,CAAC,QAAQ;AACvC,kBAAQ,MAAM,oCAAoC,GAAG;AAErD,cAAI,KAAK,eAAe,MAAM;AAC5B,kBAAM,QAAQ,KAAK,wBAAwB,QAAQ,KAAK,UAAU;AAClE,gBAAI,UAAU,IAAI;AAChB,mBAAK,wBAAwB,OAAO,OAAO,CAAC;AAAA,YAC9C;AAAA,UACF;AAEA,eAAK,iBAAiB;AACtB,eAAK,aAAa;AAElB,cAAI,CAAC,WAAW;AACd,yBAAa,cAAc;AAC3B,oBAAQ,KAAK;AAAA,UACf;AAAA,QACF,CAAC;AAED,aAAK,eAAe,GAAG,SAAS,CAAC,SAAS;AACxC,kBAAQ,IAAI,oCAAoC,IAAI,EAAE;AAEtD,cAAI,KAAK,eAAe,MAAM;AAC5B,kBAAM,QAAQ,KAAK,wBAAwB,QAAQ,KAAK,UAAU;AAClE,gBAAI,UAAU,IAAI;AAChB,mBAAK,wBAAwB,OAAO,OAAO,CAAC;AAAA,YAC9C;AAAA,UACF;AAEA,eAAK,iBAAiB;AACtB,eAAK,aAAa;AAElB,cAAI,CAAC,aAAa,CAAC,KAAK,gBAAgB;AACtC,yBAAa,cAAc;AAC3B,oBAAQ,KAAK;AAAA,UACf;AAAA,QACF,CAAC;AAED,yBAAiB,WAAW,MAAM;AAChC,cAAI,CAAC,WAAW;AACd,oBAAQ,MAAM,iCAAiC;AAC/C,oBAAQ;AAAA,cACN,+BAA+B,WAAW,kBAAkB,YAAY;AAAA,YAAA;AAE1E,oBAAQ,KAAK;AAAA,UACf;AAAA,QACF,GAAG,IAAK;AAAA,MACV,SAAS,OAAO;AACd,gBAAQ,MAAM,mCAAmC,KAAK;AACtD,aAAK,iBAAiB;AACtB,aAAK,aAAa;AAClB,gBAAQ,KAAK;AAAA,MACf;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,gBAAkC;AACtC,WAAO,IAAI,QAAQ,OAAO,YAAY;AACpC,UAAI;AACF,cAAM,eAAe,MAAM,+BAAA;AAE3B,YAAI,aAAa,SAAS,GAAG;AAC3B,kBAAQ;AAAA,YACN,eAAe,aAAa,MAAM;AAAA,UAAA;AAEpC,qBAAW,OAAO,cAAc;AAC9B,kBAAM,KAAK,YAAY,GAAG;AAAA,UAC5B;AACA,gBAAM,IAAI,QAAQ,CAACG,aAAY,WAAWA,UAAS,GAAI,CAAC;AAAA,QAC1D;AAEA,cAAM,qBAAqBJ,SAAAA,IAAI,aAC3BC,UAAAA,KAAK,QAAQ,eAAe,oBAAoB,IAChDA,UAAAA,KAAKD,SAAAA,IAAI,WAAA,GAAc,uBAAuB;AAElD,gBAAQ,IAAI,mCAAmC,kBAAkB,EAAE;AAEnE,YAAI,CAACE,QAAAA,WAAW,kBAAkB,GAAG;AACnC,kBAAQ,MAAM,iCAAiC,kBAAkB,EAAE;AACnE,kBAAQ,KAAK;AACb;AAAA,QACF;AAEA,gBAAQ,IAAI,qDAAqD;AAEjE,cAAM,aAAaF,SAAAA,IAAI,aACnBK,kBAAQ,kBAAkB,IAC1BJ,UAAAA,KAAKD,SAAAA,IAAI,WAAA,GAAc,IAAI;AAE/B,gBAAQ,IAAI,+BAA+B,UAAU,EAAE;AAEvD,aAAK,kBAAkBG,yBAAM,oBAAoB,CAAA,GAAI;AAAA,UACnD,aAAa;AAAA,UACb,OAAO;AAAA,UACP,KAAK;AAAA,UACL,OAAO;AAAA,QAAA,CACR;AAED,YAAI,CAAC,KAAK,mBAAmB,CAAC,KAAK,gBAAgB,KAAK;AACtD,kBAAQ,MAAM,2CAA2C;AACzD,kBAAQ,KAAK;AACb;AAAA,QACF;AAEA,gBAAQ;AAAA,UACN;AAAA,UACA,KAAK,gBAAgB;AAAA,QAAA;AAGvB,YAAI;AACJ,YAAI,YAAY;AAEhB,cAAM,eAAe,CAAC,SAAiB;AACrC,gBAAM,SAAS,KAAK,SAAA;AACpB,kBAAQ,IAAI,oBAAoB,MAAM;AAEtC,cACE,OAAO,SAAS,gBAAgB,KAChC,OAAO,SAAS,SAAS,KACxB,OAAO,SAAS,UAAU,KAAK,OAAO,SAAS,SAAS,KACzD,OAAO,SAAS,gBAAgB,KAChC,OAAO,SAAS,iBAAiB,GACjC;AACA,wBAAY;AACZ,yBAAa,cAAc;AAC3B,oBAAQ,IAAI,uCAAuC;AACnD,oBAAQ,IAAI;AAAA,UACd;AAEA,cACE,OAAO,SAAS,OAAO,KACvB,OAAO,SAAS,QAAQ,KACxB,OAAO,SAAS,iBAAiB,GACjC;AACA,oBAAQ,MAAM,oCAAoC,MAAM;AACxD,yBAAa,cAAc;AAC3B,oBAAQ,KAAK;AAAA,UACf;AAAA,QACF;AAEA,YAAI,KAAK,gBAAgB,QAAQ;AAC/B,eAAK,gBAAgB,OAAO,GAAG,QAAQ,YAAY;AAAA,QACrD;AAEA,YAAI,KAAK,gBAAgB,QAAQ;AAC/B,eAAK,gBAAgB,OAAO,GAAG,QAAQ,CAAC,SAAS;AAC/C,kBAAM,cAAc,KAAK,SAAA;AACzB,oBAAQ,MAAM,mBAAmB,WAAW;AAC5C,yBAAa,IAAI;AAAA,UACnB,CAAC;AAAA,QACH;AAEA,aAAK,gBAAgB,GAAG,SAAS,CAAC,QAAQ;AACxC,kBAAQ,MAAM,qCAAqC,GAAG;AACtD,eAAK,kBAAkB;AAEvB,cAAI,CAAC,WAAW;AACd,yBAAa,cAAc;AAC3B,oBAAQ,KAAK;AAAA,UACf;AAAA,QACF,CAAC;AAED,aAAK,gBAAgB,GAAG,SAAS,CAAC,SAAS;AACzC,kBAAQ,IAAI,qCAAqC,IAAI,EAAE;AACvD,eAAK,kBAAkB;AAEvB,cAAI,CAAC,aAAa,CAAC,KAAK,gBAAgB;AACtC,yBAAa,cAAc;AAC3B,oBAAQ,KAAK;AAAA,UACf;AAAA,QACF,CAAC;AAED,yBAAiB,WAAW,MAAM;AAChC,cAAI,CAAC,WAAW;AACd,oBAAQ,MAAM,kCAAkC;AAChD,oBAAQ,KAAK;AAAA,UACf;AAAA,QACF,GAAG,GAAK;AAAA,MACV,SAAS,OAAO;AACd,gBAAQ,MAAM,oCAAoC,KAAK;AACvD,aAAK,kBAAkB;AACvB,gBAAQ,KAAK;AAAA,MACf;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,oBAAmC;AACvC,WAAO,IAAI,QAAQ,CAAC,YAAY;AAC9B,UAAI,KAAK,oBAAoB,MAAM;AACjC,gBAAQ,IAAI,kCAAkC;AAC9C,gBAAA;AACA;AAAA,MACF;AAEA,cAAQ,IAAI,iCAAiC;AAE7C,UAAI;AACF,YAAI,QAAQ,aAAa,WAAW,KAAK,gBAAgB,KAAK;AAC5DJ,6BAAAA,KAAK,uBAAuB,KAAK,gBAAgB,GAAG,IAAI,CAAC,QAAQ;AAC/D,gBAAI,KAAK;AACP,sBAAQ,MAAM,uCAAuC,GAAG;AAAA,YAC1D,OAAO;AACL,sBAAQ,IAAI,2CAA2C;AAAA,YACzD;AACA,iBAAK,kBAAkB;AACvB,oBAAA;AAAA,UACF,CAAC;AAAA,QACH,OAAO;AACL,eAAK,gBAAgB,KAAA;AACrB,kBAAQ,IAAI,2CAA2C;AACvD,eAAK,kBAAkB;AACvB,kBAAA;AAAA,QACF;AAAA,MACF,SAAS,OAAO;AACd,gBAAQ,MAAM,uCAAuC,KAAK;AAC1D,aAAK,kBAAkB;AACvB,gBAAA;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,+BAA+B;AACnC,YAAQ,IAAI,sCAAsC;AAElD,QAAI,KAAK,mBAAmB,QAAQ,KAAK,eAAe,MAAM;AAC5D,UAAI;AACF,cAAM,KAAK,YAAY,KAAK,UAAU;AAAA,MACxC,SAAS,OAAO;AACd,gBAAQ;AAAA,UACN,6CAA6C,KAAK,UAAU;AAAA,UAC5D;AAAA,QAAA;AAAA,MAEJ;AACA,WAAK,iBAAiB;AACtB,WAAK,aAAa;AAAA,IACpB;AAEA,UAAM,YAAY,CAAC,GAAG,KAAK,uBAAuB;AAClD,eAAW,OAAO,WAAW;AAC3B,YAAM,KAAK,YAAY,GAAG;AAAA,IAC5B;AAEA,UAAM,gBAAgB,MAAM,8BAAA;AAC5B,eAAW,OAAO,eAAe;AAC/B,YAAM,KAAK,YAAY,GAAG;AAAA,IAC5B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,gCAAgC;AACpC,YAAQ,IAAI,uCAAuC;AAEnD,QAAI,KAAK,oBAAoB,QAAQ,KAAK,gBAAgB,KAAK;AAC7D,UAAI;AACF,gBAAQ;AAAA,UACN,kDAAkD,KAAK,gBAAgB,GAAG;AAAA,QAAA;AAE5E,cAAM,KAAK,kBAAA;AAAA,MACb,SAAS,OAAO;AACd,gBAAQ,MAAM,+CAA+C,KAAK;AAAA,MACpE;AACA,WAAK,kBAAkB;AAAA,IACzB;AAEA,QAAI;AACF,YAAM,gBAAgB,MAAM,+BAAA;AAC5B,cAAQ;AAAA,QACN,SAAS,cAAc,MAAM;AAAA,MAAA;AAE/B,iBAAW,OAAO,eAAe;AAC/B,cAAM,KAAK,YAAY,GAAG;AAAA,MAC5B;AAAA,IACF,SAAS,OAAO;AACd,cAAQ,MAAM,oDAAoD,KAAK;AAAA,IACzE;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,gBAAgB,OAAgB;AAC9B,SAAK,iBAAiB;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA,EAKA,mBAAmB;AACjB,WAAO;AAAA,MACL,SAAS,KAAK;AAAA,MACd,KAAK,KAAK;AAAA,MACV,kBAAkB,CAAC,GAAG,KAAK,uBAAuB;AAAA,IAAA;AAAA,EAEtD;AAAA;AAAA;AAAA;AAAA,EAKA,oBAAoB;;AAClB,WAAO;AAAA,MACL,SAAS,KAAK;AAAA,MACd,OAAK,UAAK,oBAAL,mBAAsB,QAAO;AAAA,IAAA;AAAA,EAEtC;AACF;ACziBO,MAAM,cAAc;AAAA,EAApB;AACG,sCAAmC;AACnC,yCAAsC;AACtC,0DAAiB,IAAA;AACjB,6DAAoB,IAAA;AACpB,iEAAwB,IAAA;AACxB,0EAAiC,IAAA;AAEjC,mCAAUE,UAAAA,KAAK,WAAW,qBAAqB;AAC/C,+BAAM,QAAA,IAAY;AAClB,qCAAYA,UAAAA,KAAK,QAAA,IAAY,MAAM,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA,EAKvD,sBAAsB;AACpB,UAAM,iBAAiBA,UAAAA,KAAK,WAAW,uBAAuB;AAE9D,SAAK,gBAAgB,IAAIK,uBAAc;AAAA,MACrC,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,MAAML,UAAAA,KAAK,QAAA,IAAY,QAAQ,aAAa;AAAA,MAC5C,OAAO;AAAA,MACP,aAAa;AAAA,MACb,WAAW;AAAA,MACX,gBAAgB;AAAA,QACd,SAAS;AAAA,QACT,kBAAkB;AAAA,QAClB,iBAAiB;AAAA,MAAA;AAAA,IACnB,CACD;AAED,UAAM,kBAAkBA,UAAAA,KAAK,QAAA,IAAY,QAAQ,cAAc;AAC/D,SAAK,cAAc,SAAS,eAAe;AAE3C,SAAK,cAAc,GAAG,UAAU,MAAM;AACpC,WAAK,gBAAgB;AAAA,IACvB,CAAC;AAED,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,iBAAiB,cAAuB;AAC5C,SAAK,aAAa,IAAIK,uBAAc;AAAA,MAClC,MAAM;AAAA,MACN,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,UAAU;AAAA,MACV,WAAW;AAAA,MACX,OAAO;AAAA,MACP,MAAML,UAAAA,KAAK,QAAA,IAAY,QAAQ,aAAa;AAAA,MAC5C,OAAO;AAAA,MACP,aAAa;AAAA,MACb,WAAW;AAAA,MACX,gBAAgB;AAAA,QACd,SAAS,KAAK;AAAA,QACd,iBAAiB;AAAA,QACjB,kBAAkB;AAAA,MAAA;AAAA,IACpB,CACD;AAED,UAAM,YAAY,eAAe,GAAG,KAAK,GAAG,IAAI,YAAY,KAAK,KAAK;AACtE,UAAM,kBAAkB,eACpB,EAAE,UAAU,KAAK,WAAW,MAAM,iBAClC,KAAK;AAET,QAAI,YAAY,qBAAqB;AACnC,WAAK,WAAW,QAAQ,SAAS;AACjC,WAAK,WAAW,YAAY,aAAa,EAAE,MAAM,UAAU;AAAA,IAC7D,OAAO;AACL,WAAK,WAAW;AAAA,QACd,OAAO,oBAAoB,WACvB,kBACA,gBAAgB;AAAA,QACpB,OAAO,oBAAoB,WACvB,CAAA,IACA,EAAE,MAAM,gBAAgB,KAAA;AAAA,MAAK;AAAA,IAErC;AAGA,SAAK,WAAW,YAAY,qBAAqB,CAAC,EAAE,UAAU;AAC5D,YAAM,sBAAsB,IAAI,SAAS,mBAAmB;AAC5D,YAAM,cAAc,IAAIK,uBAAc;AAAA,QACpC,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,UAAU;AAAA,QACV,WAAW;AAAA,QACX,iBAAiB;AAAA,QACjB,OAAO,CAAC;AAAA,QACR,aAAa;AAAA,QACb,iBAAiB,sBAAsB,cAAc;AAAA,QACrD,MAAML,UAAAA,KAAK,QAAA,IAAY,QAAQ,aAAa;AAAA,QAC5C,gBAAgB;AAAA,UACd,SAAS,KAAK;AAAA,UACd,iBAAiB;AAAA,UACjB,kBAAkB;AAAA,QAAA;AAAA,QAEpB,GAAI,sBACA;AAAA,UACE,MAAM;AAAA,QAAA,IAER,CAAA;AAAA,MAAC,CACN;AAED,kBAAY,QAAQ,GAAG;AAGvB,WAAK,sBAAsB,WAAW;AAEtC,UAAI,qBAAqB;AACvB,oBAAY,KAAK,iBAAiB,MAAM;AACtC,sBAAY,KAAA;AAAA,QACd,CAAC;AAAA,MACH;AAEA,aAAO,EAAE,QAAQ,OAAA;AAAA,IACnB,CAAC;AAGD,SAAK,sBAAsB,KAAK,UAAU;AAG1C,SAAK,uBAAuB,KAAK,UAAU;AAE3C,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAKA,kBAAkB,KAA4B;AAC5C,UAAM,cAAc,IAAIK,uBAAc;AAAA,MACpC,MAAML,UAAAA,KAAK,QAAA,IAAY,QAAQ,aAAa;AAAA,MAC5C,gBAAgB;AAAA,QACd,SAAS,KAAK;AAAA,QACd,iBAAiB;AAAA,QACjB,kBAAkB;AAAA,MAAA;AAAA,IACpB,CACD;AAED,QAAI,YAAY,qBAAqB;AACnC,kBAAY,QAAQ,GAAG,KAAK,GAAG,IAAI,GAAG,EAAE;AAAA,IAC1C,OAAO;AACL,kBAAY,SAAS,KAAK,WAAW,EAAE,MAAM,KAAK;AAAA,IACpD;AAGA,SAAK,sBAAsB,WAAW;AAEtC,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAKA,gBAAsC;AACpC,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAKA,mBAAyC;AACvC,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAKA,qBAAqB;AACnB,QAAI,KAAK,iBAAiB,CAAC,KAAK,cAAc,eAAe;AAC3D,WAAK,cAAc,MAAA;AACnB,WAAK,gBAAgB;AAAA,IACvB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,iBAAiB;AACf,QAAI,KAAK,cAAc,CAAC,KAAK,WAAW,eAAe;AACrD,WAAK,WAAW,KAAA;AAChB,WAAK,WAAW,SAAA;AAAA,IAClB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,mBAAmB,KAAa,QAAuB;AACrD,SAAK,cAAc,IAAI,KAAK,MAAM;AAAA,EACpC;AAAA;AAAA;AAAA;AAAA,EAKA,cAAc,KAAwC;AACpD,WAAO,KAAK,cAAc,IAAI,GAAG;AAAA,EACnC;AAAA;AAAA;AAAA;AAAA,EAKA,sBAAsB,KAAa,QAAa;AAC9C,SAAK,kBAAkB,IAAI,KAAK,MAAM;AAAA,EACxC;AAAA;AAAA;AAAA;AAAA,EAKA,oBAAoB,KAAkB;AACpC,WAAO,KAAK,kBAAkB,IAAI,GAAG;AAAA,EACvC;AAAA;AAAA;AAAA;AAAA,EAKA,uBAAuB,KAAa;AAClC,SAAK,kBAAkB,OAAO,GAAG;AAAA,EACnC;AAAA;AAAA;AAAA;AAAA,EAKA,+BAA+B,KAAa,MAAW;AACrD,SAAK,2BAA2B,IAAI,KAAK,IAAI;AAAA,EAC/C;AAAA;AAAA;AAAA;AAAA,EAKA,6BAA6B,KAAkB;AAC7C,WAAO,KAAK,2BAA2B,IAAI,GAAG;AAAA,EAChD;AAAA;AAAA;AAAA;AAAA,EAKA,gCAAgC,KAAa;AAC3C,SAAK,2BAA2B,OAAO,GAAG;AAAA,EAC5C;AAAA;AAAA;AAAA;AAAA,EAKA,eAAe,QAAwB;AACrC,UAAM,eAAe,UAAU,KAAK;AACpC,QAAI,gBAAgB,CAAC,aAAa,eAAe;AAC/C,mBAAa,SAAA;AAAA,IACf;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,qBAAqB,QAAwB;AAC3C,UAAM,eAAe,UAAU,KAAK;AACpC,QAAI,gBAAgB,CAAC,aAAa,eAAe;AAC/C,UAAI,aAAa,eAAe;AAC9B,qBAAa,WAAA;AAAA,MACf,OAAO;AACL,qBAAa,SAAA;AAAA,MACf;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,YAAY,QAAwB;AAClC,UAAM,eAAe,UAAU,KAAK;AACpC,QAAI,gBAAgB,CAAC,aAAa,eAAe;AAC/C,mBAAa,MAAA;AAAA,IACf;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMQ,sBAAsB,QAAuB;AACnD,QAAI,CAAC,UAAU,OAAO,cAAe;AAErC,QAAI,mBAAmB;AAGvB,WAAO,GAAG,aAAa,CAAC,UAAU;AAChC,UAAI,OAAO,iBAAiB,CAAC,kBAAkB;AAE7C,2BAAmB;AAGnB,cAAM,eAAA;AAGN,eAAO,WAAA;AAIP,gBAAQ,SAAS,MAAM;AACrB,6BAAmB;AAAA,QACrB,CAAC;AAAA,MACH;AAAA,IACF,CAAC;AAGD,WAAO,GAAG,cAAc,MAAM;AAE5B,yBAAmB;AAAA,IACrB,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA,EAMQ,uBAAuB,QAAuB;AACpD,QAAI,CAAC,UAAU,OAAO,cAAe;AAGrC,WAAO,GAAG,YAAY,MAAM;AAC1B,aAAO,YAAY,KAAK,kBAAkB;AAAA,IAC5C,CAAC;AAGD,WAAO,GAAG,cAAc,MAAM;AAC5B,aAAO,YAAY,KAAK,oBAAoB;AAAA,IAC9C,CAAC;AAGD,WAAO,GAAG,WAAW,MAAM;AACzB,aAAO,YAAY,KAAK,iBAAiB;AAAA,IAC3C,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU;AACR,SAAK,WAAW,MAAA;AAChB,SAAK,cAAc,MAAA;AACnB,SAAK,kBAAkB,MAAA;AACvB,SAAK,2BAA2B,MAAA;AAAA,EAClC;AACF;AC9VA,MAAM,UAAU,QAAQ,SAAS;AAc1B,MAAM,qBAAN,MAAM,mBAAkB;AAAA,EAAxB;AAWY;AAAA,qCAAY;AAAA,MAC3B,OAAO,CAAC,SAAS,QAAQ,QAAQ,SAAS,OAAO;AAAA,MACjD,UAAU,CAAC,OAAO,WAAW;AAAA,MAC7B,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,MAAM,CAAC,OAAO,OAAO,OAAO,SAAS,SAAS,MAAM;AAAA,IAAA;AAI7C;AAAA,mCAAU;AAAA,MACjB,OAAO;AAAA,QACL;AAAA,UACE,MAAM;AAAA,UACN,YAAY,CAAC,QAAQ,OAAO,OAAO,QAAQ,MAAM;AAAA,QAAA;AAAA,MACnD;AAAA,MAEF,UAAU;AAAA,QACR;AAAA,UACE,MAAM;AAAA,UACN,YAAY,CAAC,MAAM,UAAU;AAAA,QAAA;AAAA,MAC/B;AAAA,MAEF,MAAM;AAAA,QACJ;AAAA,UACE,MAAM;AAAA,UACN,YAAY,CAAC,OAAO,KAAK;AAAA,QAAA;AAAA,MAC3B;AAAA,MAEF,MAAM;AAAA,QACJ;AAAA,UACE,MAAM;AAAA,UACN,YAAY,CAAC,MAAM,MAAM,MAAM,QAAQ,QAAQ,KAAK;AAAA,QAAA;AAAA,MACtD;AAAA,MAEF,KAAK;AAAA,QACH;AAAA,UACE,MAAM;AAAA,UACN,YAAY,CAAC,GAAG;AAAA,QAAA;AAAA,MAClB;AAAA,IACF;AAAA;AAAA,EA9CF,OAAO,cAAiC;AACtC,QAAI,CAAC,mBAAkB,UAAU;AAC/B,yBAAkB,WAAW,IAAI,mBAAA;AAAA,IACnC;AACA,WAAO,mBAAkB;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA,EA+CQ,YAAY,UAIlB;AACA,UAAM,MAAM,KAAK,QAAQ,QAAQ,EAAE,YAAA;AAEnC,UAAM,iBAAiB;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IAAA;AAGF,UAAM,mBAAmB;AAAA,MACvB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IAAA;AAEF,UAAM,gBAAgB,CAAC,MAAM;AAC7B,UAAM,kBAAkB;AAAA,MACtB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IAAA;AAEF,UAAM,oBAAoB,CAAC,QAAQ,QAAQ,OAAO,QAAQ,KAAK;AAC/D,UAAM,uBAAuB;AAAA,MAC3B;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IAAA;AAEF,UAAM,kBAAkB,CAAC,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,OAAO;AAExE,QAAI,eAAe,SAAS,GAAG,GAAG;AAChC,aAAO,EAAE,MAAM,KAAK,UAAU,QAAQ,WAAW,KAAA;AAAA,IACnD,WAAW,iBAAiB,SAAS,GAAG,GAAG;AACzC,aAAO,EAAE,MAAM,KAAK,UAAU,UAAU,WAAW,KAAA;AAAA,IACrD,WAAW,cAAc,SAAS,GAAG,GAAG;AACtC,aAAO,EAAE,MAAM,KAAK,UAAU,OAAO,WAAW,KAAA;AAAA,IAClD,WAAW,gBAAgB,SAAS,GAAG,GAAG;AACxC,aAAO,EAAE,MAAM,KAAK,UAAU,SAAS,WAAW,KAAA;AAAA,IACpD,WAAW,kBAAkB,SAAS,GAAG,GAAG;AAC1C,aAAO,EAAE,MAAM,KAAK,UAAU,WAAW,WAAW,MAAA;AAAA,IACtD,WAAW,qBAAqB,SAAS,GAAG,GAAG;AAC7C,aAAO,EAAE,MAAM,KAAK,UAAU,cAAc,WAAW,MAAA;AAAA,IACzD,WAAW,gBAAgB,SAAS,GAAG,GAAG;AACxC,aAAO,EAAE,MAAM,KAAK,UAAU,SAAS,WAAW,MAAA;AAAA,IACpD,OAAO;AACL,aAAO,EAAE,MAAM,KAAK,UAAU,WAAW,WAAW,MAAA;AAAA,IACtD;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKQ,YAAY,WAA2B;AAC7C,UAAM,YAAoC;AAAA,MACxC,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,SAAS;AAAA,IAAA;AAEX,WAAO,UAAU,UAAU,YAAA,CAAa,KAAK;AAAA,EAC/C;AAAA;AAAA;AAAA;AAAA,EAKQ,sBAAsB,UAA8C;AAC1E,YAAQ,SAAS,UAAA;AAAA,MACf,KAAK;AACH,eAAO;AAAA,MACT,KAAK;AACH,eAAO;AAAA,MACT,KAAK;AACH,eAAO;AAAA,MACT,KAAK;AACH,eAAO;AAAA,MACT;AACE,eAAO;AAAA,IAAA;AAAA,EAEb;AAAA;AAAA;AAAA;AAAA,EAKA,MAAc,gBAAgB,UAAmC;AAC/D,QAAI;AACF,UAAI;AACF,cAAM,SAAS,MAAM,QAAQ,eAAe,EAAE,MAAM,UAAU;AAC9D,eAAO,OAAO,SAAS;AAAA,MACzB,QAAQ;AACN,gBAAQ,IAAI,uBAAuB;AACnC,eAAO;AAAA;AAAA,QAAsB,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA,MACvC;AAAA,IACF,SAAS,OAAY;AACnB,cAAQ,MAAM,+BAA+B,KAAK;AAClD,aAAO;AAAA;AAAA,QAAsB,QAAQ;AAAA;AAAA,iBAAqB,+BAAO,YAAW,eAAe;AAAA,IAC7F;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,cAAc,SAAiB;AACnC,UAAM,QAAQ,MAAM,GAAG,SAAS,QAAQ,SAAS,EAAE,eAAe,MAAM;AACxE,WAAO,MAAM,IAAI,CAAC,YAAY;AAAA,MAC5B,MAAM,OAAO;AAAA,MACb,aAAa,OAAO,YAAA;AAAA,MACpB,MAAM,KAAK,KAAK,SAAS,OAAO,IAAI;AAAA,IAAA,EACpC;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,gBAAgB,YAAoB;AACxC,UAAM,GAAG,SAAS,MAAM,YAAY,EAAE,WAAW,MAAM;AACvD,WAAO,EAAE,SAAS,KAAA;AAAA,EACpB;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,WAAW,UAAkB;AACjC,UAAM,GAAG,SAAS,UAAU,UAAU,EAAE;AACxC,WAAO,EAAE,SAAS,KAAA;AAAA,EACpB;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,WAAW,YAAoB;AACnC,UAAM,QAAQ,MAAM,GAAG,SAAS,KAAK,UAAU;AAC/C,QAAI,MAAM,eAAe;AACvB,YAAM,GAAG,SAAS,MAAM,YAAY,EAAE,WAAW,MAAM;AAAA,IACzD,OAAO;AACL,YAAM,GAAG,SAAS,OAAO,UAAU;AAAA,IACrC;AACA,WAAO,EAAE,SAAS,KAAA;AAAA,EACpB;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,OAAO,SAAiB,SAAiB;AAC7C,QAAI;AACF,YAAM,GAAG,SAAS,OAAO,SAAS,OAAO;AACzC,aAAO,EAAE,SAAS,KAAA;AAAA,IACpB,SAAS,OAAO;AACd,cAAQ,MAAM,kCAAkC,KAAK;AACrD,YAAM;AAAA,IACR;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,OAAO,UAAkB;AAC7B,QAAI;AACF,YAAM,GAAG,SAAS,OAAO,QAAQ;AACjC,aAAO,EAAE,QAAQ,KAAA;AAAA,IACnB,QAAQ;AACN,aAAO,EAAE,QAAQ,MAAA;AAAA,IACnB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,SAAS,UAAkB;AAC/B,QAAI;AACF,YAAM,UAAU,MAAM,GAAG,SAAS,SAAS,UAAU,OAAO;AAC5D,aAAO;AAAA,IACT,SAAS,OAAO;AACd,cAAQ,MAAM,uBAAuB,KAAK;AAC1C,YAAM;AAAA,IACR;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,iBAAiB,UAAkB;AACvC,QAAI;AACF,YAAM,WAAW,KAAK,YAAY,QAAQ;AAE1C,UAAI,CAAC,SAAS,WAAW;AACvB,eAAO;AAAA,UACL,SAAS;AAAA,UACT;AAAA,UACA,OAAO,aAAa,SAAS,IAAI;AAAA,UACjC,SAAS,KAAK,sBAAsB,QAAQ;AAAA,QAAA;AAAA,MAEhD;AAEA,UAAI,UAAU;AACd,UAAI,YAAY;AAChB,UAAI,UAAyB;AAE7B,UAAI,SAAS,aAAa,QAAQ;AAChC,kBAAU,MAAM,GAAG,SAAS,SAAS,UAAU,OAAO;AAAA,MACxD,WAAW,SAAS,aAAa,UAAU;AACzC,YAAI,SAAS,SAAS,SAAS;AAC7B,oBAAU,MAAM,KAAK,gBAAgB,QAAQ;AAAA,QAC/C,WAAW,SAAS,SAAS,QAAQ;AACnC,oBAAU;AAAA,QACZ,OAAO;AACL,oBAAU,kBAAkB,SAAS,IAAI;AAAA,QAC3C;AAAA,MACF,WAAW,SAAS,aAAa,OAAO;AACtC,kBAAU,MAAM,GAAG,SAAS,SAAS,QAAQ;AAC7C,kBAAU;AAAA,MACZ,WAAW,SAAS,aAAa,SAAS;AACxC,cAAM,cAAc,MAAM,GAAG,SAAS,SAAS,QAAQ;AACvD,cAAM,aAAa,YAAY,SAAS,QAAQ;AAChD,cAAM,WAAW,KAAK,YAAY,SAAS,IAAI;AAC/C,oBAAY,QAAQ,QAAQ,WAAW,UAAU;AACjD,kBAAU;AAAA,MACZ;AAEA,aAAO;AAAA,QACL,SAAS;AAAA,QACT;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MAAA;AAAA,IAEJ,SAAS,OAAY;AACnB,cAAQ,MAAM,iCAAiC,KAAK;AACpD,aAAO;AAAA,QACL,SAAS;AAAA,QACT,UAAU,KAAK,YAAY,QAAQ;AAAA,QACnC,QAAO,+BAAO,YAAW;AAAA,QACzB,SAAS;AAAA,MAAA;AAAA,IAEb;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,eAAe,UAAkB;AACrC,QAAI;AACF,YAAM,SAAS,MAAM,GAAG,SAAS,SAAS,QAAQ;AAClD,aAAO;AAAA,IACT,SAAS,OAAO;AACd,cAAQ,MAAM,8BAA8B,KAAK;AACjD,YAAM;AAAA,IACR;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,UAAU,UAAkB,SAAiB;AACjD,QAAI;AACF,YAAM,GAAG,SAAS,UAAU,UAAU,SAAS,OAAO;AACtD,aAAO,EAAE,SAAS,KAAA;AAAA,IACpB,SAAS,OAAO;AACd,cAAQ,MAAM,uBAAuB,KAAK;AAC1C,YAAM;AAAA,IACR;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,gBAAgB,UAAkB,aAA0B;AAChE,QAAI;AACF,YAAM,SAAS,OAAO,KAAK,WAAW;AACtC,YAAM,GAAG,SAAS,UAAU,UAAU,MAAM;AAC5C,aAAO,EAAE,SAAS,KAAA;AAAA,IACpB,SAAS,OAAO;AACd,cAAQ,MAAM,8BAA8B,KAAK;AACjD,YAAM;AAAA,IACR;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,sBAAsB,UAAkB,aAAqB;AACjE,QAAI;AACF,YAAM,SAAS,OAAO,KAAK,aAAa,QAAQ;AAChD,YAAM,GAAG,SAAS,UAAU,UAAU,MAAM;AAC5C,aAAO,EAAE,SAAS,KAAA;AAAA,IACpB,SAAS,OAAO;AACd,cAAQ,MAAM,8BAA8B,KAAK;AACjD,YAAM;AAAA,IACR;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,aAAa,UAAkB;AACnC,QAAI;AACF,YAAM,SAAS,MAAM,GAAG,SACrB,OAAO,QAAQ,EACf,KAAK,MAAM,IAAI,EACf,MAAM,MAAM,KAAK;AACpB,aAAO,EAAE,QAAQ,MAAM,SAAA;AAAA,IACzB,SAAS,OAAO;AACd,aAAO,EAAE,QAAQ,OAAO,MAAM,UAAU,MAAA;AAAA,IAC1C;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKQ,qBAAqB;AAC3B,WAAO,KAAK,KAAKD,SAAAA,IAAI,QAAQ,UAAU,GAAG,mBAAmB;AAAA,EAC/D;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,iBAAiB;AACrB,QAAI;AACF,YAAM,kBAAkB,KAAK,mBAAA;AAC7B,YAAM,SAAS,MAAM,GAAG,SACrB,OAAO,eAAe,EACtB,KAAK,MAAM,IAAI,EACf,MAAM,MAAM,KAAK;AAEpB,UAAI,CAAC,QAAQ;AACX,eAAO,EAAE,SAAS,MAAM,OAAO,CAAA,EAAC;AAAA,MAClC;AAEA,YAAM,cAAc,MAAM,GAAG,SAAS,SAAS,iBAAiB,OAAO;AACvE,YAAM,QAAQ,KAAK,MAAM,WAAW;AAEpC,aAAO,EAAE,SAAS,MAAM,OAAO,MAAM,QAAQ,KAAK,IAAI,QAAQ,GAAC;AAAA,IACjE,SAAS,OAAO;AACd,cAAQ,MAAM,aAAa,KAAK;AAChC,aAAO,EAAE,SAAS,OAAO,OAAO,CAAA,EAAC;AAAA,IACnC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,gBAAgB,OAAc;AAClC,QAAI;AACF,YAAM,kBAAkB,KAAK,mBAAA;AAC7B,YAAM,GAAG,SAAS;AAAA,QAChB;AAAA,QACA,KAAK,UAAU,OAAO,MAAM,CAAC;AAAA,QAC7B;AAAA,MAAA;AAEF,aAAO,EAAE,SAAS,KAAA;AAAA,IACpB,SAAS,OAAO;AACd,cAAQ,MAAM,aAAa,KAAK;AAChC,aAAO,EAAE,SAAS,OAAO,SAAS,OAAA;AAAA,IACpC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKQ,eAAe,UAA4B;AACjD,WAAO;AAAA,MACL,MAAM;AAAA,MACN,MAAM,KAAK,SAAS,QAAQ;AAAA,MAC5B,WAAW,KAAK,QAAQ,QAAQ,EAAE,YAAA;AAAA,MAClC,MAAM,KAAK,oBAAoB,QAAQ;AAAA,MACvC,QAAQ,GAAG,WAAW,QAAQ;AAAA,IAAA;AAAA,EAElC;AAAA;AAAA;AAAA;AAAA,EAKQ,oBAAoB,UAA0B;AACpD,UAAM,MAAM,KAAK,QAAQ,QAAQ,EAAE,YAAA;AACnC,eAAW,CAAC,MAAM,UAAU,KAAK,OAAO,QAAQ,KAAK,SAAS,GAAG;AAC/D,UAAI,WAAW,SAAS,GAAG,GAAG;AAC5B,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,SACJ,QACA,UAGI,IACsB;AAC1B,QAAI;AACF,YAAM,EAAE,UAAU,UAAA,IAAc,MAAMO,SAAAA,OAAO,eAAe,QAAQ;AAAA,QAClE,YAAY,CAAC,UAAU;AAAA,QACvB,SAAS,QAAQ,WAAY,KAAK,QAAQ;AAAA,MAAA,CAC3C;AAED,UAAI,CAAC,YAAY,UAAU,SAAS,GAAG;AACrC,cAAM,WAAW,KAAK,eAAe,UAAU,CAAC,CAAC;AAEjD,YACE,QAAQ,gBACR,CAAC,KAAK,UAAU,MAAM,SAAS,SAAS,SAAgB,GACxD;AACA,gBAAMA,SAAAA,OAAO,eAAe,QAAQ;AAAA,YAClC,MAAM;AAAA,YACN,OAAO;AAAA,YACP,SAAS;AAAA,UAAA,CACV;AACD,iBAAO;AAAA,QACT;AAGA,eAAO,YAAY,KAAK,iBAAiB,QAAQ;AACjD,eAAO;AAAA,MACT;AAAA,IACF,SAAS,OAAO;AACd,cAAQ,MAAM,uBAAuB,KAAK;AAC1CA,sBAAO,aAAa,MAAM,WAAW;AAAA,IACvC;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,cACJ,QACgD;AAChD,QAAI;AACF,YAAM,EAAE,UAAU,UAAA,IAAc,MAAMA,SAAAA,OAAO,eAAe,QAAQ;AAAA,QAClE,YAAY,CAAC,eAAe;AAAA,QAC5B,OAAO;AAAA,MAAA,CACR;AAED,UAAI,CAAC,YAAY,UAAU,SAAS,GAAG;AACrC,cAAM,cAAc,UAAU,CAAC;AAC/B,cAAM,cAAc;AAAA,UAClB,MAAM;AAAA,UACN,MAAM,KAAK,SAAS,WAAW;AAAA,QAAA;AAEjC,eAAO,YAAY,KAAK,oBAAoB,WAAW;AACvD,eAAO;AAAA,MACT;AAAA,IACF,SAAS,OAAO;AACd,cAAQ,MAAM,4BAA4B,KAAK;AAC/CA,sBAAO,aAAa,MAAM,WAAW;AAAA,IACvC;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,eAAe,KAAoB;AACvC,QAAI,CAAC,OAAO,IAAI,cAAe;AAG/B,UAAM,aAAa,IAAI,YAAY,OAAA;AACnC,UAAM,mBAAmB,WAAW,SAAS,yBAAyB;AAGtE,UAAM,KAAK,SAAS,KAAK;AAAA,MACvB,SAAS,mBACJ,KAAK,QAAQ,QACb,KAAK,QAAQ;AAAA,MAClB,cAAc;AAAA,IAAA,CACf;AAAA,EACH;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,oBAAoB,KAAoB;AAC5C,QAAI,CAAC,OAAO,IAAI,cAAe;AAC/B,UAAM,KAAK,cAAc,GAAG;AAAA,EAC9B;AAAA;AAAA;AAAA;AAAA,EAIA,MAAM,iBAAiB,SAGpB;AACD,QAAI;AACF,UAAI,YAAY;AAChB,UAAI,YAAY;AAEhB,YAAM,gBAAgB,OAAO,gBAAwB;AACnD,cAAM,QAAQ,MAAM,GAAG,SAAS,KAAK,WAAW;AAEhD,YAAI,MAAM,UAAU;AAClB,uBAAa,MAAM;AACnB;AAAA,QACF,WAAW,MAAM,eAAe;AAC9B,gBAAM,UAAU,MAAM,GAAG,SAAS,QAAQ,WAAW;AACrD,qBAAW,SAAS,SAAS;AAC3B,kBAAM,YAAY,KAAK,KAAK,aAAa,KAAK;AAC9C,kBAAM,cAAc,SAAS;AAAA,UAC/B;AAAA,QACF;AAAA,MACF;AAEA,YAAM,cAAc,OAAO;AAC3B,aAAO,EAAE,MAAM,WAAW,UAAA;AAAA,IAC5B,SAAS,OAAO;AACd,cAAQ,MAAM,qCAAqC,KAAK;AACxD,aAAO,EAAE,MAAM,GAAG,WAAW,EAAA;AAAA,IAC/B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,uBAAuB,SAM1B;AACD,QAAI;AACF,YAAM,QAID,CAAA;AAEL,YAAM,gBAAgB,OAAO,aAAqB,eAAe,OAAO;AACtE,cAAM,UAAU,MAAM,GAAG,SAAS,QAAQ,aAAa;AAAA,UACrD,eAAe;AAAA,QAAA,CAChB;AAED,mBAAW,SAAS,SAAS;AAC3B,gBAAM,WAAW,KAAK,KAAK,aAAa,MAAM,IAAI;AAClD,gBAAM,UAAU,eACZ,KAAK,KAAK,cAAc,MAAM,IAAI,IAClC,MAAM;AAEV,cAAI,MAAM,UAAU;AAClB,kBAAM,KAAK;AAAA,cACT,cAAc;AAAA,cACd;AAAA,cACA,QAAQ;AAAA,YAAA,CACT;AAAA,UACH,WAAW,MAAM,eAAe;AAC9B,kBAAM,KAAK;AAAA,cACT,cAAc;AAAA,cACd;AAAA,cACA,QAAQ;AAAA,YAAA,CACT;AACD,kBAAM,cAAc,UAAU,OAAO;AAAA,UACvC;AAAA,QACF;AAAA,MACF;AAEA,YAAM,cAAc,OAAO;AAC3B,aAAO,EAAE,MAAA;AAAA,IACX,SAAS,OAAO;AACd,cAAQ,MAAM,wCAAwC,KAAK;AAC3D,aAAO,EAAE,OAAO,GAAC;AAAA,IACnB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,WAAW,UAAoC;AACnD,QAAI;AACF,YAAM,GAAG,SAAS,OAAO,QAAQ;AACjC,aAAO;AAAA,IACT,QAAQ;AACN,aAAO;AAAA,IACT;AAAA,EACF;AACF;AAvpBE,cADW,oBACI;AADV,IAAM,oBAAN;ACZA,MAAM,cAAc;AAAA;AAAA;AAAA;AAAA,EAIzB,MAAM,gBAAgB;AACpB,UAAM,SAAS,MAAMA,SAAAA,OAAO,eAAe;AAAA,MACzC,YAAY,CAAC,eAAe;AAAA,IAAA,CAC7B;AACD,WAAO,OAAO,UAAU,CAAC;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,WAAW;AACf,UAAM,SAAS,MAAMA,SAAAA,OAAO,eAAe;AAAA,MACzC,YAAY,CAAC,UAAU;AAAA,IAAA,CACxB;AACD,QAAI,OAAO,YAAY,OAAO,UAAU,WAAW,GAAG;AACpD,aAAO;AAAA,IACT;AACA,WAAO,OAAO,UAAU,CAAC;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,SAAS,SAAc;AAC3B,UAAM,SAAS,MAAMA,SAAAA,OAAO,eAAe;AAAA,MACzC,GAAG;AAAA,MACH,SAAS;AAAA,QACP,EAAE,MAAM,cAAc,YAAY,CAAC,MAAM,EAAA;AAAA,QACzC,EAAE,MAAM,aAAa,YAAY,CAAC,GAAG,EAAA;AAAA,MAAE;AAAA,IACzC,CACD;AACD,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,cAAc,aAAkB;AACpC,QAAI;AACF,YAAM,SAAS,MAAMA,SAAAA,OAAO,eAAe;AAAA,QACzC,OAAO;AAAA,QACP,aAAa,YAAW,oBAAI,KAAA,GAAO,YAAA,EAAc,MAAM,GAAG,EAAE,EAAE,QAAQ,MAAM,GAAG,CAAC;AAAA,QAChF,SAAS;AAAA,UACP,EAAE,MAAM,cAAc,YAAY,CAAC,MAAM,EAAA;AAAA,UACzC,EAAE,MAAM,aAAa,YAAY,CAAC,GAAG,EAAA;AAAA,QAAE;AAAA,MACzC,CACD;AAED,UAAI,OAAO,YAAY,CAAC,OAAO,UAAU;AACvC,eAAO,EAAE,SAAS,OAAO,SAAS,SAAA;AAAA,MACpC;AAEA,YAAM,GAAG,SAAS;AAAA,QAChB,OAAO;AAAA,QACP,KAAK,UAAU,aAAa,MAAM,CAAC;AAAA,QACnC;AAAA,MAAA;AAEF,aAAO;AAAA,QACL,SAAS;AAAA,QACT,UAAU,OAAO;AAAA,QACjB,SAAS;AAAA,MAAA;AAAA,IAEb,SAAS,OAAO;AACd,cAAQ,MAAM,WAAW,KAAK;AAC9B,aAAO,EAAE,SAAS,OAAO,SAAS,OAAA;AAAA,IACpC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,gBAAgB;AACpB,QAAI;AACF,YAAM,SAAS,MAAMA,SAAAA,OAAO,eAAe;AAAA,QACzC,OAAO;AAAA,QACP,YAAY,CAAC,UAAU;AAAA,QACvB,SAAS;AAAA,UACP,EAAE,MAAM,QAAQ,YAAY,CAAC,KAAK,EAAA;AAAA,UAClC,EAAE,MAAM,aAAa,YAAY,CAAC,GAAG,EAAA;AAAA,QAAE;AAAA,MACzC,CACD;AAED,UAAI,OAAO,YAAY,OAAO,UAAU,WAAW,GAAG;AACpD,eAAO,EAAE,SAAS,OAAO,SAAS,SAAA;AAAA,MACpC;AAEA,YAAM,WAAW,OAAO,UAAU,CAAC;AAEnC,YAAM,aAAa,MAAM,GAAG,SAAS,SAAS,QAAQ;AACtD,aAAO;AAAA,QACL,SAAS;AAAA,QACT;AAAA,QACA;AAAA,QACA,SAAS;AAAA,MAAA;AAAA,IAEb,SAAS,OAAO;AACd,cAAQ,MAAM,WAAW,KAAK;AAC9B,aAAO,EAAE,SAAS,OAAO,SAAS,OAAA;AAAA,IACpC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,cAAc,SAAc;AAChC,UAAM,SAAS,MAAMA,SAAAA,OAAO,eAAe;AAAA,MACzC,OAAO,QAAQ,SAAS;AAAA,MACxB,YAAY,QAAQ,cAAc,CAAC,eAAe;AAAA,MAClD,aAAa,QAAQ,eAAe;AAAA,IAAA,CACrC;AAED,QAAI,OAAO,YAAY,OAAO,UAAU,WAAW,GAAG;AACpD,aAAO;AAAA,IACT;AAEA,WAAO,OAAO,UAAU,CAAC;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,eAAe,QAAuB,SAAc;AACxD,QAAI,CAAC,QAAQ;AACX,aAAO,EAAE,UAAU,MAAM,WAAW,CAAA,EAAC;AAAA,IACvC;AACA,WAAO,MAAMA,SAAAA,OAAO,eAAe,QAAQ,OAAO;AAAA,EACpD;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,eAAe,QAAuB,SAAc;AACxD,QAAI,CAAC,QAAQ;AACX,aAAO,EAAE,UAAU,MAAM,UAAU,GAAA;AAAA,IACrC;AACA,WAAO,MAAMA,SAAAA,OAAO,eAAe,QAAQ,OAAO;AAAA,EACpD;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,eAAe,QAAuB,SAAc;AACxD,QAAI,CAAC,QAAQ;AACX,aAAO,EAAE,UAAU,EAAA;AAAA,IACrB;AACA,WAAO,MAAMA,SAAAA,OAAO,eAAe,QAAQ,OAAO;AAAA,EACpD;AAAA;AAAA;AAAA;AAAA,EAKA,aAAa,OAAe,SAAiB;AAC3CA,oBAAO,aAAa,OAAO,OAAO;AAAA,EACpC;AACF;ACtJO,MAAM,YAAY;AAAA,EAGvB,YAAYC,SAAiB,OAAO;AAF5B;AAGN,SAAK,QAAQA;AAAA,EACf;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW,QAAQ,SAAS;AAC1B,UAAM,OAAOC,SAAAA,KAAK;AAAA,MAChB,KAAK,aAAa,KAAK;AAAA,IAAA;AAEzBA,aAAAA,KAAK,mBAAmB,IAAI;AAAA,EAC9B;AAAA;AAAA;AAAA;AAAA,EAKQ,aACN,iBAC8C;AAC9C,UAAM,eAA6C,CAAA;AACnD,QAAI,KAAK,OAAO;AACd,mBAAa;AAAA,QACX,EAAE,OAAO,SAAS,MAAM,iBAAA;AAAA,QACxB,EAAE,OAAO,QAAQ,MAAM,cAAA;AAAA,MAAc;AAAA,IAEzC;AAEA,UAAM,WAAyD;AAAA,MAC7D;AAAA,QACE,OAAO;AAAA,QACP,SAAS;AAAA,UACP;AAAA,YACE,OAAO;AAAA,YACP,aAAa;AAAA,YACb,SAAS;AAAA,YACT,OAAO,YAAY;AACjB,oBAAM,MAAMH,SAAAA,cAAc,iBAAA;AAC1B,kBAAI,OAAO,CAAC,IAAI,eAAe;AAC7B,oBAAI,MAAA;AACJ,oBAAI,YAAY,KAAK,+BAA+B;AAAA,cACtD;AAAA,YACF;AAAA,UAAA;AAAA,UAEF;AAAA,YACE,OAAO;AAAA,YACP,aAAa;AAAA,YACb,SAAS;AAAA,YACT,OAAO,YAAY;AACjB,oBAAM,MAAMA,SAAAA,cAAc,iBAAA;AAC1B,kBAAI,OAAO,CAAC,IAAI,eAAe;AAC7B,oBAAI,MAAA;AACJ,sBAAM,aAAa,IAAI,YAAY,OAAA;AACnC,sBAAM,mBAAmB,WAAW;AAAA,kBAClC;AAAA,gBAAA;AAGF,oBAAI,kBAAkB;AACpB,sBAAI,YAAY,KAAK,0BAA0B;AAAA,gBACjD,OAAO;AAEL,sBAAI,YAAY,KAAK,0BAA0B;AAAA,gBACjD;AAAA,cACF;AAAA,YACF;AAAA,UAAA;AAAA,UAEF,EAAE,MAAM,YAAA;AAAA,UACR;AAAA,YACE,OAAO;AAAA,YACP,SAAS;AAAA,cACP;AAAA,gBACE,OAAO;AAAA,gBACP,aAAa;AAAA,gBACb,SAAS;AAAA,gBACT,OAAO,YAAY;AACjB,wBAAM,MAAMA,SAAAA,cAAc,iBAAA;AAC1B,sBAAI,OAAO,CAAC,IAAI,eAAe;AAC7B,wBAAI,MAAA;AACJ,wBAAI,YAAY,KAAK,+BAA+B;AAAA,kBACtD;AAAA,gBACF;AAAA,cAAA;AAAA,cAEF;AAAA,gBACE,OAAO;AAAA,gBACP,aAAa;AAAA,gBACb,SAAS;AAAA,gBACT,OAAO,YAAY;AACjB,wBAAM,MAAMA,SAAAA,cAAc,iBAAA;AAC1B,sBAAI,OAAO,CAAC,IAAI,eAAe;AAC7B,wBAAI,MAAA;AACJ,wBAAI,YAAY,KAAK,qCAAqC;AAAA,kBAC5D;AAAA,gBACF;AAAA,cAAA;AAAA,YACF;AAAA,UACF;AAAA,UAEF,EAAE,MAAM,YAAA;AAAA,UACR;AAAA,YACE,OAAO;AAAA,YACP,MAAM;AAAA,YACN,aAAa;AAAA,UAAA;AAAA,QACf;AAAA,MACF;AAAA,MAEF;AAAA,QACE,OAAO;AAAA,QACP,SAAS;AAAA,UACP;AAAA,YACE,OAAO;AAAA,YACP,MAAM;AAAA,YACN,aAAa;AAAA,UAAA;AAAA,UAEf;AAAA,YACE,OAAO;AAAA,YACP,MAAM;AAAA,YACN,aAAa;AAAA,UAAA;AAAA,UAEf,EAAE,MAAM,YAAA;AAAA,UACR;AAAA,YACE,OAAO;AAAA,YACP,MAAM;AAAA,YACN,aAAa;AAAA,UAAA;AAAA,UAEf;AAAA,YACE,OAAO;AAAA,YACP,MAAM;AAAA,YACN,aAAa;AAAA,UAAA;AAAA,UAEf;AAAA,YACE,OAAO;AAAA,YACP,MAAM;AAAA,YACN,aAAa;AAAA,UAAA;AAAA,UAEf;AAAA,YACE,OAAO;AAAA,YACP,MAAM;AAAA,YACN,aAAa;AAAA,UAAA;AAAA,UAEf;AAAA,YACE,OAAO;AAAA,YACP,MAAM;AAAA,YACN,aAAa;AAAA,UAAA;AAAA,QACf;AAAA,MACF;AAAA,MAEF;AAAA,QACE,OAAO;AAAA,QACP,SAAS;AAAA,UACP;AAAA,YACE,OAAO;AAAA,YACP,MAAM;AAAA,YACN,aAAa;AAAA,UAAA;AAAA,UAEf;AAAA,YACE,OAAO;AAAA,YACP,MAAM;AAAA,YACN,aAAa;AAAA,UAAA;AAAA,UAEf;AAAA,YACE,OAAO;AAAA,YACP,MAAM;AAAA,YACN,aAAa;AAAA,UAAA;AAAA,UAEf,EAAE,MAAM,YAAA;AAAA,UACR;AAAA,YACE,OAAO;AAAA,YACP,MAAM;AAAA,YACN,aAAa;AAAA,UAAA;AAAA,QACf;AAAA,MACF;AAAA,MAEF,GAAI,KAAK,QACL;AAAA,QACE;AAAA,UACE,OAAO;AAAA,UACP,SAAS;AAAA,QAAA;AAAA,MACX,IAEF,CAAA;AAAA,MACJ;AAAA,QACE,OAAO;AAAA,QACP,SAAS;AAAA,UACP;AAAA,YACE,OAAO;AAAA,YACP,MAAM;AAAA,YACN,aAAa;AAAA,UAAA;AAAA,QACf;AAAA,MACF;AAAA,IACF;AAGF,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAKA,eAAe,KAAoB;AACjC,UAAM,WAAWG,SAAAA,KAAK,kBAAkB;AAAA,MACtC;AAAA,QACE,OAAO;AAAA,QACP,OAAO,MAAM;AACX,qCAAK,YAAY,KAAK;AAAA,QACxB;AAAA,MAAA;AAAA,MAEF;AAAA,QACE,OAAO;AAAA,QACP,OAAO,MAAM;AACX,qCAAK,YAAY,KAAK;AAAA,QACxB;AAAA,MAAA;AAAA,MAEF,EAAE,MAAM,YAAA;AAAA,MACR;AAAA,QACE,OAAO;AAAA,QACP,OAAO,MAAMT,SAAAA,IAAI,KAAA;AAAA,MAAK;AAAA,IACxB,CACD;AAED,aAAS,MAAM,EAAE,QAAQ,IAAA,CAAK;AAAA,EAChC;AAAA;AAAA;AAAA;AAAA,EAKA,kBAAkB,KAAoB;AACpC,UAAM,cAAcS,SAAAA,KAAK,kBAAkB;AAAA,MACzC;AAAA,QACE,OAAO;AAAA,QACP,OAAO,MAAM;AACX,qCAAK,YAAY,KAAK;AAAA,QACxB;AAAA,MAAA;AAAA,MAEF;AAAA,QACE,OAAO;AAAA,QACP,OAAO,MAAM;AACX,qCAAK,YAAY,KAAK;AAAA,QACxB;AAAA,MAAA;AAAA,IACF,CACD;AAED,gBAAY,MAAM,EAAE,QAAQ,IAAA,CAAK;AAAA,EACnC;AAAA;AAAA;AAAA;AAAA,EAKA,eAAe,KAAoB;AACjC,UAAM,WAAWA,SAAAA,KAAK,kBAAkB;AAAA,MACtC,EAAE,OAAO,MAAM,MAAM,OAAA;AAAA,MACrB,EAAE,OAAO,MAAM,MAAM,OAAA;AAAA,MACrB,EAAE,MAAM,YAAA;AAAA,MACR,EAAE,OAAO,MAAM,MAAM,MAAA;AAAA,MACrB,EAAE,OAAO,MAAM,MAAM,OAAA;AAAA,MACrB,EAAE,OAAO,MAAM,MAAM,QAAA;AAAA,MACrB,EAAE,MAAM,YAAA;AAAA,MACR,EAAE,OAAO,MAAM,MAAM,YAAA;AAAA,IAAY,CAClC;AAED,aAAS,MAAM,EAAE,QAAQ,IAAA,CAAK;AAAA,EAChC;AAAA;AAAA;AAAA;AAAA,EAKA,eAAe,KAAoB;AACjC,UAAM,WAAWA,SAAAA,KAAK,kBAAkB;AAAA,MACtC,EAAE,OAAO,MAAM,MAAM,SAAA;AAAA,MACrB,EAAE,OAAO,QAAQ,MAAM,YAAA;AAAA,MACvB,EAAE,OAAO,MAAM,MAAM,UAAA;AAAA,MACrB,EAAE,MAAM,YAAA;AAAA,MACR;AAAA,QACE,QAAO,2BAAK,kBAAiB,SAAS;AAAA,QACtC,MAAM;AAAA,MAAA;AAAA,IACR,CACD;AAED,aAAS,MAAM,EAAE,QAAQ,IAAA,CAAK;AAAA,EAChC;AAAA;AAAA;AAAA;AAAA,EAKA,cAAc,KAAoB;AAChC,QAAI,CAAC,KAAK,MAAO;AAEjB,UAAM,UAAUA,SAAAA,KAAK,kBAAkB;AAAA,MACrC,EAAE,OAAO,SAAS,MAAM,iBAAA;AAAA,MACxB,EAAE,OAAO,QAAQ,MAAM,cAAA;AAAA,IAAc,CACtC;AAED,YAAQ,MAAM,EAAE,QAAQ,IAAA,CAAK;AAAA,EAC/B;AAAA;AAAA;AAAA;AAAA,EAKA,gBAAgB,KAAoB;AAClC,UAAM,YAAYA,SAAAA,KAAK,kBAAkB;AAAA,MACvC;AAAA,QACE,OAAO;AAAA,QACP,OAAO,MAAM;AACX,cAAI,UAAU;AACd,cAAI;AACF,kBAAM,cAAc,KAAK,KAAKT,SAAAA,IAAI,WAAA,GAAc,cAAc;AAC9D,gBAAIE,QAAAA,WAAW,WAAW,GAAG;AAC3B,oBAAM,cAAc,KAAK,MAAMQ,QAAAA,aAAa,aAAa,MAAM,CAAC;AAChE,wBAAU,YAAY,WAAW;AAAA,YACnC;AAAA,UACF,SAAS,OAAO;AACd,oBAAQ,MAAM,YAAY,KAAK;AAAA,UACjC;AACAH,mBAAAA,OAAO,eAAe,KAAM;AAAA,YAC1B,OAAO;AAAA,YACP,SAAS;AAAA,YACT,QAAQ,MAAM,OAAO;AAAA;AAAA,YACrB,SAAS,CAAC,IAAI;AAAA,YACd,MAAM;AAAA,UAAA,CACP;AAAA,QACH;AAAA,MAAA;AAAA,IACF,CACD;AAED,cAAU,MAAM,EAAE,QAAQ,IAAA,CAAK;AAAA,EACjC;AACF;ACvUO,MAAM,WAAW;AAAA,EAMtB,YACEI,gBACAC,oBACAC,gBACAC,cACA;AAVM;AACA;AACA;AACA;AAQN,SAAK,gBAAgBH;AACrB,SAAK,oBAAoBC;AACzB,SAAK,gBAAgBC;AACrB,SAAK,cAAcC;AACnB,SAAK,iBAAA;AAAA,EACP;AAAA;AAAA;AAAA;AAAA,EAKQ,mBAAmB;AACzB,SAAK,mBAAA;AACL,SAAK,oBAAA;AACL,SAAK,wBAAA;AACL,SAAK,oBAAA;AACL,SAAK,kBAAA;AACL,SAAK,qBAAA;AAAA,EACP;AAAA;AAAA;AAAA;AAAA,EAKQ,qBAAqB;AAE3BC,aAAAA,QAAQ,GAAG,yBAAyB,CAAC,OAAO,SAAS;AACnD,YAAM,MAAM,KAAK;AACjB,YAAM,eAAe,KAAK,cAAc,cAAc,GAAG;AAEzD,UAAI,gBAAgB,CAAC,aAAa,eAAe;AAC/C,qBAAa,YAAY,KAAK,yBAAyB,IAAI;AAAA,MAC7D;AAAA,IACF,CAAC;AAGDA,aAAAA,QAAQ,GAAG,kBAAkB,CAAC,OAAO,SAAS;AAC5C,YAAM,MAAM,KAAK;AACjB,YAAM,eAAe,KAAK,cAAc,cAAc,GAAG;AAEzD,WAAK,cAAc,+BAA+B,KAAK,IAAI;AAE3D,UAAI,gBAAgB,CAAC,aAAa,eAAe;AAC/C,qBAAa,YAAY,KAAK,kBAAkB,IAAI;AAAA,MACtD;AAAA,IACF,CAAC;AAGDA,aAAAA,QAAQ,GAAG,sBAAsB,CAAC,OAAO,EAAE,KAAK,aAAa;AAC3D,UAAI,OAAO,QAAQ;AACjB,aAAK,cAAc,sBAAsB,KAAK,MAAM;AAAA,MACtD;AAAA,IACF,CAAC;AAGDA,aAAAA,QAAQ,GAAG,6BAA6B,CAAC,OAAO,SAAS;AACvD,YAAM,MAAM,KAAK;AACjB,UAAI,CAAC,IAAK;AAEV,YAAM,eAAeT,SAAAA,cAAc,gBAAgB,MAAM,MAAM;AAC/D,UAAI,cAAc;AAChB,aAAK,cAAc,mBAAmB,KAAK,YAAY;AAEvD,cAAM,mBAAmB,KAAK,cAAc,oBAAoB,GAAG;AACnE,YAAI,kBAAkB;AACpB,uBAAa,YAAY,KAAK,kBAAkB;AAAA,YAC9C;AAAA,YACA,QAAQ;AAAA,UAAA,CACT;AACD,eAAK,cAAc,uBAAuB,GAAG;AAC7C;AAAA,QACF;AAEA,cAAM,cACJ,KAAK,cAAc,6BAA6B,GAAG;AACrD,YAAI,aAAa;AACf,uBAAa,YAAY,KAAK,kBAAkB,WAAW;AAC3D,eAAK,cAAc,gCAAgC,GAAG;AAAA,QACxD;AAAA,MACF;AAAA,IACF,CAAC;AAGDS,aAAAA,QAAQ,GAAG,sBAAsB,OAAO,OAAO,SAAS;AACtD,YAAM,MAAM,KAAK;AACjB,UAAI,CAAC,KAAK;AACR,cAAM,OAAO,KAAK,uBAAuB;AAAA,UACvC,MAAM;AAAA,UACN,KAAK;AAAA,QAAA,CACN;AACD;AAAA,MACF;AAEA,YAAM,eAAeT,SAAAA,cAAc,gBAAgB,MAAM,MAAM;AAC/D,UAAI,cAAc;AAChB,aAAK,cAAc,mBAAmB,KAAK,YAAY;AAAA,MACzD;AAEA,YAAM,aAAa,KAAK,cAAc,cAAA;AACtC,UAAI,CAAC,cAAc,WAAW,eAAe;AAC3C,cAAM,OAAO,KAAK,uBAAuB;AAAA,UACvC,MAAM;AAAA,UACN,KAAK;AAAA,QAAA,CACN;AACD;AAAA,MACF;AAEA,UAAI;AACF,cAAM,SAAS,MAAM,WAAW,YAAY,kBAAkB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,sDAShB,GAAG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,SAShD;AAED,cAAM,OAAO,KAAK,uBAAuB,MAAM;AAAA,MACjD,SAAS,OAAY;AACnB,cAAM,OAAO,KAAK,uBAAuB;AAAA,UACvC,MAAM;AAAA,UACN,KAAK,cAAc,MAAM,WAAW;AAAA,QAAA,CACrC;AAAA,MACH;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA,EAKQ,sBAAsB;AAE5BS,aAAAA,QAAQ,OAAO,YAAY,CAAC,GAAG,QAAQ;AACrC,aAAO,KAAK,cAAc,kBAAkB,GAAG;AAAA,IACjD,CAAC;AAGDA,aAAAA,QAAQ,GAAG,iCAAiC,CAAC,OAAO,OAAY,CAAA,MAAO;AACrE,WAAK,cAAc,iBAAiB,KAAK,WAAW;AAEpD,UAAI,KAAK,gBAAgB;AACvB,cAAM,eAAe,MAAM;;AACzB,gBAAMC,OAAM,KAAK,cAAc,cAAA;AAC/B,eAAI,UAAK,gBAAL,mBAAkB,SAAS,4BAA4B;AACzDA,yCAAK,YAAY,KAAK,uBAAuB,KAAK;AAAA,UACpD,OAAO;AACLA,yCAAK,YAAY;AAAA,cACf;AAAA,cACA,KAAK;AAAA;AAAA,UAET;AAEA,cAAI,KAAK,gBAAgB;AACvBA,yCAAK,YAAY,KAAK,wBAAwB,KAAK;AAAA,UACrD;AAAA,QACF;AAEA,cAAM,MAAM,KAAK,cAAc,cAAA;AAC/B,mCAAK,YAAY,KAAK,mBAAmB;AACzC,mCAAK,YAAY,KAAK,aAAa;AACnC,mBAAW,cAAc,GAAI;AAAA,MAC/B;AAAA,IACF,CAAC;AAGDD,qBAAQ,GAAG,mBAAmB,MAAM;AAClC,WAAK,cAAc,eAAA;AAAA,IACrB,CAAC;AAEDA,qBAAQ,GAAG,mBAAmB,MAAM;AAClC,WAAK,cAAc,qBAAA;AAAA,IACrB,CAAC;AAEDA,qBAAQ,GAAG,gBAAgB,MAAM;AAC/B,WAAK,cAAc,YAAA;AAAA,IACrB,CAAC;AAGDA,aAAAA,QAAQ,GAAG,2BAA2B,CAAC,UAAU;AAC/C,YAAM,gBAAgBT,SAAAA,cAAc,gBAAgB,MAAM,MAAM;AAChE,UAAI,eAAe;AACjB,aAAK,cAAc,eAAe,aAAa;AAAA,MACjD;AAAA,IACF,CAAC;AAEDS,aAAAA,QAAQ,GAAG,2BAA2B,CAAC,UAAU;AAC/C,YAAM,gBAAgBT,SAAAA,cAAc,gBAAgB,MAAM,MAAM;AAChE,UAAI,eAAe;AACjB,aAAK,cAAc,qBAAqB,aAAa;AAAA,MACvD;AAAA,IACF,CAAC;AAEDS,aAAAA,QAAQ,GAAG,wBAAwB,CAAC,UAAU;AAC5C,YAAM,gBAAgBT,SAAAA,cAAc,gBAAgB,MAAM,MAAM;AAChE,UAAI,eAAe;AACjB,aAAK,cAAc,YAAY,aAAa;AAAA,MAC9C;AAAA,IACF,CAAC;AAGDS,aAAAA,QAAQ,GAAG,2BAA2B,CAAC,OAAO,aAAqB;AACjE,YAAM,MAAM,KAAK,cAAc,cAAA;AAC/B,UAAI,OAAO,CAAC,IAAI,eAAe;AAC7B,YAAI,YAAY,KAAK,2BAA2B,QAAQ;AAAA,MAC1D;AAAA,IACF,CAAC;AAEDA,aAAAA,QAAQ,GAAG,uBAAuB,CAAC,OAAO,aAAqB;AAC7D,YAAM,MAAM,KAAK,cAAc,cAAA;AAC/B,UAAI,OAAO,CAAC,IAAI,eAAe;AAC7B,YAAI,YAAY,KAAK,uBAAuB,QAAQ;AAAA,MACtD;AAAA,IACF,CAAC;AAGDA,qBAAQ,GAAG,qBAAqB,MAAM;AACpC,YAAM,aAAa,KAAK,cAAc,cAAA;AACtC,UAAI,cAAc,CAAC,WAAW,iBAAiB,WAAW,eAAe;AACvE,mBAAW,WAAA;AAAA,MACb;AAAA,IACF,CAAC;AAGDA,aAAAA,QAAQ;AAAA,MACN;AAAA,MACA,CAAC,OAAO,aAAuC;AAC7C,cAAM,aAAa,KAAK,cAAc,cAAA;AACtC,YAAI,cAAc,CAAC,WAAW,eAAe;AAC3C,qBAAW,YAAY,SAAS,GAAG,SAAS,CAAC;AAAA,QAC/C;AAAA,MACF;AAAA,IAAA;AAIFA,qBAAQ,OAAO,uBAAuB,MAAM;AAC1C,YAAM,aAAa,KAAK,cAAc,cAAA;AACtC,aAAO,cAAc,CAAC,WAAW,gBAC7B,WAAW,gBACX;AAAA,IACN,CAAC;AAGDA,aAAAA,QAAQ,GAAG,cAAc,CAAC,UAAU;AAClC,UAAI;AACF,cAAM,SAAST,SAAAA,cAAc,gBAAgB,MAAM,MAAM;AACzD,YAAI,UAAU,CAAC,OAAO,eAAe;AACnC,gBAAM,aAAa,OAAO,sBAAA;AAG1B,gBAAM,WACJ,QAAQ,aAAa,UACjB,WAAW,aAAa,CAAC,IACzB;AAEN,oBAAU,UAAU,QAAQ;AAAA,QAC9B;AAAA,MACF,SAAS,OAAO;AACd,gBAAQ,MAAM,yBAAyB,KAAK;AAAA,MAC9C;AAAA,IACF,CAAC;AAGDS,qBAAQ,GAAG,YAAY,MAAM;AAC3Bf,eAAAA,IAAI,KAAA;AAAA,IACN,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA,EAKQ,0BAA0B;AAEhCe,aAAAA,QAAQ,OAAO,oBAAoB,OAAO,GAAG,YAAoB;AAC/D,aAAO,MAAM,KAAK,kBAAkB,cAAc,OAAO;AAAA,IAC3D,CAAC;AAGDA,aAAAA,QAAQ,OAAO,sBAAsB,OAAO,GAAG,eAAuB;AACpE,aAAO,MAAM,KAAK,kBAAkB,gBAAgB,UAAU;AAAA,IAChE,CAAC;AAGDA,aAAAA,QAAQ,OAAO,iBAAiB,OAAO,GAAG,aAAqB;AAC7D,aAAO,MAAM,KAAK,kBAAkB,WAAW,QAAQ;AAAA,IACzD,CAAC;AAGDA,aAAAA,QAAQ,OAAO,iBAAiB,OAAO,GAAG,eAAuB;AAC/D,aAAO,MAAM,KAAK,kBAAkB,WAAW,UAAU;AAAA,IAC3D,CAAC;AAGDA,aAAAA,QAAQ,OAAO,aAAa,OAAO,GAAG,SAAiB,YAAoB;AACzE,aAAO,MAAM,KAAK,kBAAkB,OAAO,SAAS,OAAO;AAAA,IAC7D,CAAC;AAGDA,aAAAA,QAAQ,OAAO,aAAa,OAAO,GAAG,aAAqB;AACzD,aAAO,MAAM,KAAK,kBAAkB,OAAO,QAAQ;AAAA,IACrD,CAAC;AAGDA,aAAAA,QAAQ,OAAO,eAAe,OAAO,GAAG,aAAqB;AAC3D,aAAO,MAAM,KAAK,kBAAkB,SAAS,QAAQ;AAAA,IACvD,CAAC;AAGDA,aAAAA,QAAQ,OAAO,uBAAuB,OAAO,GAAG,aAAqB;AACnE,aAAO,MAAM,KAAK,kBAAkB,iBAAiB,QAAQ;AAAA,IAC/D,CAAC;AAGDA,aAAAA,QAAQ,OAAO,qBAAqB,OAAO,GAAG,aAAqB;AACjE,aAAO,MAAM,KAAK,kBAAkB,eAAe,QAAQ;AAAA,IAC7D,CAAC;AAEDA,aAAAA,QAAQ;AAAA,MACN;AAAA,MACA,OAAO,GAAG,UAAkB,YAAoB;AAC9C,eAAO,MAAM,KAAK,kBAAkB,UAAU,UAAU,OAAO;AAAA,MACjE;AAAA,IAAA;AAIFA,aAAAA,QAAQ;AAAA,MACN;AAAA,MACA,OAAO,GAAG,UAAkB,gBAA6B;AACvD,eAAO,MAAM,KAAK,kBAAkB;AAAA,UAClC;AAAA,UACA;AAAA,QAAA;AAAA,MAEJ;AAAA,IAAA;AAIFA,aAAAA,QAAQ;AAAA,MACN;AAAA,MACA,OAAO,GAAG,UAAkB,gBAAwB;AAClD,eAAO,MAAM,KAAK,kBAAkB;AAAA,UAClC;AAAA,UACA;AAAA,QAAA;AAAA,MAEJ;AAAA,IAAA;AAIFA,aAAAA,QAAQ,OAAO,mBAAmB,OAAO,GAAG,aAAqB;AAC/D,aAAO,MAAM,KAAK,kBAAkB,aAAa,QAAQ;AAAA,IAC3D,CAAC;AAGDA,qBAAQ,OAAO,mBAAmB,YAAY;AAC5C,aAAO,MAAM,KAAK,kBAAkB,eAAA;AAAA,IACtC,CAAC;AAEDA,aAAAA,QAAQ,OAAO,oBAAoB,OAAO,GAAG,UAAiB;AAC5D,aAAO,MAAM,KAAK,kBAAkB,gBAAgB,KAAK;AAAA,IAC3D,CAAC;AAGDA,aAAAA,QAAQ,OAAO,uBAAuB,OAAO,GAAG,YAAoB;AAClE,aAAO,MAAM,KAAK,kBAAkB,iBAAiB,OAAO;AAAA,IAC9D,CAAC;AAGDA,aAAAA,QAAQ,OAAO,6BAA6B,OAAO,GAAG,YAAoB;AACxE,aAAO,MAAM,KAAK,kBAAkB,uBAAuB,OAAO;AAAA,IACpE,CAAC;AAGDA,aAAAA,QAAQ,OAAO,iBAAiB,OAAO,GAAG,aAAqB;AAC7D,aAAO,MAAM,KAAK,kBAAkB,WAAW,QAAQ;AAAA,IACzD,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA,EAKQ,sBAAsB;AAE5BA,qBAAQ,OAAO,wBAAwB,YAAY;AACjD,aAAO,MAAM,KAAK,cAAc,cAAA;AAAA,IAClC,CAAC;AAGDA,qBAAQ,OAAO,mBAAmB,YAAY;AAC5C,aAAO,MAAM,KAAK,cAAc,SAAA;AAAA,IAClC,CAAC;AAGDA,aAAAA,QAAQ,OAAO,mBAAmB,OAAO,GAAG,YAAiB;AAC3D,aAAO,MAAM,KAAK,cAAc,SAAS,OAAO;AAAA,IAClD,CAAC;AAGDA,aAAAA,QAAQ,OAAO,kBAAkB,OAAO,GAAG,gBAAqB;AAC9D,aAAO,MAAM,KAAK,cAAc,cAAc,WAAW;AAAA,IAC3D,CAAC;AAEDA,qBAAQ,OAAO,kBAAkB,YAAY;AAC3C,aAAO,MAAM,KAAK,cAAc,cAAA;AAAA,IAClC,CAAC;AAGDA,aAAAA,QAAQ,OAAO,wBAAwB,OAAO,GAAG,YAAiB;AAChE,aAAO,MAAM,KAAK,cAAc,cAAc,OAAO;AAAA,IACvD,CAAC;AAGDA,aAAAA,QAAQ,OAAO,yBAAyB,OAAO,OAAO,YAAiB;AACrE,YAAM,SAAST,SAAAA,cAAc,gBAAgB,MAAM,MAAM;AACzD,UAAI,QAAQ;AACV,eAAO,MAAM,KAAK,cAAc,eAAe,QAAQ,OAAO;AAAA,MAChE;AACA,aAAO,EAAE,UAAU,MAAM,WAAW,CAAA,EAAC;AAAA,IACvC,CAAC;AAEDS,aAAAA,QAAQ,OAAO,yBAAyB,OAAO,OAAO,YAAiB;AACrE,YAAM,SAAST,SAAAA,cAAc,gBAAgB,MAAM,MAAM;AACzD,UAAI,QAAQ;AACV,eAAO,MAAM,KAAK,cAAc,eAAe,QAAQ,OAAO;AAAA,MAChE;AACA,aAAO,EAAE,UAAU,MAAM,UAAU,GAAA;AAAA,IACrC,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA,EAKQ,oBAAoB;AAE1BS,aAAAA,QAAQ,GAAG,kBAAkB,CAAC,UAAU;AACtC,YAAM,MAAMT,SAAAA,cAAc,gBAAgB,MAAM,MAAM;AACtD,UAAI,OAAO,CAAC,IAAI,eAAe;AAC7B,aAAK,YAAY,eAAe,GAAG;AAAA,MACrC;AAAA,IACF,CAAC;AAEDS,aAAAA,QAAQ,GAAG,qBAAqB,CAAC,UAAU;AACzC,YAAM,MAAMT,SAAAA,cAAc,gBAAgB,MAAM,MAAM;AACtD,UAAI,OAAO,CAAC,IAAI,eAAe;AAC7B,aAAK,YAAY,kBAAkB,GAAG;AAAA,MACxC;AAAA,IACF,CAAC;AAEDS,aAAAA,QAAQ,GAAG,kBAAkB,CAAC,UAAU;AACtC,YAAM,MAAMT,SAAAA,cAAc,gBAAgB,MAAM,MAAM;AACtD,UAAI,OAAO,CAAC,IAAI,eAAe;AAC7B,aAAK,YAAY,eAAe,GAAG;AAAA,MACrC;AAAA,IACF,CAAC;AAEDS,aAAAA,QAAQ,GAAG,kBAAkB,CAAC,UAAU;AACtC,YAAM,MAAMT,SAAAA,cAAc,gBAAgB,MAAM,MAAM;AACtD,UAAI,OAAO,CAAC,IAAI,eAAe;AAC7B,aAAK,YAAY,eAAe,GAAG;AAAA,MACrC;AAAA,IACF,CAAC;AAEDS,aAAAA,QAAQ,GAAG,iBAAiB,CAAC,UAAU;AACrC,YAAM,MAAMT,SAAAA,cAAc,gBAAgB,MAAM,MAAM;AACtD,UAAI,OAAO,CAAC,IAAI,eAAe;AAC7B,aAAK,YAAY,cAAc,GAAG;AAAA,MACpC;AAAA,IACF,CAAC;AAEDS,aAAAA,QAAQ,GAAG,mBAAmB,CAAC,UAAU;AACvC,YAAM,MAAMT,SAAAA,cAAc,gBAAgB,MAAM,MAAM;AACtD,UAAI,OAAO,CAAC,IAAI,eAAe;AAC7B,aAAK,YAAY,gBAAgB,GAAG;AAAA,MACtC;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA,EAKQ,uBAAuB;AAE7BS,qBAAQ,OAAO,aAAa,YAAY;AACtC,aAAOE,eAAA;AAAA,IACT,CAAC;AAGDF,aAAAA,QAAQ,OAAO,aAAa,OAAO,MAAM,UAAoB;AAC3D,aAAOd,UAAAA,KAAK,GAAG,KAAK;AAAA,IACtB,CAAC;AAGDc,aAAAA,QAAQ,OAAO,gBAAgB,OAAO,GAAG,aAAqB;AAC5D,aAAOV,UAAAA,QAAQ,QAAQ;AAAA,IACzB,CAAC;AAAA,EACH;AACF;ACjgBA,MAAMa,eAAaC,SAAAA,+PAA6B;AAChD,MAAMC,cAAYf,UAAAA,QAAQa,YAAU;AACpC,QAAA,IAAY,gBAAgBjB,eAAKmB,aAAW,IAAI;AAChD,QAAA,IAAY,OAAOnB,UAAAA,KAAK,QAAA,IAAY,eAAe,SAAS;AAC5D,QAAA,IAAY,SAAS,YAAY,sBAC7BA,UAAAA,KAAK,YAAY,eAAe,WAAW,IAC3C,QAAA,IAAY;AAGhB,MAAM,QAAQ,QAAA,IAAY,UAAU,MAAM;AAG1C,IAAIoB,QAAAA,UAAU,WAAW,KAAK,gBAAO,4BAAA;AAGrC,IAAI,QAAQ,aAAa,sBAAa,kBAAkBrB,SAAAA,IAAI,SAAS;AAErE,IAAI,CAACA,SAAAA,IAAI,6BAA6B;AACpCA,WAAAA,IAAI,KAAA;AACJ,UAAQ,KAAK,CAAC;AAChB;AAGA,MAAM,iBAAiB,IAAI,eAAA;AAC3B,MAAM,gBAAgB,IAAI,cAAA;AAC1B,MAAM,oBAAoB,IAAI,kBAAA;AAC9B,MAAM,gBAAgB,IAAI,cAAA;AAC1B,MAAM,cAAc,IAAI,YAAY,KAAK;AACzC,MAAM,aAAa,IAAI;AAAA,EACrB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAEA,IAAI,iBAAiB;AAKrB,eAAe,6BAA6B;AAC1C,UAAQ,IAAI,sBAAsB;AA0ElC,UAAQ,IAAI,mDAAmD;AAC/D,QAAM,gBAAgB,cAAc,iBAAA;AACpC,iDAAe,YAAY;AAAA,IACzB;AAAA,IACA;AAAA,IACA;AAAA;AAEF,QAAM,cAAc,iBAAiB,UAAU;AAE/C,QAAM,aAAa,cAAc,cAAA;AACjC,MAAI,YAAY;AACd,eAAW,KAAK,iBAAiB,MAAM;AACrC,iBAAW,MAAM;AACf,sBAAc,mBAAA;AACd,sBAAc,eAAA;AAAA,MAChB,GAAG,GAAG;AAAA,IACR,CAAC;AAAA,EACH;AACF;AAKA,SAAS,gBAAgB;AACvB,QAAM,gBAAgB,cAAc,oBAAA;AAGpC,gBAAc,YAAY,GAAG,mBAAmB,MAAM;AAEpD,QAAI;AACF,YAAM,kBAAkBC,UAAAA,KAAKD,SAAAA,IAAI,WAAA,GAAc,cAAc;AAC7D,YAAM,MAAM,KAAK,MAAMU,QAAAA,aAAa,iBAAiB,OAAO,CAAC;AAC7D,qDAAe,YAAY,KAAK,eAAe,IAAI;AAAA,IACrD,SAAS,OAAO;AACd,cAAQ,MAAM,wCAAwC,KAAK;AAAA,IAC7D;AAEA,mDAAe;AACf,+BAAA;AAAA,EACF,CAAC;AAGD,cAAY,WAAA;AACd;AAGAV,SAAAA,IAAI,UAAA,EAAY,KAAK,MAAM;AACzB,gBAAA;AACF,CAAC;AAEDA,SAAAA,IAAI,GAAG,qBAAqB,MAAM;AAChC,MAAI,QAAQ,aAAa,UAAU;AACjCA,aAAAA,IAAI,KAAA;AAAA,EACN;AACF,CAAC;AAGDA,SAAAA,IAAI,GAAG,eAAe,OAAO,UAAU;AACrC,QAAM,iBAAiB;AACvB,MAAI,CAAC,kBAAkB,mBAAmB,QAAQ;AAChD,UAAM,eAAA;AACN,qBAAiB;AACjB,mBAAe,gBAAgB,IAAI;AAEnC,YAAQ,IAAI,sDAAsD;AAElE,UAAM,uBAAuB,OAC3B,eACA,MACA,YACG;AACH,aAAO,QAAQ,KAAK;AAAA,QAClB,cAAA;AAAA,QACA,IAAI,QAAc,CAAC,YAAY;AAC7B,qBAAW,MAAM;AACf,oBAAQ,KAAK,GAAG,IAAI,8BAA8B,OAAO,IAAI;AAC7D,oBAAA;AAAA,UACF,GAAG,OAAO;AAAA,QACZ,CAAC;AAAA,MAAA,CACF;AAAA,IACH;AAEA,QAAI;AACF,YAAM,QAAQ,IAAI;AAAA,QAChB;AAAA,UACE,YAAY,MAAM,eAAe,8BAAA;AAAA,UACjC;AAAA,UACA;AAAA,QAAA;AAAA,QAEF;AAAA,UACE,YAAY,MAAM,eAAe,6BAAA;AAAA,UACjC;AAAA,UACA;AAAA,QAAA;AAAA,MACF,CACD;AACD,cAAQ,IAAI,uCAAuC;AAAA,IACrD,SAAS,OAAO;AACd,cAAQ,MAAM,qCAAqC,KAAK;AAAA,IAC1D;AAGA,eAAW,MAAM;AACf,cAAQ,IAAI,4CAA4C;AACxDA,eAAAA,IAAI,KAAK,CAAC;AAAA,IACZ,GAAG,GAAI;AAAA,EACT;AACF,CAAC;AAGD,QAAQ,GAAG,QAAQ,MAAM;AAMzB,CAAC;AAGD,QAAQ,GAAG,qBAAqB,OAAO,UAAU;AAC/C,UAAQ,MAAM,uBAAuB,KAAK;AAC1C,mBAAiB;AACjB,iBAAe,gBAAgB,IAAI;AAEnC,MAAI;AACF,UAAM,QAAQ,KAAK;AAAA,MACjB,QAAQ,IAAI;AAAA,QACV,eAAe,6BAAA;AAAA,QACf,eAAe,8BAAA;AAAA,MAA8B,CAC9C;AAAA,MACD,IAAI,QAAQ,CAAC,YAAY,WAAW,SAAS,GAAI,CAAC;AAAA,IAAA,CACnD;AACD,YAAQ,IAAI,6BAA6B;AAAA,EAC3C,SAAS,GAAG;AACV,YAAQ,MAAM,4BAA4B,CAAC;AAAA,EAC7C;AAEAA,WAAAA,IAAI,KAAK,CAAC;AACZ,CAAC;AAGD,QAAQ,GAAG,sBAAsB,CAAC,WAAW;AAC3C,UAAQ,MAAM,gCAAgC,MAAM;AACtD,CAAC;AAEDA,SAAAA,IAAI,GAAG,mBAAmB,MAAM;AAC9B,QAAM,aAAa,cAAc,cAAA;AACjC,MAAI,cAAc,CAAC,WAAW,eAAe;AAC3C,QAAI,WAAW,cAAe,YAAW,QAAA;AACzC,eAAW,MAAA;AAAA,EACb;AACF,CAAC;AAEDA,SAAAA,IAAI,GAAG,YAAY,MAAM;AACvB,QAAM,aAAa,cAAc,cAAA;AACjC,MAAI,CAAC,cAAc,WAAW,eAAe;AAC3C,kBAAc,iBAAiB,UAAU;AAAA,EAC3C,OAAO;AACL,QAAI,WAAW,cAAe,YAAW,QAAA;AACzC,eAAW,MAAA;AAAA,EACb;AACF,CAAC;;;;;;;"}