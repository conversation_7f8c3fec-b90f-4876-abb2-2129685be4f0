<template>
  <div class="workspace-container">
    <!-- Workspace header -->
    <div class="workspace-header">
      <div class="breadcrumb-container">
        <el-breadcrumb separator="/">
          <el-breadcrumb-item>
            {{ workspacePath ? '工作区' : '文件' }}
          </el-breadcrumb-item>
          <el-breadcrumb-item v-if="workspacePath">
            {{ getWorkspaceName(workspacePath) }}
          </el-breadcrumb-item>
          <el-breadcrumb-item v-if="currentFilePath">
            {{ getFileName(currentFilePath) }}
          </el-breadcrumb-item>
        </el-breadcrumb>
      </div>
      
      <!-- 修改工作区头部按钮 -->
      <div class="workspace-actions">
        <button
          class="workspace-action-button"
          @click="openWorkspaceDirectory"
          :title="workspacePath ? '打开工作区目录' : '打开包含此文件的目录'"
        >
          <FolderColoredIcon class="button-svg-icon" />
          <span>打开目录</span>
        </button>
        <button
          v-if="workspacePath"
          class="workspace-action-button"
          @click="createNewFile"
          :disabled="!workspacePath"
          title="新建文件"
        >
          <FileColoredIcon class="button-svg-icon" />
          <span>新建文件</span>
        </button>
      </div>
    </div>

    <!-- Main content area -->
    <div class="workspace-content">
      <div v-if="!workspacePath && !currentFilePath" class="empty-workspace">
        <div class="empty-content">
          <el-icon :size="64" color="#909399">
            <Folder />
          </el-icon>
          <h3>欢迎使用工作区</h3>
          <p>选择一个目录开始工作</p>
          <el-button type="primary" @click="openWorkspaceDirectory">
            选择工作区目录
          </el-button>
        </div>
      </div>

      <div v-else-if="currentFilePath" class="file-editor">
        <!-- File editor placeholder -->
        <!-- 修改编辑器头部布局和样式 -->
        <div class="editor-header">
          <div class="file-title">
            <el-icon :size="20">
              <Document />
            </el-icon>
            <span>{{ getFileName(currentFilePath) }}</span>
            <span v-if="isFileModified" class="modified-indicator">●</span>
          </div>
          <div class="file-path-actions">
            <div class="file-path">{{ currentFilePath }}</div>
            <div class="editor-actions">
              <button 
                class="action-button save-button" 
                @click="saveFile" 
                :disabled="!isFileModified"
                :class="{ 'disabled': !isFileModified }"
              >
                保存
              </button>
              <button 
                class="action-button close-button" 
                @click="closeFile"
              >
                关闭
              </button>
            </div>
          </div>
        </div>
        
        <div class="editor-content">
          <div v-if="fileError" class="file-error">
            <div class="error-icon">
              <UnsupportedIcon class="unsupported-icon" />
            </div>
            <p class="error-title">{{ fileError.title }}</p>
            <p class="error-message">{{ fileError.message }}</p>
            <div v-if="fileError.suggestions" class="error-suggestions">
              <p>建议您</p>
              <ul>
                <li v-for="suggestion in fileError.suggestions" :key="suggestion">
                  {{ suggestion }}
                </li>
              </ul>
            </div>
          </div>
          <!-- 图像显示 -->
          <div v-else-if="currentFileInfo?.category === 'image' && imageData" class="image-viewer">
            <div class="image-container">
              <img :src="imageData" :alt="currentFilePath" class="preview-image" />
            </div>
            <div class="image-info">
              <p><strong>文件名：</strong>{{ getFileName(currentFilePath) }}</p>
              <p><strong>文件类型：</strong>{{ currentFileInfo.type.toUpperCase() }}</p>
              <p><strong>文件路径：</strong>{{ currentFilePath }}</p>
            </div>
          </div>
          <!-- PDF 阅读器 -->
          <div v-else-if="currentFileInfo?.category === 'pdf'" class="pdf-viewer">
            <div class="pdf-toolbar">
              <button @click="prevPage" :disabled="pdfPageNum <= 1">上一页</button>
              <span>第 {{ pdfPageNum }} / {{ pdfTotalPages }} 页</span>
              <button @click="nextPage" :disabled="pdfPageNum >= pdfTotalPages">下一页</button>
            </div>
            <div class="pdf-canvas-container">
              <canvas ref="pdfCanvas" class="pdf-canvas"></canvas>
            </div>
          </div>
          <!-- 文本编辑器 (作为后备) -->
          <el-input
            v-else
            v-model="fileContent"
            type="textarea"
            :rows="20"
            :placeholder="getPlaceholderText()"
            :readonly="isReadOnly"
            @input="handleFileContentChange"
          />
        </div>
      </div>

      <div v-else-if="workspacePath && !currentFilePath" class="workspace-ready-state">
        <div class="ready-content">
          <ReadyIcon class="ready-icon" />
          <h3 class="ready-title">工作区已就绪</h3>
          <p class="ready-subtitle">从左侧文件树中选择文件开始</p>
          <p class="ready-subtitle">你也可以</p>
          <div class="ready-actions">
            <el-button type="primary" :icon="FolderOpened" @click="openWorkspaceDirectory">
              打开目录
            </el-button>
            <el-button type="primary" :icon="DocumentAdd" @click="createNewFile" :disabled="!workspacePath">
              添加文件
            </el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount, watch, nextTick, markRaw } from "vue";
import { useRoute, useRouter } from "vue-router";
import { ElMessage, ElMessageBox, ElLoading } from "element-plus";
import {
  Folder,
  FolderOpened,
  Document,
  DocumentAdd
} from "@element-plus/icons-vue";
// 导入SVG图标
import ReadyIcon from "@/assets/svg/ready.svg?component";
import FileColoredIcon from "@/assets/svg/file_colored.svg?component";
import FolderColoredIcon from "@/assets/svg/folder_colored.svg?component";
import UnsupportedIcon from "@/assets/svg/unsupported.svg?component";
import { useWorkspaceStoreHook } from "@/store/modules/workspace";
import { debounce } from "lodash-es";
// 导入 multi-tags store
import { useMultiTagsStoreHook } from "@/store/modules/multiTags";
import { usePermissionStoreHook } from "@/store/modules/permission";

import "core-js/proposals/promise-with-resolvers"
import * as pdfjsLib from "pdfjs-dist";
import pdfjsWorker from "pdfjs-dist/build/pdf.worker.mjs?url";

const route = useRoute();
const router = useRouter();
const workspaceStore = useWorkspaceStoreHook();
const multiTagsStore = useMultiTagsStoreHook();

const fileContent = ref<string>("");
const originalFileContent = ref<string>("");

// 设置 PDF.js 的 workerSrc
pdfjsLib.GlobalWorkerOptions.workerSrc = pdfjsWorker;

// File error state for unsupported files
const fileError = ref<{
  title: string;
  message: string;
  suggestions?: string[];
} | null>(null);

// File type info
const currentFileInfo = ref<{
  type: string;
  category: string;
  supported: boolean;
} | null>(null);

// Image data for image files
const imageData = ref<string | null>(null);

// PDF viewer state
const pdfDoc = ref(null);
const pdfPageNum = ref(1);
const pdfTotalPages = ref(0);
const pdfCanvas = ref<HTMLCanvasElement | null>(null);

// Use computed properties from store
const workspacePath = computed(() => workspaceStore.getCurrentWorkspacePath);
const currentFilePath = computed(() => workspaceStore.getCurrentFilePath);
const isFileModified = computed(() =>
  currentFilePath.value ? workspaceStore.isFileModified(currentFilePath.value) : false
);

// Computed properties for UI
const isReadOnly = computed(() => {
  return currentFileInfo.value?.category === 'office' ||
         currentFileInfo.value?.category === 'pdf' ||
         !currentFileInfo.value?.supported;
});

const getPlaceholderText = () => {
  if (!currentFilePath.value) return "请选择一个文件...";
  if (fileError.value) return "";
  if (currentFileInfo.value?.category === 'office') {
    return "Office 文档内容（只读）";
  }
  if (currentFileInfo.value?.category === 'pdf') {
    return "PDF 文档内容（只读）";
  }
  return "文件内容...";
};

// Get file name from path
const getFileName = (filePath: string) => {
  if (!filePath) return '';
  return filePath.split(/[/\\]/).pop() || '';
};

// Get suggestions for unsupported file types
const getSuggestionsForFileType = (fileInfo: { type: string; category: string }) => {
  switch (fileInfo.category) {
    case 'image':
      return [
        "使用系统默认图片查看器打开",
        "使用专业图片编辑软件（如 Photoshop、GIMP）",
        "在浏览器中打开查看"
      ];
    case 'archive':
      return [
        "使用解压缩软件（如 WinRAR、7-Zip）解压",
        "解压后查看内部文件",
        "检查压缩包是否损坏"
      ];
    case 'media':
      return [
        "使用媒体播放器打开（如 VLC、Windows Media Player）",
        "使用专业音视频编辑软件",
        "检查文件格式是否受支持"
      ];
    case 'office':
      return [
        "使用 Microsoft Office 或 WPS Office 打开",
        "转换为纯文本格式后重新打开",
        "使用在线文档查看器"
      ];
    default:
      return [
        "检查文件扩展名是否正确",
        "尝试使用其他应用程序打开",
      ];
  }
};

// Computed properties
const getWorkspaceName = (path: string) => {
  return path.split(/[/\\]/).pop() || path;
};

// Watch for route changes
watch(() => route.params.workspacePath, (newPath) => {
  if (newPath && typeof newPath === 'string') {
    const decodedPath = decodeURIComponent(newPath);
    if (decodedPath !== workspaceStore.getCurrentWorkspacePath) {
      workspaceStore.setWorkspacePath(decodedPath);
    }
  }
}, { immediate: true });

// Event handlers
const handleCustomFileSelection = (event: CustomEvent) => {
  console.log('Received workspace-open-file custom event:', event.detail.filePath);
  switchToFile(event.detail.filePath);
};

const handleIpcFileSelection = (_, filePath: string) => {
  console.log('Received workspace-file-selected IPC event:', filePath);
  switchToFile(filePath);
};

// Track the last requested file path to prevent redundant operations
let lastRequestedFilePath = '';
let fileOpenInProgress = false;

// Enhanced file opening function with immediate cancellation of previous operations
const switchToFile = async (filePath: string) => {
  // If same file or operation in progress for this file, do nothing
  if (lastRequestedFilePath === filePath) {
    return;
  }
  
  // Cancel any pending debounced operations
  debouncedOpenFile.cancel();
  
  // Update the last requested file path
  lastRequestedFilePath = filePath;
  
  // If we're already opening a file, use debounce to queue this one
  if (fileOpenInProgress) {
    debouncedOpenFile(filePath);
    return;
  }
  
  // Otherwise open immediately
  fileOpenInProgress = true;
  try {
    await nextTick();
    await openFile(filePath);
  } finally {
    fileOpenInProgress = false;
  }
};

// Keep debounced version as fallback for rapid switching
const debouncedOpenFile = debounce(async (filePath: string) => {
  if (lastRequestedFilePath === filePath && !fileOpenInProgress) {
    fileOpenInProgress = true;
    try {
      await openFile(filePath);
    } finally {
      fileOpenInProgress = false;
    }
  }
}, 100); // Shorter debounce time

// Watch for changes in current file path from store
watch(currentFilePath, async (newFilePath, oldFilePath) => {
  console.log('Current file path changed:', { newFilePath, oldFilePath });
  if (newFilePath && newFilePath !== oldFilePath) {
    console.log('Opening file due to store change:', newFilePath);
    // Use optimized switching function
    switchToFile(newFilePath);
  }
}, { immediate: true });

// Watch for route changes to handle file parameter
watch(() => route.params.filePath, (newFilePath) => {
  if (newFilePath) {
    const decodedPath = decodeURIComponent(newFilePath as string);
    console.log('Route file path changed:', decodedPath);
    if (decodedPath !== currentFilePath.value) {
      workspaceStore.setCurrentFile(decodedPath);
    }
  } else {
    // If we navigate away from a file view, clear the current file
    if (currentFilePath.value && !workspaceStore.isExcelFile(currentFilePath.value)) {
      workspaceStore.setCurrentFile("");
    }
  }
}, { immediate: true });

const handleFileContentChange = () => {
  if (currentFilePath.value) {
    const isModified = fileContent.value !== originalFileContent.value;
    if (isModified) {
      workspaceStore.markFileAsModified(currentFilePath.value);
    } else {
      // If content is back to original, mark as saved
      workspaceStore.markFileAsSaved(currentFilePath.value);
    }
  }
};

// File operations
const openWorkspaceDirectory = async () => {
  try {
    const path = await window.ipcRenderer.invoke("dialog:openDirectory");
    if (path) {
      workspaceStore.setWorkspacePath(path);
      // Navigate to workspace route with the selected path
      router.push(`/workspace/${encodeURIComponent(path)}`);
    }
  } catch (error) {
    console.error("Error opening workspace directory:", error);
    ElMessage.error("打开目录失败");
  }
};

const createNewFile = async () => {
  if (!workspacePath.value) return;
  
  try {
    const { value: fileName } = await ElMessageBox.prompt(
      "请输入文件名",
      "新建文件",
      {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        inputPattern: /^[^\\/:*?"<>|]+$/,
        inputErrorMessage: '文件名不能包含 \\ / : * ? " < > |'
      }
    );

    if (fileName && fileName.trim()) {
      const newFilePath = `${workspacePath.value}/${fileName.trim()}`;
      await window.ipcRenderer.invoke("fs:createFile", newFilePath);
      ElMessage.success("文件创建成功");
      
      // Open the newly created file
      openFile(newFilePath);
    }
  } catch (error) {
    if (error !== "cancel") {
      console.error("Error creating file:", error);
      ElMessage.error("创建文件失败");
    }
  }
};

const openFile = async (filePath: string) => {
  // 检查是否为Excel文件，如果是则直接跳转
  if (workspaceStore.isExcelFile(filePath)) {
    await router.push(`/dataManagement/imandex/${encodeURIComponent(filePath)}`);
    return;
  }

  try {
    // Use new file type detection and reading
    const result = await window.ipcRenderer.invoke("fs:readFileWithType", filePath);

    if (!result.success) {
      // Handle unsupported file types
      fileError.value = {
        title: "不支持的文件类型",
        message: result.message,
        suggestions: getSuggestionsForFileType(result.fileInfo)
      };
      currentFileInfo.value = result.fileInfo;
      fileContent.value = "";
      originalFileContent.value = "";
    } else {
      // File is supported, display content
      fileError.value = null;
      currentFileInfo.value = result.fileInfo;
      workspaceStore.setCurrentFile(filePath);
      workspaceStore.setFileContent(filePath, result.content);
      fileContent.value = result.content;
      originalFileContent.value = result.content;

      // Handle image data
      if (result.imageData) {
        imageData.value = result.imageData;
      } else {
        imageData.value = null;
      }

      // Handle PDF data
      if (result.fileInfo.category === 'pdf' && result.rawData) {
        loadPdf(result.rawData);
      } else {
        pdfDoc.value = null;
      }

      // Only clear modification status if the content matches what's stored
      const storedContent = workspaceStore.getFileContent(filePath);
      if (storedContent === result.content) {
        workspaceStore.markFileAsSaved(filePath);
      }
    }
  } catch (error) {
    console.error("Error opening file:", error);
    fileError.value = {
      title: "文件读取错误",
      message: "读取文件时发生错误，请检查文件是否存在或是否有权限访问。",
      suggestions: ["检查文件路径是否正确", "确认文件未被其他程序占用", "检查文件权限设置"]
    };
    ElMessage.error("打开文件失败");
  }
};

const saveFile = async () => {
  if (!currentFilePath.value) return;

  try {
    await window.ipcRenderer.invoke("fs:writeFile", currentFilePath.value, fileContent.value);
    workspaceStore.setFileContent(currentFilePath.value, fileContent.value);
    workspaceStore.markFileAsSaved(currentFilePath.value);
    originalFileContent.value = fileContent.value;
    ElMessage.success("文件保存成功");
  } catch (error) {
    console.error("Error saving file:", error);
    ElMessage.error("保存文件失败");
  }
};

const closeFile = () => {
  const VITE_HIDE_HOME = import.meta.env.VITE_HIDE_HOME === "true";

  // 统一使用更简洁的关闭逻辑，参考 dataImandEx
  const index = multiTagsStore.multiTags.findIndex(
    item => item.path === route.path
  );

  const handleClose = () => {
    if (index !== -1) {
      const tag = multiTagsStore.multiTags[index];
      if (tag.meta?.fixedTag) return;

      if (tag.meta?.keepAlive) {
        usePermissionStoreHook().cacheOperate({
          mode: "delete",
          name: tag.name
        });
      }

      multiTagsStore.handleTags("splice", tag.path);

      if (route.path === tag.path) {
        const latestView = multiTagsStore.multiTags.slice(-1)[0];
        if (latestView) {
          // 关键修复：当跳转目标是文件时，构造URL字符串进行导航，避免重复
          if (latestView.params?.filePath) {
            const filePath = decodeURIComponent(latestView.params.filePath as string);
            if (workspaceStore.isExcelFile(filePath)) {
              router.push(
                `/dataManagement/imandex/${encodeURIComponent(filePath)}`
              );
            } else {
              router.push(`/workspace/file/${encodeURIComponent(filePath)}`);
            }
          } else {
            // 如果目标不是文件，则清除当前文件状态并跳转
            workspaceStore.setCurrentFile("");
            router.push(latestView);
          }
        } else {
          // 所有标签页都关闭了
          if (VITE_HIDE_HOME) {
            router.push("/");
          } else {
            router.push("/welcome");
          }
          workspaceStore.setCurrentFile(""); // 确保清理文件状态
        }
      }
    }
  };

  if (isFileModified.value) {
    ElMessageBox.confirm("文件已修改，是否保存？", "确认关闭", {
      confirmButtonText: "保存并关闭",
      cancelButtonText: "不保存",
      distinguishCancelAndClose: true,
      type: "warning"
    })
      .then(() => {
        saveFile().then(() => {
          handleClose();
        });
      })
      .catch(action => {
        if (action === "cancel") {
          handleClose();
        }
      });
  } else {
    handleClose();
  }
};

// Listen for file selection from sidebar
onMounted(async () => {
  console.log('Workspace view mounted');
  console.log('Current file:', currentFilePath.value);
  console.log('Workspace path:', workspacePath.value);

  // Listen for file selection events from the workspace file tree (IPC)
  window.ipcRenderer.on('workspace-file-selected', handleIpcFileSelection);

  // Listen for custom DOM events (direct file selection)
  window.addEventListener('workspace-open-file', handleCustomFileSelection);

  // Wait for next tick to ensure all reactive state is properly initialized
  await nextTick();

  // If there's already a current file path from store, open it
  if (currentFilePath.value) {
    console.log('Opening existing file from store on mount:', currentFilePath.value);
    // Use optimized switching function
    switchToFile(currentFilePath.value);
  }
});

// Cleanup listeners
onBeforeUnmount(() => {
  window.ipcRenderer.removeAllListeners('workspace-file-selected');
  window.removeEventListener('workspace-open-file', handleCustomFileSelection);
  
  // Cancel any pending operations
  debouncedOpenFile.cancel();
  lastRequestedFilePath = '';
  fileOpenInProgress = false;
});


const loadPdf = async (data: Uint8Array) => {
  console.log("Entering loadPdf function...");
  // 1. Reset state
  pdfDoc.value = null;
  pdfTotalPages.value = 0;
  pdfPageNum.value = 1;

  // 2. Validate input
  if (!data || data.length === 0) {
    console.error("loadPdf called with empty or invalid data.");
    ElMessage.error("PDF 数据为空，无法加载。");
    return;
  }
  
  console.log(`Received PDF data (${data.length} bytes). Attempting to load...`);

  try {
    // 3. Create loading task
    const loadingTask = pdfjsLib.getDocument({ data });
    console.log("PDF.js getDocument task created. Awaiting promise to resolve...");
    
    // 4. Await document loading
    const doc = await loadingTask.promise;
    console.log("PDF.js getDocument promise resolved successfully.");
    
    // 关键：使用 markRaw 防止 Vue 对其进行响应式代理
    pdfDoc.value = markRaw(doc);

    if (!doc || doc.numPages === 0) {
      console.error("PDF document loaded but is invalid (0 pages).");
      ElMessage.error("PDF 文档加载失败或文档为空。");
      return;
    }

    // 5. Update state and prepare for rendering
    console.log(`PDF loaded successfully with ${doc.numPages} pages.`);
    pdfTotalPages.value = doc.numPages;
    
    console.log("Waiting for Vue's nextTick to ensure canvas is in the DOM...");
    await nextTick();
    
    // 6. Render the first page
    console.log("nextTick finished. Calling renderPage for page 1.");
    renderPage(pdfPageNum.value);

  } catch (error) {
    // 7. Handle any errors during the process
    console.error("An error occurred during PDF loading or parsing:", error);
    ElMessage.error(`加载 PDF 文档时出错: ${error.message || '未知错误'}`);
  }
};

const renderPage = async (num: number) => {
  if (!pdfDoc.value) {
    console.warn("renderPage called but pdfDoc is not available.");
    return;
  }

  // Ensure canvas is ready
  if (!pdfCanvas.value) {
    console.warn("renderPage called but pdfCanvas is not yet available in the DOM. Retrying after nextTick.");
    await nextTick();
    if (!pdfCanvas.value) {
      console.error("FATAL: pdfCanvas is still not available after nextTick.");
      ElMessage.error("PDF 渲染目标丢失，请尝试重新打开文件。");
      return;
    }
  }

  try {
    const page = await pdfDoc.value.getPage(num);
    const viewport = page.getViewport({ scale: 1.5 });
    const canvas = pdfCanvas.value;
    const context = canvas.getContext("2d");

    canvas.height = viewport.height;
    canvas.width = viewport.width;

    const renderContext = {
      canvasContext: context,
      viewport: viewport
    };

    await page.render(renderContext).promise;
  } catch (error) {
    console.error(`Failed to render PDF page ${num}:`, error);
    ElMessage.error(`渲染 PDF 第 ${num} 页时出错。`);
  }
};

const prevPage = () => {
  if (pdfPageNum.value > 1) {
    pdfPageNum.value--;
    renderPage(pdfPageNum.value);
  }
};

const nextPage = () => {
  if (pdfPageNum.value < pdfTotalPages.value) {
    pdfPageNum.value++;
    renderPage(pdfPageNum.value);
  }
};

// Expose methods for external use
defineExpose({
  openFile,
  openWorkspaceDirectory
});
</script>

<style scoped>
.workspace-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: transparent; /* 背景透明 */
}

/* 深色模式适配 */
html.dark .workspace-container {
  background: var(--el-bg-color);
}

.workspace-header {
  padding: 16px 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: transparent; /* 背景透明 */
}

/* 深色模式适配 */
html.dark .workspace-header {
  background: var(--el-bg-color-page);
  border-bottom: 1px solid var(--pure-border-color);
}

.breadcrumb-container {
  flex: 1;
}

/* 工作区头部按钮样式 */
.workspace-actions {
  display: flex;
  gap: 12px;
}

.workspace-action-button {
  width: 118px;
  height: 32px;
  border-radius: 4px;
  background: #FAFAFA;
  box-sizing: border-box;
  border: 1px solid #005DFF;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  cursor: pointer;
  color: #005DFF;
  font-size: 14px;
  transition: all 0.3s;
}

.workspace-action-button:hover {
  background: rgba(0, 93, 255, 0.05);
}

.workspace-action-button:active {
  background: rgba(0, 93, 255, 0.1);
}

.workspace-action-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  border-color: #C0C4CC;
  color: #C0C4CC;
}

.button-svg-icon {
  width: 14px;
  height: 14px;
  display: inline-block;
}

/* 深色模式适配 */
html.dark .workspace-action-button {
  background: #2A2A2A;
  border-color: #005DFF;
  color: #409EFF;
}

html.dark .workspace-action-button:hover {
  background: rgba(0, 93, 255, 0.1);
}

html.dark .workspace-action-button:disabled {
  opacity: 0.5;
  border-color: #606266;
  color: #606266;
}

.workspace-content {
  flex: 1;
  padding: 0 24px 24px;
  overflow: auto;
  display: flex;
  flex-direction: column;
}

.empty-workspace {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  background: transparent; /* 背景透明 */
}

.empty-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 48px 24px;
  background: white; /* 内容区白底 */
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

/* 深色模式适配 */
html.dark .empty-content {
  background: var(--el-bg-color);
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
}

.empty-content h3 {
  margin: 16px 0 8px 0;
  color: #303133;
}

/* 深色模式适配 */
html.dark .empty-content h3 {
  color: var(--el-text-color-primary);
}

.empty-content p {
  margin: 8px 0;
  color: #606266;
}

/* 深色模式适配 */
html.dark .empty-content p {
  color: var(--el-text-color-regular);
}

.file-editor {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: transparent; /* 移除白色背景，改为透明 */
  border-radius: 0; /* 移除圆角 */
  box-shadow: none; /* 移除阴影 */
  overflow-y: scroll;
}

.file-editor::-webkit-scrollbar {
  width: 6px;
}

.file-editor::-webkit-scrollbar-track {
  background: transparent;
}

.file-editor::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.file-editor::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 深色模式适配 */
html.dark .file-editor {
  background: transparent; /* 透明背景 */
  box-shadow: none; /* 移除阴影 */
}

.editor-header {
  display: flex;
  flex-direction: column; /* 改为纵向排列 */
  padding: 16px 0 8px 0; /* 减少下方padding */
  border-bottom: none; /* 移除边框 */
  background: transparent; /* 背景透明 */
}

/* 深色模式适配 */
html.dark .editor-header {
  background: transparent; /* 背景透明 */
  border-bottom: none; /* 移除边框 */
}

.file-title {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
  margin-bottom: 4px; /* 减少与路径之间的间距 */
}

.file-path-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.file-title span {
  font-size: 20px; /* 按要求设置字体大小 */
  font-weight: bold; /* 按要求设置字体粗细 */
  color: #000000;
}

/* 深色模式下标题颜色 */
html.dark .file-title span {
  color: #FFFFFF;
}

.modified-indicator {
  color: #f56c6c;
  font-size: 16px;
  margin-left: 4px;
}

.file-path {
  font-size: 14px; /* 按要求设置字体大小 */
  font-weight: normal; /* 按要求设置字体粗细 */
  color: #3D3D3D; /* 按要求设置颜色 */
  margin-left: 0; /* 移除左边距，因为现在是独立一行 */
  flex: 1; /* 让路径占据剩余空间 */
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-right: 16px; /* 与按钮保持一定距离 */
}

/* 深色模式适配 */
html.dark .file-path {
  color: #A0A0A0; /* 深色模式下使用较浅的灰色 */
}

.editor-actions {
  display: flex;
  gap: 8px;
  align-self: center; /* 垂直居中 */
}

.action-button {
  border-radius: 4px; /* 按要求设置圆角 */
  background: #FAFAFA; /* 按要求设置背景色 */
  padding: 0; /* 移除内边距，使用固定宽高 */
  font-size: 14px; /* 合适的字体大小 */
  transition: all 0.3s; /* 平滑过渡 */
  border: none; /* 移除边框 */
  width: 78px; /* 按要求设置宽度 */
  height: 28px; /* 按要求设置高度 */
  line-height: 28px; /* 文字垂直居中 */
  text-align: center; /* 文字水平居中 */
  cursor: pointer; /* 确保鼠标显示为指针 */
}

.save-button {
  color: var(--el-color-primary); /* 使用主题色 */
}

.save-button.disabled {
  color: #909399; /* 灰色文字 */
  background: #F2F2F2; /* 更浅的背景 */
  cursor: not-allowed;
}

.close-button {
  color: #FF6060; /* 按要求设置颜色 */
}

/* 深色模式适配 */
html.dark .action-button {
  background: #2A2A2A; /* 深色模式下更深的背景 */
}

html.dark .save-button.disabled {
  color: #606266;
  background: #1A1A1A;
}

/* 编辑器内容区域添加背景 */
.editor-content {
  flex: 1;
  padding: 0; /* 减少上边距，使编辑器区域提升 */
}

/* 为深色模式适配文本框背景 */
html.dark :deep(.el-textarea__inner) {
  background: var(--el-bg-color-overlay); /* 深色模式下使用覆盖层背景色 */
}

/* 修改文本编辑器样式 */
:deep(.el-textarea__inner) {
  height: 100% !important;
  resize: none;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 14px;
  line-height: 1.5;
  border: none;
  background: #FAFAFA;
  padding: 16px;
  border-radius:
}

/* 移除文本框焦点时的边框 */
:deep(.el-textarea__inner:focus) {
  box-shadow: none !important;
  border: none !important;
}

/* File error display styles */
.file-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 2rem;
  text-align: center;
  color: #606266;
  background: #FAFAFA; /* Add background color to match el-input */
  border-radius: 4px; /* Add border radius */
}

/* Dark mode support for file-error */
html.dark .file-error {
  background: var(--el-bg-color-overlay); /* Use same background as dark mode textarea */
  color: var(--el-text-color-regular);
}

.error-icon {
  margin-bottom: 1rem;
}

.unsupported-icon {
  margin-left: 60px
}

.error-title {
  font-size: 24px;
  font-weight: bold;
  text-align: center;
  color: #3D3D3D;
}

.error-message {
  font-size: 16px;
  font-weight: 300;
  text-align: center;
  color: #333333;
}

.error-suggestions {
  text-align: left;
  max-width: 600px;
}

.error-suggestions p {
  font-size: 16px;
  font-weight: bold;
  text-align: center;
  color: #333333;
}

.error-suggestions ul {
  margin: 0;
  padding-left: 0;
  list-style-type: none;
}

.error-suggestions li {
  margin-bottom: 0.5rem;
  line-height: 1.5;
  position: relative;
  padding-left: 20px;
  text-align: left;
}

.error-suggestions li:before {
  content: "•";
  position: absolute;
  left: 0;
  color: black;
  font-size: 20px;
  line-height: 20px;
}

/* Dark mode bullet point color */
html.dark .error-suggestions li:before {
  color: #79bbff;
}

/* Image viewer styles */
.image-viewer {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 1rem;
  background-color: #f8f9fa;
}

.image-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fff;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  margin-bottom: 1rem;
  overflow: hidden;
}

.preview-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  border-radius: 4px;
}

.image-info {
  background-color: #fff;
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
}

.image-info p {
  margin: 0.5rem 0;
  font-size: 14px;
  color: #606266;
}

.image-info strong {
  color: #303133;
}

.workspace-ready-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  width: 100%;
}

.ready-content {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  padding: 20px;
}

.ready-icon {
  color: #409EFF;
  margin: 20px 0px 20px 70px;
}

.ready-title {
  opacity: 1;
  font-size: 24px;
  font-weight: bold;
  line-height: normal;
  letter-spacing: 0px;
  color: #3D3D3D;
  margin-bottom: 20px;
}

.ready-subtitle {
  opacity: 1;
  font-size: 16px;
  font-weight: 300;
  line-height: normal;
  letter-spacing: 0px;
  color: #333333;
  margin-bottom: 10px;
}

.ready-actions {
  margin-top: 20px;
  display: flex;
  gap: 10px;
}

/* PDF Viewer Styles */
.pdf-viewer {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #e9e9e9; /* Light grey background for the viewer */
}

.pdf-toolbar {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 8px;
  background-color: #f2f2f2;
  border-bottom: 1px solid #ccc;
}

.pdf-toolbar button {
  background-color: #007bff;
  color: white;
  border: none;
  padding: 5px 10px;
  border-radius: 4px;
  cursor: pointer;
  margin: 0 10px;
}

.pdf-toolbar button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.pdf-toolbar span {
  font-weight: bold;
}

.pdf-canvas-container {
  flex: 1;
  overflow: auto;
  /* Use flexbox for robust centering and scrolling behavior */
  display: flex;
  justify-content: center;
  align-items: flex-start; /* Align to top for natural scrolling */
  padding: 20px;
}

.pdf-canvas {
  border: 1px solid #ccc;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}
</style>
