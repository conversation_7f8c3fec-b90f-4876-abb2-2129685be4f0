import pandas as pd
import numpy as np
from shap.explainers import Tree, Permutation, Linear, PartitionExplainer

def _explain_permutation_reg_model(perm_reg_model, X: pd.<PERSON>Frame, Y, fit_params=dict()):
    explainer = PartitionExplainer(perm_reg_model(**fit_params).fit(X, Y).predict, X, feature_names=X.columns)
    return explainer

def _explain_permutation_cls_model(perm_cls_model, X: pd.DataFrame, Y, fit_params=dict()):
    model = perm_cls_model(**fit_params).fit(X, Y)
    f = lambda x: model.predict_proba(x)[:,1]
    med = np.median(X, axis=0).reshape((1, X.shape[1]))
    explainer = PartitionExplainer(f, med, feature_names=X.columns)
    return explainer

def _explain_linear_model(linear_model, X: pd.DataFrame, Y, fit_params=dict()):
    explainer = Linear(linear_model(**fit_params).fit(X, Y), masker=X, data=X, feature_names=X.columns)
    return explainer

def _explain_tree_model(tree_model, X: pd.DataFrame, Y, fit_params=dict()):
    explainer = Tree(tree_model(**fit_params).fit(X, Y), data=X, feature_names=X.columns)
    return explainer

def _explain_shap_values(explainer, X):
    if isinstance(explainer, Tree):
        shap_values = explainer(X, check_additivity=False)
    else:
        shap_values = explainer(X)
    return shap_values

def _check_explainer(model):
    name = model.__name__
    if name in ["KNeighborsClassifier", "SVC", "DummyClassifier", "AdaBoostClassifier", "BaggingClassifier", "MLPClassifier"]:
        return _explain_permutation_cls_model
    elif name in ["KNeighborsRegressor", "SVR", "DummyRegressor", "AdaBoostRegressor", "BaggingRegressor", "MLPRegressor"]:
        return _explain_permutation_reg_model
    elif name in ["ExtraTreeClassifier", "ExtraTreeRegressor", "GradientBoostingClassifier", "GradientBoostingRegressor", "RandomForestRegressor", "RandomForestClassifier", "HistGradientBoostingClassifier", "HistGradientBoostingRegressor", "DecisionTreeClassifier", "DecisionTreeRegressor", "ExtraTreesClassifier", "ExtraTreesRegressor", "XGBClassifier", "XGBRegressor", "LGBMClassifier", "LGBMRegressor", "CatBoostClassifier", "CatBoostRegressor"]:
        return _explain_tree_model
    elif name in ["LinearRegression", "LogisticRegression", "PassiveAggressiveClassifier", "PassiveAggressiveRegressor", "RidgeClassifier", "SGDClassifier", "SGDRegressor", "Lasso", "ElasticNet", "Ridge"]:
        return _explain_linear_model
    else:
        raise Exception("No suitable explainer")

def extract_shap_values(model, X: pd.DataFrame, Y, fit_params=dict()):
    explainer_model = _check_explainer(model)
    explainer = explainer_model(model, X, Y, fit_params)
    values = _explain_shap_values(explainer, X)
    return values