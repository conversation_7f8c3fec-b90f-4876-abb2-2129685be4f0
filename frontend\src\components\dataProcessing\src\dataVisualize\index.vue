<template>
  <div v-if="visible" class="visualize-container">
    <!-- 标题 -->
    <div class="section-title">
      <StatIcon class="title-icon" />
      <span>可视化数据分析</span>
    </div>

    <!-- 列选择按钮和操作按钮 -->
    <div class="column-selector-container">
      <div class="column-buttons-wrapper" ref="columnButtonsWrapperRef">
        <div class="column-buttons">
          <div
            v-for="col in paginatedColumns"
            :key="col"
            :class="[
              'column-button',
              { selected: selectedColumns.includes(col) }
            ]"
            @click="toggleColumnSelection(col)"
          >
            <span>{{ col }}</span>
            <span v-if="selectedColumns.includes(col)" class="axis-label">
              {{ getAxisLabel(col) }}
            </span>
          </div>
        </div>
        <!-- 列分页控制 -->
        <div v-if="totalColumnPages > 1" class="pagination-dots">
          <span
            v-for="page in totalColumnPages"
            :key="page"
            :class="['dot', { active: currentColumnPage === page - 1 }]"
            @click="goToColumnPage(page - 1)"
          />
        </div>
      </div>
      <div class="action-buttons">
        <button class="action-button update-button" @click="updateData">
          更新
        </button>
        <button class="action-button export-button" @click="exportAllCharts">
          导出
        </button>
      </div>
    </div>

    <!-- 图表展示区域 -->
    <div class="charts-container">
      <div class="charts-grid">
        <div 
          v-for="(chartType, index) in paginatedChartTypes" 
          :key="chartType.type"
          class="chart-card"
        >
          <div class="chart-title">
            {{ getFormattedChartTitle(chartType) }}
            <el-icon class="magnify-icon" @click="showEnlargedChart(index, chartType)">
              <ZoomIn />
            </el-icon>
          </div>
          <div class="chart-content">
            <component
              :is="chartComponents[chartType.type]"
              :ref="el => { if (el) chartRefs[index] = el }"
              :chartData="chartData"
              :columns="columns"
              :selectedColumns="selectedColumns"
            />
          </div>
        </div>
      </div>
      <!-- 图表分页控制 -->
      <div v-if="totalChartPages > 1" class="pagination-dots">
        <span
          v-for="page in totalChartPages"
          :key="page"
          :class="['dot', { active: currentChartPage === page - 1 }]"
          @click="goToChartPage(page - 1)"
        />
      </div>
    </div>
    
    <div v-if="!hasEnoughColumns" class="chart-placeholder">
      <el-empty description="请选择至少1列以生成图表" />
    </div>

    <!-- 放大图表的对话框 -->
    <el-dialog
      v-model="enlargedChartVisible"
      :title="enlargedChartTitle"
      width="80%"
      destroy-on-close
      top="5vh"
      @closed="handleDialogClose"
    >
      <div class="enlarged-chart-container">
        <div ref="enlargedChartRef" class="enlarged-chart"></div>
      </div>
      <template #header="{ titleId, titleClass }">
        <div class="enlarged-chart-header">
          <h4 :id="titleId" :class="titleClass">{{ enlargedChartTitle }}</h4>
          <div class="enlarged-chart-actions">
            <button class="action-button export-button" @click="exportEnlargedChartData">
              导出数据
            </button>
            <button class="action-button save-button" @click="saveEnlargedChart">
              保存图片
            </button>
          </div>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount, watchEffect, nextTick } from "vue";
import StatIcon from "@/assets/svg/icon_stat.svg?component";
import { ZoomIn } from '@element-plus/icons-vue';
import * as echarts from "echarts";
import "echarts/lib/component/dataZoom";
import "echarts/lib/component/dataset";
import "echarts/extension/dataTool";
import type { TableInstance } from "../dataTable/types";
import {
  ChartBar,
  ChartBox,
  ChartHeatMap,
  ChartPie,
  ChartScatter,
  ChartScatter3D
} from './components';
import { exportChartData, exportChartImage } from "@/utils/exportUtils";

const props = defineProps<{
  tableInstance?: TableInstance;
  visible: boolean;
}>();

// 图表类型定义
const chartTypes = [
  { type: 'scatter', label: '散点图', minColumns: 2 },
  { type: 'scatter3d', label: '3D散点图', minColumns: 3 },
  { type: 'bar', label: '柱状图', minColumns: 1 },
  { type: 'pie', label: '饼图', minColumns: 1 },
  { type: 'heatmap', label: '热力图', minColumns: 0 }, // 热力图不需要选择列，始终可用
  { type: 'box', label: '箱线图', minColumns: 1 }
];

// 图表组件映射
const chartComponents = {
  'scatter': ChartScatter,
  'scatter3d': ChartScatter3D,
  'bar': ChartBar,
  'pie': ChartPie,
  'heatmap': ChartHeatMap,
  'box': ChartBox
};

// 状态
const columns = ref<string[]>([]);
const selectedColumns = ref<string[]>([]);
const chartRefs = ref<any[]>([]);
const chartData = ref<any[][]>([]);

// 列分页
const currentColumnPage = ref(0);
const columnsPerPage = ref(8); // 初始值，将根据容器宽度动态调整
const columnButtonWidth = 106; // 94px宽度 + 12px间距
const columnButtonsWrapperRef = ref<HTMLElement | null>(null);

// 图表分页
const currentChartPage = ref(0);
const chartsPerPage = ref(3); // 每页显示3个图表

// 计算属性 - 列分页
const totalColumnPages = computed(() => {
  return Math.ceil(columns.value.length / columnsPerPage.value);
});

const paginatedColumns = computed(() => {
  const start = currentColumnPage.value * columnsPerPage.value;
  const end = start + columnsPerPage.value;
  return columns.value.slice(start, end);
});

// 计算属性 - 图表分页
const totalChartPages = computed(() => {
  return Math.ceil(availableChartTypes.value.length / chartsPerPage.value);
});

const paginatedChartTypes = computed(() => {
  const start = currentChartPage.value * chartsPerPage.value;
  const end = start + chartsPerPage.value;
  return availableChartTypes.value.slice(start, end);
});

// 根据选择的列数确定可用的图表类型
const availableChartTypes = computed(() => {
  return chartTypes.filter(chart => 
    selectedColumns.value.length >= chart.minColumns
  );
});

const hasEnoughColumns = computed(() => {
  // 允许图表分页始终可用
  return true;
});

// 方法
const getAxisLabel = (col: string) => {
  const index = selectedColumns.value.indexOf(col);
  if (index === 0) return "X轴";
  if (index === 1) return "Y轴";
  if (index === 2) return "Z轴";
  return "";
};

const toggleColumnSelection = (col: string) => {
  const index = selectedColumns.value.indexOf(col);
  if (index > -1) {
    selectedColumns.value.splice(index, 1);
  } else {
    // 限制最多选择3个列
    if (selectedColumns.value.length < 3) {
      selectedColumns.value.push(col);
    }
  }
  
  // 重置图表分页到第一页
  currentChartPage.value = 0;
};

// 列分页导航
const goToColumnPage = (page: number) => {
  currentColumnPage.value = page;
};

// 图表分页导航
const goToChartPage = (page: number) => {
  currentChartPage.value = page;
};

const updateData = () => {
  fetchTableData();
};

const exportAllCharts = () => {
  // 导出当前页的所有图表
  chartRefs.value.forEach((chartInstance, index) => {
    if (chartInstance && chartInstance.getChartInstance) {
      const instance = chartInstance.getChartInstance();
      if (instance) {
        const chartType = paginatedChartTypes.value[index];
        const dataURL = instance.getDataURL({
          type: 'png',
          pixelRatio: 2,
          backgroundColor: '#fff'
        });
        exportChartImage(dataURL, `${getFormattedChartTitle(chartType)}_${Date.now()}.png`);
      }
    }
  });
};

const fetchTableData = () => {
  if (!props.tableInstance) return;
  
  try {
    // 获取表格数据
    const data = props.tableInstance.getData() || [];
    chartData.value = data;
    
    // 获取列名
    const tableColumns = props.tableInstance.getColumns() || [];
    columns.value = tableColumns.map(col => col.title || col.data);
  } catch (error) {
    console.error("Failed to get table data:", error);
  }
};

// 根据容器宽度计算每页显示的列按钮数量
const updateColumnsPerPage = () => {
  if (columnButtonsWrapperRef.value) {
    const containerWidth = columnButtonsWrapperRef.value.offsetWidth;
    const buttonsPerPage = Math.max(1, Math.floor(containerWidth / columnButtonWidth));
    columnsPerPage.value = buttonsPerPage;
    
    // 如果当前页码超出了新的总页数，则调整到最后一页
    if (currentColumnPage.value >= totalColumnPages.value && totalColumnPages.value > 0) {
      currentColumnPage.value = totalColumnPages.value - 1;
    }
  }
};

// 监听窗口大小变化
const handleResize = () => {
  // 窗口大小变化时重新计算每页显示的列按钮数量
  updateColumnsPerPage();
};

// 放大图表相关
const enlargedChartVisible = ref(false);
const enlargedChartTitle = ref('');
const enlargedChartRef = ref<HTMLElement | null>(null);
const enlargedChartInstance = ref<echarts.ECharts | null>(null);
const currentEnlargedChartType = ref<{ type: string, label: string } | null>(null);
const currentEnlargedChartIndex = ref<number | null>(null);

// 显示放大图表
const showEnlargedChart = (index: number, chartType: { type: string, label: string }) => {
  enlargedChartTitle.value = getFormattedChartTitle(chartType);
  currentEnlargedChartType.value = chartType;
  currentEnlargedChartIndex.value = index;
  enlargedChartVisible.value = true;
  
  // 等待对话框渲染完成后初始化图表
  nextTick(() => {
    if (enlargedChartRef.value) {
      // 如果已存在图表实例，先销毁它
      if (enlargedChartInstance.value) {
        enlargedChartInstance.value.dispose();
      }

      // 对热力图特殊处理，保持1:1比例
      if (chartType.type === 'heatmap') {
        resizeEnlargedHeatmapContainer();
      } else {
        // 其他图表恢复默认大小
        if (enlargedChartRef.value) {
          enlargedChartRef.value.style.width = '100%';
          enlargedChartRef.value.style.height = '100%';
        }
      }

      // 初始化新的图表实例
      enlargedChartInstance.value = echarts.init(enlargedChartRef.value);
      
      // 获取原图表配置并应用
      const originalChart = chartRefs.value[index];
      if (originalChart && originalChart.getChartOptions) {
        const options = originalChart.getChartOptions();
        enlargedChartInstance.value.setOption(options);
      }

      // 添加窗口大小变化监听
      window.addEventListener('resize', handleEnlargedChartResize);
    }
  });
};

// 调整放大后热力图容器的大小
const resizeEnlargedHeatmapContainer = () => {
  if (enlargedChartRef.value) {
    const container = enlargedChartRef.value.parentElement;
    if (container) {
      const containerWidth = container.clientWidth;
      const containerHeight = container.clientHeight;
      const size = Math.min(containerWidth, containerHeight);
      enlargedChartRef.value.style.width = `${size}px`;
      enlargedChartRef.value.style.height = `${size}px`;
    }
  }
};

// 处理放大图表的大小调整
const handleEnlargedChartResize = () => {
  if (currentEnlargedChartType.value?.type === 'heatmap') {
    resizeEnlargedHeatmapContainer();
  }
  if (enlargedChartInstance.value) {
    enlargedChartInstance.value.resize();
  }
};

// 导出放大图表数据
const exportEnlargedChartData = async () => {
  if (currentEnlargedChartIndex.value !== null && chartRefs.value[currentEnlargedChartIndex.value]) {
    const chartInstance = chartRefs.value[currentEnlargedChartIndex.value];
    if (chartInstance && chartInstance.getChartData) {
      const chartData = chartInstance.getChartData();
      if (chartData) {
        try {
          await exportChartData(chartData, enlargedChartTitle.value);
        } catch (error) {
          console.error('Export chart data failed:', error);
        }
      }
    }
  }
};

// 保存放大图表为图片
const saveEnlargedChart = async () => {
  if (enlargedChartInstance.value) {
    const dataURL = enlargedChartInstance.value.getDataURL({
      type: 'png',
      pixelRatio: 2,
      backgroundColor: '#fff'
    });
    try {
      await exportChartImage(dataURL, `${enlargedChartTitle.value}_${Date.now()}.png`);
    } catch (error) {
      console.error('Save enlarged chart failed:', error);
    }
  }
};

// 监听对话框关闭事件，清理资源
const handleDialogClose = () => {
  window.removeEventListener('resize', handleEnlargedChartResize);
  if (enlargedChartInstance.value) {
    enlargedChartInstance.value.dispose();
    enlargedChartInstance.value = null;
  }
  currentEnlargedChartIndex.value = null;
};

// 获取格式化的图表标题
const getFormattedChartTitle = (chartType: { type: string, label: string }) => {
  // 热力图保持原样
  if (chartType.type === 'heatmap') {
    return chartType.label;
  }
  
  // 散点图显示 x,y
  if (chartType.type === 'scatter') {
    if (selectedColumns.value.length >= 2) {
      const x = selectedColumns.value[0];
      const y = selectedColumns.value[1];
      return `关于${x}，${y}的散点图`;
    }
    return chartType.label;
  }
  
  // 3D散点图显示 x,y,z
  if (chartType.type === 'scatter3d') {
    if (selectedColumns.value.length >= 3) {
      const x = selectedColumns.value[0];
      const y = selectedColumns.value[1];
      const z = selectedColumns.value[2];
      return `关于${x}，${y}，${z}的3D散点图`;
    }
    return chartType.label;
  }
  
  // 其他图表类型只显示第一列
  if (selectedColumns.value.length > 0) {
    return `关于${selectedColumns.value[0]}的${chartType.label}`;
  }
  
  return chartType.label;
};

// 监听可见性和数据变化
watchEffect(() => {
  if (props.visible) {
    fetchTableData();
    // 当组件变为可见时，立即更新列按钮数量
    nextTick(() => {
      updateColumnsPerPage();
    });
  }
});

onMounted(() => {
  window.addEventListener('resize', handleResize);
  
  // 初始化时获取数据
  nextTick(() => {
    fetchTableData();
  });
});

onBeforeUnmount(() => {
  window.removeEventListener('resize', handleResize);
  
  // 销毁放大图表实例
  if (enlargedChartInstance.value) {
    enlargedChartInstance.value.dispose();
  }
});
</script>

<style lang="scss" scoped>
.visualize-container {
  padding: 16px;
  background: transparent;
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  color: #3d3d3d;
  margin-bottom: 6px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.title-icon {
  width: 14px;
  height: 14px;
}

.column-selector-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.column-buttons-wrapper {
  flex-grow: 1;
  overflow: hidden;
}

.column-buttons {
  display: flex;
  flex-wrap: nowrap;
  gap: 12px;
  overflow-x: auto;
  padding: 4px;
  
  &::-webkit-scrollbar {
    display: none;
  }
}

.column-button {
  width: 94px;
  height: 36px;
  flex-shrink: 0;
  border-radius: 12px;
  background: rgba(98, 46, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  position: relative;
  
  span {
    font-size: 14px;
    font-weight: bold;
    text-align: center;
    color: #622EFF;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    padding: 0 4px;
  }
  
  &.selected {
    background: #005DFF;
    
    span {
      color: #ffffff;
    }
    
    &::after {
      content: '';
      position: absolute;
      bottom: 4px;
      left: 50%;
      transform: translateX(-50%);
      width: 16px;
      height: 2px;
      background-color: #ffffff;
      border-radius: 1px;
    }
  }
  
  .axis-label {
    position: absolute;
    top: -6px;
    right: -6px;
    background: #005DFF;
    color: white;
    border-radius: 50%;
    width: 16px;
    height: 16px;
    font-size: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
  }
}

.action-buttons {
  display: flex;
  gap: 8px;
  margin-left: 12px;
}

.action-button {
  width: 72px;
  height: 28px;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
  background: white;
  cursor: pointer;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  
  &.update-button {
    color: #3D3D3D;
  }
  
  &.export-button {
    color: #005DFF;
  }
  
  &:hover {
    background: #f5f7fa;
  }
}

.pagination-dots {
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 8px 0;
  gap: 8px;
}

.dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #dcdfe6;
  cursor: pointer;
  transition: all 0.3s ease;

  &.active {
    width: 20px;
    border-radius: 4px;
    background-color: #005DFF;
  }
}

.charts-container {
  margin-top: 16px;
  overflow: visible;
}

.charts-grid {
  display: flex;
  gap: 16px;
  flex-wrap: nowrap;
  overflow: visible;
}

.chart-card {
  flex: 0 0 calc(33.333% - 11px);
  height: 400px;
  border-radius: 4px;
  background: #FFFFFF;
  box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.08);
  padding: 16px;
  z-index: 1; /* 确保卡片在最上层 */
  position: relative; /* 使z-index生效 */
}

.chart-title {
  font-size: 14px;
  font-weight: bold;
  color: #3D3D3D;
  margin-bottom: 8px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.magnify-icon {
  cursor: pointer;
  font-size: 16px;
  color: #909399;
  &:hover {
    color: #005DFF;
  }
}

.chart-content {
  width: 100%;
  height: calc(100% - 22px);
}

.chart-placeholder {
  height: 300px;  /* 同步增加占位符高度 */
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;
  border-radius: 4px;
  box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.08);
}

.enlarged-chart-container {
  width: 100%;
  height: 70vh;
  display: flex;
  justify-content: center;
  align-items: center;
}

.enlarged-chart {
  width: 100%;
  height: 100%;
}

.enlarged-chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 0 20px;
}

.enlarged-chart-header h4 {
  font-size: 16px;
  font-weight: 600;
  color: #3D3D3D;
  margin: 0;
}

.enlarged-chart-actions {
  display: flex;
  gap: 8px;
}

.save-button {
  color: #67C23A;
}

@media (max-width: 1200px) {
  .charts-grid {
    .chart-card {
      flex: 0 0 calc(50% - 8px);
    }
  }
}

@media (max-width: 768px) {
  .charts-grid {
    .chart-card {
      flex: 0 0 100%;
    }
  }
}
</style> 