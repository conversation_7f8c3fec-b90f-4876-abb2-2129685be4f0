from app.types.model_request import ModelRequest, ModelConfig, Dataset, Model
from typing import Tuple, Union, Any
import pandas as pd
from sklearn.model_selection import train_test_split
from flask import current_app
from app.types.flaskapp import FlaskWithExecutor
from typing import cast
from app.types.services import ModelTask

def check_dataset(dataset_info: Dataset) -> Tuple[Any, Any]:
    
    # 获取数据集
    values = dataset_info.get("data", [])
    if len(values) == 0: raise ValueError("数据集为空")
    # 获取数据集列名
    columns = dataset_info.get("meta", {}).get("headers", {}).get("all", [])
    if len(columns) == 0: raise ValueError("数据集列名不能为空")
    # 创建dataset
    dataset = pd.DataFrame(values, columns=columns)
    # 获取索引列
    index_column = dataset_info.get("meta", {}).get("headers", {}).get("index", [])
    if len(index_column) == 0: raise ValueError("索引列不能为空")
    # 设置索引
    dataset = dataset.set_index(index_column, drop=True)
    # 获取特征列
    feature_columns = dataset_info.get("meta", {}).get("headers", {}).get("features", [])
    if len(feature_columns) == 0: raise ValueError("特征列不能为空")
    if len(feature_columns) == 1: raise ValueError("特征列最少2个")
    # 获取目标列
    target_columns = dataset_info.get("meta", {}).get("headers", {}).get("target", [])
    if len(target_columns) == 0: raise ValueError("目标列不能为空")
    # 获取X
    X = dataset[feature_columns]
    X = X.apply(pd.to_numeric, errors="coerce")
    # 获取Y
    Y = dataset[target_columns]
    Y = Y.apply(pd.to_numeric, errors="coerce")
    return X, Y


def check_model(model_info: Model) -> dict:

    # 模型算法名
    alg_name = model_info.get("algorithm", {}).get("name")
    if not alg_name: raise ValueError("模型算法名不能为空")
    # 模型算法参数
    params = model_info.get("algorithm", {}).get("params", {})
    if len(params) == 0: raise ValueError("模型算法参数不能为空")
    optimize = params.pop("optimize", False)
    alg_params = {}
    for i, j in params.items():
        if isinstance(j, list) and len(list(set(j))) == 1:
            alg_params[i] = j[0]
        else:
            alg_params[i] = j
    # 模型任务uid
    uid = model_info.get("uid", None)

    # 模型测试集评估配置
    test_info = model_info.get("meta", {}).get("evaluation", {}).get("test", False)
    if test_info:
        test_size = test_info.get("size", 0.2)
        random_state = test_info.get("random_state", 42)
        test = True
    else:
        test_size = None
        random_state = None
        test = False
    
    # 模型交叉验证配置
    cv_info = model_info.get("meta", {}).get("evaluation", {}).get("cv", False)
    if cv_info:
        cv = cv_info.get("k", 5)
    else:
        cv = False
    
    # 模型LOOCV配置
    loocv_info = model_info.get("meta", {}).get("evaluation", {}).get("loocv", False)
    if loocv_info:
        loocv = True
    else:
        loocv = False
    
    # 模型SHAP配置
    shap = model_info.get("meta", {}).get("evaluation", {}).get("shap", False)
    
    if loocv or optimize or cv or shap:
        asynchronous = True
    else:
        asynchronous = False
    
    return {
        "name": model_info.get("meta", {}).get("name", ""),
        "alg_name": alg_name,
        "alg_params": alg_params,
        "uid": uid,
        "test": test,
        "test_size": test_size,
        "random_state": random_state,
        "cv": cv,
        "loocv": loocv,
        "asynchronous": asynchronous,
        "optimize": optimize,
        "shap": shap,
    }

def check_model_request(model_request: ModelRequest) -> ModelConfig:

    X, Y = check_dataset(model_request.get("dataset"))

    model_info = check_model(model_request.get("model"))

    if model_info.get("test"):
        x_train, x_test, y_train, y_test = train_test_split(
            X, Y, test_size=model_info.get("test_size"), random_state=model_info.get("random_state")
        )
    else:
        x_train = X.copy()
        y_train = Y.copy()
        x_test = None
        y_test = None

    return ModelConfig(
        name=model_info['name'],
        x_train=x_train, # type: ignore
        y_train=y_train, # type: ignore
        x_test=x_test, # type: ignore
        y_test=y_test, # type: ignore
        cv=model_info["cv"],
        loocv=model_info["loocv"],
        test=model_info["test"],
        alg_name=model_info["alg_name"],
        alg_param=model_info["alg_params"],
        uid=model_info["uid"],
        asynchronous=model_info["asynchronous"],
        optimize=model_info["optimize"],
        shap=model_info["shap"],
        is_rabbitmq_ready=cast(FlaskWithExecutor, current_app).is_rabbitmq_ready
    )

def check_model_edit_request(new_model_request: ModelRequest, old_model_config: ModelConfig) -> ModelConfig:
    X, Y = check_dataset(new_model_request.get("dataset"))
    new_model_info = check_model(new_model_request.get("model"))
    if new_model_info.get("test"):
        x_train, x_test, y_train, y_test = train_test_split(
            X, Y, test_size=new_model_info.get("test_size"), random_state=new_model_info.get("random_state")
        )
    else:
        x_train = X.copy()
        y_train = Y.copy()
        x_test = None
        y_test = None
    return ModelConfig(
        name=old_model_config.name,
        x_train=x_train, # type: ignore
        y_train=y_train, # type: ignore
        x_test=x_test, # type: ignore
        y_test=y_test, # type: ignore
        cv=new_model_info["cv"],
        loocv=new_model_info["loocv"],
        test=new_model_info["test"],
        alg_name=old_model_config.alg_name,
        alg_param=new_model_info["alg_params"],
        uid=old_model_config.uid,
        asynchronous=new_model_info["asynchronous"],
        optimize=new_model_info["optimize"],
        shap=new_model_info["shap"],
        is_rabbitmq_ready=cast(FlaskWithExecutor, current_app).is_rabbitmq_ready
    )

import werkzeug
from pathlib import PurePath

class ReadWebFile:
    def __init__(self, filepath):
        self.filepath = filepath
        if isinstance(filepath, werkzeug.datastructures.FileStorage):
            self.ext = self.extension = PurePath(filepath.filename).suffix

    def read(self):
        if self.ext in [".xlsx", ".xls"]:
            data = pd.read_excel(self.filepath)
        elif self.ext in [".txt", ".csv"]:
            if self.ext == ".txt":
                spliter = "\t"
            elif self.ext == ".csv":
                spliter = ","
            buffer = self.filepath.read()
            for encoding in ["utf-8", "gbk", "utf-16"]:
                try:
                    file_read = buffer.decode(encoding)
                    if "\r\n" in file_read:
                        line_spliter = "\r\n"
                    else:
                        line_spliter = "\n"
                    file_read = file_read.split(line_spliter)
                    error = ""
                    break
                except Exception as e:
                    error = e
                    continue
            if error:
                file_read = ""
            data = []
            if len(file_read) > 0:
                for index, line in enumerate(file_read):
                    row = []
                    for cell in line.split(spliter):
                        row.append(cell)
                    if index == 0:
                        columns = row
                    elif index == len(file_read) - 1:
                        continue
                    else:
                        data.append(row)
                data = pd.DataFrame(data, columns=columns)
            else:
                data = pd.DataFrame([])
        else:
            data = pd.DataFrame([])
        return data


def read_data(filepath: werkzeug.datastructures.FileStorage) -> pd.DataFrame:
    if isinstance(filepath, werkzeug.datastructures.FileStorage):
        data = ReadWebFile(filepath).read()
        return data


def transform_model_info(model_info) -> dict:
    try:
        result = model_info.get("result", model_info)
    except:
        result = model_info.result
    model_params = result.pop("model_params")
    x_train = model_params.get("x_train", {})
    y_train = model_params.get("y_train", {})
    x_test = model_params.get("x_test", {})
    y_test = model_params.get("y_test", {})
    new_params = {}
    for yname, yvalue in y_train.items():
        new_params[yname] = {
            **model_params,
            "x_train": x_train,
            "y_train": {yname: yvalue},
            "x_test": x_test,
            "y_test": {yname: y_test[yname]} if y_test is not None else None,
        }
    result['model_params'] = new_params
    return result