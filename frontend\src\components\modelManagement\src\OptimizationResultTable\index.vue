<template>
  <div class="optimization-result-view">
    <div class="model-controls">
      <div class="best-params-display" v-if="optimizedParams && Object.keys(optimizedParams).length > 0">
        <span class="title">最佳参数组合:</span>
        <div class="param-item" v-for="(value, key) in optimizedParams" :key="key">
          <span class="param-key">{{ key }}:</span>
          <span class="param-value">{{ value }}</span>
        </div>
      </div>
      <el-button type="primary" @click="exportData" :icon="Download">导出结果</el-button>
    </div>
    <ReTable v-if="tableData.length > 0" :data="tableData">
      <template #default="{ data: paginatedData }">
        <el-table :data="paginatedData" class="model-table" height="400">
          <el-table-column
            v-for="column in tableColumns"
            :key="column.prop"
            :prop="column.prop"
            :label="column.label"
            :width="column.width"
            sortable
            show-overflow-tooltip
          >
            <template #default="{ row }">
              {{ formatValue(row[column.prop]) }}
            </template>
          </el-table-column>
        </el-table>
      </template>
    </ReTable>
    <el-empty v-else description="无优化过程数据" />

  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { ElMessage } from 'element-plus';
import { Download } from "@element-plus/icons-vue";
import ReTable from "@/components/ReTable/index.vue";
import { exportSingleSheet } from "@/utils/exportUtils";

const props = defineProps<{
  modelResult?: {
    optimizedParams?: Record<string, any>;
    optimizedResult?: any[];
  };
}>();

const optimizedParams = computed(() => props.modelResult?.optimizedParams);
const optimizedResult = computed(() => props.modelResult?.optimizedResult);

const tableData = computed(() => optimizedResult.value || []);

const tableColumns = computed(() => {
  if (!optimizedResult.value || optimizedResult.value.length === 0) {
    return [];
  }
  const firstRow = optimizedResult.value[0];
  const keys = Object.keys(firstRow);
  
  // Custom labels and widths for specific columns
  const columnConfig: Record<string, { label: string, width?: number }> = {
    'rmse': { label: 'RMSE' },
    'mse': { label: 'MSE' },
    'mae': { label: 'MAE' },
    'r2': { label: 'R²' },
    'r': { label: 'R' },
    'mape': { label: 'MAPE' },
    'n_samples': { label: 'n_samples' },
  };

  return keys.map(key => ({
    prop: key,
    label: columnConfig[key]?.label || key,
    width: columnConfig[key]?.width,
  }));
});

const formatValue = (value: any) => {
  // Convert numeric strings to numbers
  const numValue = typeof value === 'string' && !isNaN(Number(value)) ? Number(value) : value;
  if (typeof numValue === 'number') {
    return parseFloat(numValue.toFixed(4));
  }
  return numValue;
};

const exportData = async () => {
  if (!optimizedResult.value || optimizedResult.value.length === 0) {
    ElMessage.warning('没有可导出的数据');
    return;
  }

  try {
    const headers = tableColumns.value.map(c => c.label);
    const content = optimizedResult.value.map(row => {
      return tableColumns.value.map(c => {
        const value = row[c.prop];
        return formatValue(value);
      });
    });

    await exportSingleSheet(
      { headers, content },
      {
        suggestedName: '超参数优化结果',
        sheetName: '优化结果',
        exportType: 'auto'
      }
    );
    
    ElMessage.success('结果已开始导出');
  } catch (error) {
    console.error('导出失败:', error);
    ElMessage.error('导出失败，请重试');
  }
};

</script>

<style lang="scss" scoped>
.optimization-result-view {
  .model-controls {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;
    flex-wrap: wrap;
    gap: 16px;
  }
}

.best-params-display {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px 16px;

  .title {
    font-size: 16px;
    font-weight: 600;
  }
  
  .param-item {
     display: inline-flex;
     align-items: baseline;
     gap: 6px;
  }

  .param-key {
    font-size: 14px;
    color: var(--el-text-color-secondary);
  }
  .param-value {
    font-size: 16px;
    font-weight: 600;
    color: var(--el-text-color-primary);
  }
}
</style> 