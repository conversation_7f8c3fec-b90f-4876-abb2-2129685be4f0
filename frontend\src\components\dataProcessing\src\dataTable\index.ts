import DataTable from "./index.vue";
import type { App } from "vue";

// 默认导出组件
export default DataTable;

// 命名导出组件
export { DataTable };

// 导出类型
export * from "./types/index";

// 导出组合式函数
export { useTable } from "./composables/useTable";
export { useTableData } from "./composables/useTableData";
export { useTableResize } from "./composables/useTableResize";

// 提供安装方法
export const install = {
  install(app: App) {
    app.component("DataTable", DataTable);
  },
};
