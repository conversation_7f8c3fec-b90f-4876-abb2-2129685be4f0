interface Range {
  min: number;
  max: number;
  count: number;
}

interface Constraint {
  latex: string;
  python: string;
  result: string;
  error: boolean;
}

// 虚拟样本生成请求接口
interface VirtualSamplesGenerationRequest {
  constraints: Constraint[];
  data: {
    values: any[][];
    columns: string[];
    ranges?: Record<string, Range>; // 特征范围设置
  }
  generation?: {            // 生成参数（可选）
    method?: string;              // 生成方法
    params?: Record<string, any>; // 算法特定参数
  };
}
  
// 虚拟样本生成响应接口
interface VirtualSamplesGenerationResponse {
  code: number;
  msg: string;
  data: {
    constraints: string[];
    data:{
      values: any[][];
      columns: string[];
      shape: number[][];
    }
    generation: {
      method: string;
      params: Record<string, any>;
    }
  };
}

export {
  Range,
  Constraint,
  VirtualSamplesGenerationRequest,
  VirtualSamplesGenerationResponse
};