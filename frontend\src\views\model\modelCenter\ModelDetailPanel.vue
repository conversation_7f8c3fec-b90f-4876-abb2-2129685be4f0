<template>
  <ModelIntroduction
    :model-type="modelType"
    :model-display-name="modelDisplayName"  
    :is-details-visible-default="true"
  />
  <div class="model-detail-panel">
    <!-- 历史计算 -->
    <section class="history-section">
      <h3 class="section-title">模型计算历史</h3>
      <el-row :gutter="10" class="search-controls">
        <el-col :span="5">
          <el-date-picker
            v-model="modelBuildTime"
            type="date"
            placeholder="选择日期"
            value-format="YYYY-MM-DD"
            style="width: 100%;"
          />
        </el-col>
        <el-col :span="5">
          <el-input
            v-model="keyword"
            placeholder="输入关键词搜索"
            clearable
            @keyup.enter="searchHistory"
          />
        </el-col>
        <el-col :span="4">
          <el-button type="primary" @click="searchHistory">搜索</el-button>
        </el-col>
      </el-row>
      <el-row :gutter="10" class="history-list">
        <el-col
          v-for="(record, index) in historyRecords"
          :key="index"
          :span="8"
        >
          <el-card shadow="hover" class="history-card">
            <p class="filename">{{ record.name }}</p>
            <el-tag size="small" :type="getCategoryTagType(record.category)" class="model-tag">{{ getAlgorithmDisplayName(record.originParams.model.algorithm.name) }}</el-tag>
            <p class="time">{{ formatDate(record.createdAt) }}</p>
            <div class="info-button">
                <el-button type="primary" size="small" @click="viewDetails(record)" :loading="viewLoadingUids.includes(record.uid)">查看详情</el-button>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </section>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import type { ModelMetadata } from "@/types/modelTypes";
import ModelIntroduction from "@/components/modelManagement/src/ModelIntroduction/index.vue";
import { ElMessage } from "element-plus";
import { onMounted } from "vue";
import { getModelList } from "@/api/models/model";
import { useRouter } from "vue-router"; // Import useRouter
import { getModelInfo } from "@/api/models/model"; // Import getModelInfo
import nameMap from "@/config/nameMap.json"; // Import nameMap
import { useWorkspaceStoreHook } from "@/store/modules/workspace";

const props = defineProps<{
  model: ModelMetadata;
  modelType: string;
  modelDisplayName: string;
}>();


interface HistoryItem {
  id: number;
  uid: string;
  name: string;
  category: "linear" | "tree" | "other";
  originParams: {
    dataset: {
      target: string;
      [key: string]: any;
    };
    model: {
      algorithm: {
        name: string;
        [key: string]: any;
      };
      [key: string]: any;
    };
    [key: string]: any;
  };
  createdAt: string;
  modelType: "reg" | "cls";
  status: "completed" | "running" | "pending" | "failed" | "upload";
  targetName: string[];
}

// 修改 historyRecords 为响应式引用
const historyRecords = ref<HistoryItem[]>([]);
const modelBuildTime = ref<string | null>(null); // For single date
const modelNameSearch = ref(""); // New: For specific model name search
const keyword = ref(""); // For general keyword search
const viewLoadingUids = ref<string[]>([]); // To manage loading state for detail buttons

const router = useRouter(); // Initialize useRouter

const workspaceStore = useWorkspaceStoreHook()

const searchHistory = async () => {
  try {
    const params: {
      buildDate?: string;
      modelName?: string;
      searchKeyword?: string;
      path?: string;
    } = {};
    if (modelBuildTime.value) params.buildDate = modelBuildTime.value;
    if (props.modelDisplayName) params.modelName = props.model.id;

    if (keyword.value) params.searchKeyword = keyword.value;

    const workspacePath = workspaceStore.getCurrentWorkspacePath || "";
    const workspaceName = workspacePath
      ? workspacePath
          .replace(/[\\/]+$/, "")
          .split(/[\\/]/)
          .pop() || ""
      : "";
    params.path = workspaceName;

    const res = await getModelList(params);
    if (res.code === 200) {
      historyRecords.value = res.data;
      if (res.data.length === 0) {
        ElMessage.info("没有找到匹配的计算历史。");
      }
    } else {
      ElMessage.error(res.msg || "获取计算历史失败");
    }
  } catch (error) {
    console.error("获取计算历史失败:", error);
    ElMessage.error("获取计算历史失败");
  }
};

// 获取算法的显示名称 (中文映射)
const getAlgorithmDisplayName = (algorithmName: string) => {
  return nameMap[algorithmName] || algorithmName;
};

// 获取模型类别标签类型
const getCategoryTagType = (category: string) => {
  switch (category) {
    case "linear":
      return "primary";
    case "tree":
      return "success";
    case "other":
      return "info";
    default:
      return "info";
  }
};

// 格式化日期
const formatDate = (dateString: string) => {
  if (!dateString) return "-";
  try {
    const date = new Date(dateString);
    return date.toLocaleString("zh-CN", {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
    });
  } catch {
    return dateString;
  }
};

const viewDetails = async (item: HistoryItem) => {
  if (item.status !== "completed") {
    ElMessage.warning("模型尚未完成，无法查看结果");
    return;
  }

  viewLoadingUids.value.push(item.uid);
  try {
    if (window.ipcRenderer) {
      const res = await getModelInfo(item.uid);
      if (res.code === 200) {
        const fullModelInfo = res.data;
        const correctModelType = fullModelInfo.alg;

        await window.ipcRenderer.send("cache_model_result", {
          uid: item.uid,
          result: fullModelInfo,
        });

        const modelCategory = item.category;
        const routeName = modelCategory === "linear" ? "LMModelResult" : "MLModelResult";

        const { href } = router.resolve({
          name: routeName,
          query: {
            uid: item.uid,
            modelType: correctModelType,
            modelName: item.name,
            targetName: item.targetName,
          },
        });
        window.open(href);
      } else {
        ElMessage.error(res.msg || "获取模型详情失败");
      }
    } else {
      ElMessage.error("非Electron环境，无法打开独立窗口");
    }
  } catch (error) {
    console.error("获取模型详情失败:", error);
    ElMessage.error("获取模型详情失败");
  } finally {
    viewLoadingUids.value = viewLoadingUids.value.filter(uid => uid !== item.uid);
  }
};

onMounted(() => {
  searchHistory(); // Fetch initial data when the component mounts
});

</script>

<style scoped>
.model-detail-panel {
  padding: 20px;
  margin: 0px 15px;
  border-radius: 8px;
  background: #FFFFFF;
  box-shadow: 0px 0px 40px 0px rgb(0 93 255 / 8%);
  border: none;
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 12px;
  color: #333;
}

.description {
  font-size: 14px;
  color: #666;
  line-height: 1.6;
}

.suggestion-list {
  margin-bottom: 20px;
}

.tip-card {
  text-align: center;
  font-size: 13px;
  color: #444;
}

.history-card {
  font-size: 13px;
  margin-top: 8px;
  margin-bottom: 8px;
}

.filename {
  font-weight: bold;
  margin-bottom: 5px;
}

.time {
  font-size: 12px;
  color: #888;
  margin: 10px 0;
}

.info-button {
  display: flex;
  justify-content: right;
}

.info-button {
  display: flex;
  justify-content: right;
}

.search-controls {
  display: flex;
  gap: 10px;
  margin-bottom: 15px;
}

.search-controls .el-input,
.search-controls .el-date-picker {
  flex: 1;
}
</style>
