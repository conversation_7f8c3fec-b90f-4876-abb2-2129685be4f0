<template>
  <div class="single-target-optimization">
    <!-- 主配置卡片 -->
    <div class="main-config-card content-bg">
      <!-- Tab controls -->
      <div class="selection-controls">
        <button type="button" :class="['selection-button', { 'is-active': activeSelectionTab === 'settings' }]"
          @click="activeSelectionTab = 'settings'">
          <img :src="settingsIcon" alt="icon" class="btn-icon" />
          优化设置
        </button>
        <button type="button" :class="['selection-button', { 'is-active': activeSelectionTab === 'parameters' }]"
          @click="activeSelectionTab = 'parameters'">
          <img :src="parameterIcon" alt="icon" class="btn-icon" />
          算法参数
        </button>
        <button type="button" 
          :class="['selection-button', { 'is-active': activeSelectionTab === 'featureRanges' }]"
          @click="handleFeatureRangesClick"
          :disabled="!selectedModel">
          <img :src="featureRangeIcon" alt="icon" class="btn-icon" />
          自变量范围
        </button>
      </div>

      <div class="selection-content">
        <div v-show="activeSelectionTab === 'settings'">
          <div class="setting-section">
            <el-row :gutter="20" align="middle">
              <el-col :xs="24" :sm="12" :lg="5">
                <div class="form-item-label">预测模型</div>
                  <div style="display: flex; align-items: center; width: 100%;">
                   <el-select v-model="selectedModel" placeholder="选择预测模型" clearable style="flex-grow: 1;"
                     @change="handleModelChange" filterable :filter-method="filterModels" :reserve-keyword="false" :disabled="loading">
                     <el-option v-for="item in filteredModelOptions" :key="item.value" :label="item.label" :value="item.value">
                       <el-tooltip :content="`ID: ${item.id} | 创建时间: ${item.createdAt} | 目标: ${item.targetName}`"
                         placement="right" effect="light">
                         <div>{{ item.label }}</div>
                       </el-tooltip>
                     </el-option>
                   </el-select>
                  <el-button circle @click="emit('refresh-models')" :disabled="loading" style="margin-left: 8px;">
                    <el-icon :class="{ 'is-loading': loading }">
                      <Refresh />
                    </el-icon>
                  </el-button>
                </div>
              </el-col>
              <el-col :xs="24" :sm="12" :lg="4">
                <div class="form-item-label">优化算法</div>
                <el-select v-model="selectedOptimizer" placeholder="选择优化算法" style="width: 100%">
                  <el-option v-for="item in optimizerOptions" :key="item.id" :label="item.config.displayName"
                    :value="item.id" />
                </el-select>
              </el-col>
              <el-col :xs="12" :sm="6" :lg="3">
                <div class="form-item-label">目标值</div>
                <el-input-number v-model="targetValue" :precision="1" :step="0.1" :min="minTargetValue"
                  :max="maxTargetValue" placeholder="目标值" style="width: 100%" :disabled="!selectedModel" />
              </el-col>
              <el-col :xs="12" :sm="6" :lg="4">
                <div class="form-item-label">优化精度%</div>
                <el-input-number v-model="criterion" :precision="2" :step="0.01" :min="0.01" :max="1"
                  placeholder="优化精度%" style="width: 100%" :disabled="!selectedModel" />
              </el-col>
              <el-col :xs="24" :sm="24" :lg="3">
                <div class="form-item-label">优化目标</div>
                <div class="target-info">
                  <!-- 当优化目标是数组时显示选择器 -->
                  <el-select
                    v-if="isOptimizationTargetArray"
                    v-model="selectedOptimizationTarget"
                    placeholder="选择优化目标"
                    style="width: 150px"
                    @change="handleOptimizationTargetChange">
                    <el-option
                      v-for="target in optimizationTargetOptions"
                      :key="target"
                      :label="target"
                      :value="target" />
                  </el-select>
                  <!-- 当优化目标不是数组时显示文本 -->
                  <span v-else class="optimization-target-label">{{ optimizationTargetLabel }}</span>
                  <el-tooltip content="当前优化目标来自于选择的预测模型" placement="top" effect="light">
                    <el-icon class="info-icon">
                      <InfoFilled />
                    </el-icon>
                  </el-tooltip>
                  <el-button type="danger" v-if="progress === 100" @click="resetOptimization"
                    style="margin-left: 20px;">
                    重置
                  </el-button>
                </div>
              </el-col>
            </el-row>
            <el-row class="mt-4" align="middle">
              <el-col :span="24" style="display: flex; align-items: center;">
                <el-button type="primary" @click="startOptimization" :disabled="disableOptimization">
                  开始优化
                </el-button>
                <el-progress :percentage="progress" type="circle" :width="40" style="margin-left: 16px;"
                  v-show="progress > 0" :status="progress === 100 ? 'success' : ''" />
              </el-col>
            </el-row>
          </div>
        </div>
        <div v-show="activeSelectionTab === 'parameters'">
          <div class="params-section">
            <ModelParamsSetting v-if="selectedOptimizer" ref="modelParamsSettingRef" :algorithm-name="selectedOptimizer"
              :is-optimizer="true" />
            <el-empty v-else description="请先选择优化算法" />
          </div>
        </div>
        <div v-show="activeSelectionTab === 'featureRanges'">
        <div class="feature-ranges-section">
          <template v-if="featureRanges.length > 0">
            <div class="form-item-label">自变量范围设置</div>
            <div class="feature-ranges-table">
              <div class="models-header">
                <div class="header-cell feature-name">特征名称</div>
                <div class="header-cell min-value">最小值</div>
                <div class="header-cell max-value">最大值</div>
              </div>
              <div v-for="(range, index) in featureRanges" :key="index" class="model-row">
                <div class="model-cell feature-name">
                  {{ range.featureName }}
                </div>
                <div class="model-cell min-value">
                  <el-input-number
                    v-model="range.minValue"
                    :precision="3"
                    :step="0.001"
                    :min="-1000"
                    :max="range.maxValue"
                    size="small"
                    style="width: 100%" />
                </div>
                <div class="model-cell max-value">
                  <el-input-number
                    v-model="range.maxValue"
                    :precision="3"
                    :step="0.001"
                    :min="range.minValue"
                    :max="1000"
                    size="small"
                    style="width: 100%" />
                </div>
              </div>
            </div>
            <el-alert
              title="提示"
              description="自变量范围将影响优化算法的搜索空间，请根据实际需求设置合理的范围。"
              type="info"
              :closable="false"
              style="margin-top: 16px;" />
          </template>
          <el-empty v-else description="请先选择预测模型以查看自变量范围" />
        </div>
      </div>
      </div>
    </div>

    <!-- 优化配方结果 -->
    <div class="result-section content-bg">
      <div class="section-header">
        <h3 class="section-title">优化配方结果</h3>
        <el-button :icon="Download" @click="exportResults" :disabled="resultData.length === 0">
          导出数据
        </el-button>
      </div>
      <ReTable :data="resultData">
        <template #default="{ data: paginatedData }">
          <el-table :data="paginatedData" class="result-table" max-height="calc(100vh - 450px)">
            <template #empty>
              <el-empty description="暂无结果" />
            </template>
            <el-table-column type="index" label="序号" width="50" v-if="resultData.length > 0" />
            <el-table-column v-for="col in sortedTableColumns" 
              :key="col.prop" 
              :prop="col.prop" 
              :label="col.label"
              sortable 
              v-if="resultData.length > 0" />
          </el-table>
        </template>
      </ReTable>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, computed, onBeforeUnmount, defineComponent } from "vue";
import { ElMessage, ElLoading } from "element-plus";
import { InfoFilled, Refresh } from "@element-plus/icons-vue";
import ModelParamsSetting from "@/components/modelManagement/src/ModelParamsSetting/index.vue";
import { SupportedModel } from "@/utils/modelParamsLoader";
import { runOptimizationSingle } from "@/api/models/optimize";
import { dynamicModelManager, ModelMetadata, getSingleTargetAlgorithms } from "@/utils/dynamicModelLoader";
import { getSocket } from "@/utils/socket";
import { Loading, Check, Download } from "@element-plus/icons-vue";
import ReTable from "@/components/ReTable/index.vue";
import { exportSingleSheet } from "@/utils/exportUtils";

import settingsIconSelected from "@/assets/svg/variable_selected.svg?url";
import settingsIconDefault from "@/assets/svg/variable.svg?url";
import parameterIconSelected from "@/assets/svg/parameter_selected.svg?url";
import parameterIconDefault from "@/assets/svg/parameter.svg?url";
import featureRangeIconSelected from "@/assets/svg/range_selected.svg?url";
import featureRangeIconDefault from "@/assets/svg/range.svg?url";
import { useWorkspaceStoreHook } from "@/store/modules/workspace";

defineOptions({
  name: "SingleTargetOptimization"
});

const props = defineProps<{
  modelList: any[],
  loading: boolean
}>();

const emit = defineEmits(['refresh-models', 'update:optimization-status']);

const activeSelectionTab = ref<'settings' | 'parameters' | 'featureRanges'>('settings');

const workspaceStore = useWorkspaceStoreHook();

const settingsIcon = computed(() =>
  activeSelectionTab.value === 'settings'
    ? settingsIconSelected
    : settingsIconDefault
);
const parameterIcon = computed(() =>
  activeSelectionTab.value === 'parameters'
    ? parameterIconSelected
    : parameterIconDefault
);
const featureRangeIcon = computed(() =>
  activeSelectionTab.value === 'featureRanges'
    ? featureRangeIconSelected
    : featureRangeIconDefault
);

const selectedModel = ref<string | null>(null);
const selectedOptimizer = ref<string | null>(null);
const optimizationTargetLabel = ref("-");
const targetValue = ref(0);
const criterion = ref(0.05);
const optimizationInProgress = ref(false); // 标记优化过程是否进行中

// 优化目标相关的响应式数据
const optimizationTargetOptions = ref<string[]>([]);
const selectedOptimizationTarget = ref<string>("");

// 自变量范围数据
const featureRanges = ref<Record<string, { min: number; max: number }>[]>([]);

// 参数设置组件引用
const modelParamsSettingRef = ref<InstanceType<typeof ModelParamsSetting> | null>(null);

// 动态获取的模型选项 (从父组件获取)
const modelOptions = ref<any[]>([]);

// 过滤后的模型选项（用于搜索）
const filteredModelOptions = ref<any[]>([]);

// 优化算法选项 (从searchParams JSON文件获取)
const optimizerOptions = ref<ModelMetadata[]>([]);

// 目标值范围
const minTargetValue = ref(0);
const maxTargetValue = ref(100);

// 结果表格列
const tableColumns = ref<Array<{ prop: string; label: string }>>([]);
const resultData = ref<Array<Record<string, any>>>([]);

// 节流
let lastUpdate = 0;
const throttleInterval = 1000; // 1000ms
const throttleData = ref<Array<any>>([]);

// 代数
const progress = ref(0);

const handleSearchInfo = (data) => {
  console.log(data);
  progress.value = Number(data.data.progress || 0);

  if (data.code === 200) {
    // 检查是否已经完成，避免重复提示
    if ((Number(data.data.progress) >= 100 || (data.data.status && data.data.status === "completed")) && optimizationInProgress.value) {
      optimizationInProgress.value = false; // 重新启用按钮
      emit('update:optimization-status', false);
      ElMessage.success("优化已完成");
      progress.value = 100;
      
      // 移除 socket 监听器，避免继续接收消息
      const socket = getSocket();
      socket.off("search_info");
      return; // 直接返回，不再处理后续数据
    } else if (!optimizationInProgress.value) {
      // 如果优化已经结束，忽略后续消息
      return;
    } else {
      // 节流
      const now = Date.now();
      if (now - lastUpdate < throttleInterval) {
        throttleData.value.push(...data.data.searchInfo);
        return;
      };
      lastUpdate = now;
    }

    let tmpData = [];
    if (throttleData.value.length > 0) {
      tmpData.push(...throttleData.value);
      throttleData.value = [];
    }
    if (data.data.searchInfo && data.data.searchInfo.length > 0) {
      tmpData.push(...data.data.searchInfo);
    }
    resultData.value.push(...tmpData);
    if (tableColumns.value.length === 0 && resultData.value.length > 0) {
      tableColumns.value = Object.keys(resultData.value[0]).map(key => ({
        prop: key,
        label: key
      }));
    }
  } else {
    ElMessage.error(data.msg);
    optimizationInProgress.value = false; // 出错时也要重置状态
    emit('update:optimization-status', false);
  }
};

// 判断优化目标是否为数组
const isOptimizationTargetArray = computed(() => {
  return Array.isArray(optimizationTargetOptions.value) && optimizationTargetOptions.value.length > 0;
});

// 处理优化目标选择变化
const handleOptimizationTargetChange = () => {
  optimizationTargetLabel.value = selectedOptimizationTarget.value || "-";
  // 当优化目标改变时，更新目标值范围
  updateTargetValueRange();
};
const resetOptimization = () => {
  resultData.value = [];
  progress.value = 0;
  optimizationInProgress.value = false;
  emit('update:optimization-status', false);
  tableColumns.value = [];
  selectedModel.value = null; // 清空选择的模型
  featureRanges.value = []; // 清空自变量范围数据
  activeSelectionTab.value = 'settings'; // 切换回设置选项卡
};

// 添加新的计算属性，将带(pred)的列排在前面
const sortedTableColumns = computed(() => {
  if (tableColumns.value.length === 0) {
    return [];
  }
  
  // 分离带(pred)的列和其他列
  const predColumns = tableColumns.value.filter(col => 
    col.prop.includes('(pred)') || col.label.includes('(pred)')
  );
  
  const otherColumns = tableColumns.value.filter(col => 
    !col.prop.includes('(pred)') && !col.label.includes('(pred)')
  );
  
  // 先显示带(pred)的列，再显示其他列
  return [...predColumns, ...otherColumns];
});

watch(() => props.modelList, (newValue) => {
  modelOptions.value = newValue;
  filteredModelOptions.value = newValue;
}, { immediate: true, deep: true });


const exportResults = () => {
  if (resultData.value.length === 0) {
    ElMessage.warning("没有可导出的数据");
    return;
  }

  // 使用排序后的列
  const columnsToExport = sortedTableColumns.value;

  const headers = ["序号", ...columnsToExport.map(c => c.label)];

  const content = resultData.value.map((row, index) => [
    index + 1,
    ...columnsToExport.map(c => row[c.prop])
  ]);

  exportSingleSheet(
    { headers, content },
    { suggestedName: "优化配方结果" }
  );
};

// 从动态加载器获取优化算法
const loadOptimizers = () => {
  try {
    optimizerOptions.value = getSingleTargetAlgorithms();
    if (optimizerOptions.value.length === 0) {
      console.warn("No single-target optimization algorithms found");
      ElMessage.warning("未找到可用的单目标优化算法");
    } else {
      console.log(`Loaded ${optimizerOptions.value.length} single-target optimization algorithms:`,
        optimizerOptions.value.map(m => m.id).join(', '));
      
      // 默认选择遗传算法
      const geneticAlgorithm = optimizerOptions.value.find(opt => opt.id === 'GeneticAlgorithm');
      if (geneticAlgorithm) {
        selectedOptimizer.value = 'GeneticAlgorithm';
        console.log('Default selected: GeneticAlgorithm');
      } else {
        // 如果找不到遗传算法，则选择第一个可用的算法
        selectedOptimizer.value = optimizerOptions.value[0].id;
        console.log('GeneticAlgorithm not found, selected first available:', optimizerOptions.value[0].id);
      }
    }
  } catch (error) {
    console.error("Error loading single-target optimization algorithms:", error);
    ElMessage.error("加载单目标优化算法失败");
  }
};

const startOptimization = async () => {
  if (disableOptimization.value) {
    ElMessage.warning("请选择预测模型和优化算法，并设置有效的目标值");
    return;
  }

  resultData.value = [];
  progress.value = 0;
  optimizationInProgress.value = true;
  emit('update:optimization-status', true);

  let params = {};
  try {
    if (modelParamsSettingRef.value?.getParams) {
      params = modelParamsSettingRef.value.getParams();
    } else {
      console.warn("无法获取优化参数，使用默认参数");
    }
  } catch (error) {
    console.error("Error getting optimization parameters:", error);
    ElMessage.warning("获取优化参数时出错，将使用默认参数");
  }

  const loadingInstance = ElLoading.service({
    lock: true, text: '优化中，请稍候...', background: 'rgba(0, 0, 0, 0.7)',
  });

  const workspacePath = workspaceStore.getCurrentWorkspacePath || '';
  const workspaceName = workspacePath
  ? (workspacePath.replace(/[\\/]+$/, '').split(/[\\/]/).pop() || '')
  : '';

  try {
    const response = await runOptimizationSingle({
      modelUid: selectedModel.value,
      target: { name: optimizationTargetLabel.value, value: targetValue.value },
      search: {
        algorithm: selectedOptimizer.value,
        params: { ...params, criterion: criterion.value }
      },
      featureRanges: featureRanges.value.reduce((acc, range) => {
        acc[range.featureName] = { min: range.minValue, max: range.maxValue };
        return acc;
      }, {} as Record<string, {min: number; max: number }>),
      path: workspaceName,
    });

    if (response.code === 200) {
      const taskUid = response.data.uid;
      const socket = getSocket();
      socket.off("search_info");
      socket.emit("get_search_info", { uid: taskUid });
      socket.on("search_info", (data) => {
        if (typeof data === "string") data = JSON.parse(data);
        handleSearchInfo(data);
      });
      progress.value = 1;
    } else {
      ElMessage.error(`优化失败: ${response.msg || '未知错误'}`);
      optimizationInProgress.value = false;
      emit('update:optimization-status', false);
    }
  } catch (error) {
    console.error("Optimization error:", error);
    ElMessage.error('优化请求失败，请重试');
    optimizationInProgress.value = false;
    emit('update:optimization-status', false);
  } finally {
    loadingInstance.close();
  }
};

const handleFeatureRangesClick = () => {
  if (!selectedModel.value) {
    ElMessage.warning("请先选择预测模型");
    return;
  }
  activeSelectionTab.value = 'featureRanges';
};

const handleModelChange = () => {
  resultData.value = [];
  tableColumns.value = [];
  
  // 重置优化目标相关数据
  optimizationTargetOptions.value = [];
  selectedOptimizationTarget.value = "";
  
  if (selectedModel.value) {
    const modelData = modelOptions.value.find(m => m.value === selectedModel.value);
    if (modelData) {
      // 处理targetName
      let targets: string[] = [];
      
      if (Array.isArray(modelData.targetName)) {
        targets = modelData.targetName;
      } else if (typeof modelData.targetName === 'string') {
        // 尝试解析字符串形式的数组
        try {
          const parsed = JSON.parse(modelData.targetName);
          if (Array.isArray(parsed)) {
            targets = parsed;
          } else {
            targets = [modelData.targetName];
          }
        } catch {
          targets = [modelData.targetName];
        }
      } else {
        targets = ['未知目标'];
      }
      
      // 设置优化目标选项
      if (targets.length > 1) {
        optimizationTargetOptions.value = targets;
        selectedOptimizationTarget.value = targets[0];
        optimizationTargetLabel.value = targets[0];
      } else {
        optimizationTargetLabel.value = targets[0] || "-";
      }
      
      // 计算初始目标值（基于选择的目标）
      updateTargetValueRange();

      // 更新自变量范围
      if (modelData.featureRanges && Object.keys(modelData.featureRanges).length > 0) {
        // 保持原始顺序，不使用 Object.entries
        const featureNames = Object.keys(modelData.featureRanges);
        featureRanges.value = featureNames.map(name => ({
          featureName: name,
          minValue: modelData.featureRanges[name].min,
          maxValue: modelData.featureRanges[name].max
        }));
      } else {
        featureRanges.value = [];
        ElMessage.info("该模型暂无自变量范围数据");
      }

    } else {
      [optimizationTargetLabel.value, targetValue.value, minTargetValue.value, maxTargetValue.value] = ["-", 0, 0, 100];
      featureRanges.value = [];
    }
  } else {
    [optimizationTargetLabel.value, targetValue.value, minTargetValue.value, maxTargetValue.value] = ["-", 0, 0, 100];
    featureRanges.value = [];
    
    // 如果当前在自变量范围标签页，切换回设置页
    if (activeSelectionTab.value === 'featureRanges') {
      activeSelectionTab.value = 'settings';
    }
  }
};

const updateTargetValueRange = () => {
  if (!selectedModel.value) return;
  
  const modelData = modelOptions.value.find(m => m.value === selectedModel.value);
  if (!modelData || !modelData.targetValues) {
    [minTargetValue.value, maxTargetValue.value, targetValue.value] = [0, 100, 0];
    return;
  }
  
  // 获取当前选择的目标索引
  let targetIndex = 0;
  if (isOptimizationTargetArray.value && selectedOptimizationTarget.value) {
    targetIndex = optimizationTargetOptions.value.indexOf(selectedOptimizationTarget.value);
    if (targetIndex < 0) targetIndex = 0;
  }
  
  // 处理targetValues
  const values: number[] = [];
  
  if (Array.isArray(modelData.targetValues)) {
    modelData.targetValues.forEach(item => {
      if (Array.isArray(item)) {
        // targetValues是二维数组的情况
        if (item.length > targetIndex) {
          const value = item[targetIndex];
          if (typeof value === 'number' && !isNaN(value)) {
            values.push(value);
          }
        }
      } else if (typeof item === 'number' && !isNaN(item)) {
        // targetValues是一维数组的情况（向后兼容）
        values.push(item);
      }
    });
  }
  
  if (values.length > 0) {
    minTargetValue.value = Math.min(...values);
    maxTargetValue.value = Math.max(...values);
    // 计算平均值作为默认值
    const sum = values.reduce((acc, val) => acc + val, 0);
    targetValue.value = parseFloat((sum / values.length).toFixed(1));
  } else {
    [minTargetValue.value, maxTargetValue.value, targetValue.value] = [0, 100, 0];
  }
};

const disableOptimization = computed(() => {
  return !selectedModel.value || !selectedOptimizer.value || isNaN(targetValue.value) || props.loading || optimizationInProgress.value;
});

// 模型搜索过滤函数
const filterModels = (query: string) => {
  if (!query) {
    filteredModelOptions.value = modelOptions.value;
    return;
  }
  
  const lowerQuery = query.toLowerCase();
  filteredModelOptions.value = modelOptions.value.filter(model => {
    // 搜索模型名称
    if (model.label.toLowerCase().includes(lowerQuery)) {
      return true;
    }
    // 搜索优化目标
    const targetNameStr = Array.isArray(model.targetName)
      ? model.targetName.join(' ')
      : String(model.targetName);
    if (targetNameStr.toLowerCase().includes(lowerQuery)) {
      return true;
    }
    // 搜索模型ID
    if (model.id.toString().includes(lowerQuery)) {
      return true;
    }
    return false;
  });
};

onMounted(async () => {
  try {
    await dynamicModelManager.initialize();
    loadOptimizers();
    tableColumns.value = [];
  } catch (error) {
    console.error("Error initializing optimization page:", error);
    ElMessage.error("初始化页面失败");
  }
});

watch(selectedModel, handleModelChange);

onBeforeUnmount(() => {
  try {
    getSocket().off("search_info");
  } catch (error) {
    console.error("Error cleaning up socket listeners:", error);
  }
});
</script>

<style scoped>
.single-target-optimization {
  width: 100%;
}

.main-config-card {
  margin: 10px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.section-header .section-title {
  margin-bottom: 0;
}

.content-bg {
  background-color: var(--el-bg-color, #ffffff);
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0px 0px 12px rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 16px;
}

.form-item-label {
  margin-bottom: 8px;
  font-size: 14px;
  color: var(--el-text-color-regular);
}

.optimization-target-label {
  font-size: 14px;
  color: var(--el-text-color-regular);
  white-space: nowrap;
  display: flex;
  align-items: center;
  height: 32px;
}

.target-info {
  display: flex;
  align-items: center;
  height: 32px;
}

.info-icon {
  margin-left: 8px;
  color: var(--el-text-color-secondary);
  cursor: help;
}

.mt-4 {
  margin-top: 1.5rem;
}

.result-table {
  width: 100%;
  overflow-x: auto;
  flex: 1;
}

.setting-section,
.params-section,
.result-section {
  margin: 20px 10px;
  box-sizing: border-box;
  overflow-x: hidden;
}

.result-section {
  display: flex;
  flex-direction: column;
  flex: 1;
  min-height: 0;
}

.params-section :deep(.params-setting-container) {
  display: flex !important;
  flex-wrap: wrap !important;
  background-color: transparent !important;
}

.params-section :deep(.param-card) {
  flex: 1 1 45% !important;
  margin: 10px !important;
}

.result-table {
  margin-bottom: 20px;
}

:deep(.el-progress) {
  &.el-progress--circle {
    .el-progress__text {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      font-size: 14px !important;
      font-weight: 600;
      color: var(--el-text-color-primary);
      line-height: 1;
      text-align: center;
      width: 100%;
      margin: 0 !important;
    }
  }
}

.selection-controls {
  display: flex;
  gap: 12px;
  margin-bottom: 24px;
}

.selection-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 184px;
  height: 36px;
  border-radius: 24px;
  border: none;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
  background: rgba(206, 206, 206, 0.1);
  color: #666;
}

.selection-button.is-active {
  background: rgba(0, 93, 255, 0.1);
  border: 1px solid #005DFF;
  color: #005DFF;
  font-weight: 500;
}

.selection-button:disabled {
  cursor: not-allowed;
  opacity: 0.5;
  background: rgba(206, 206, 206, 0.1);
  color: #999;
}

.selection-button:disabled:hover {
  background: rgba(206, 206, 206, 0.1);
  color: #999;
}

.btn-icon {
  width: 16px;
  height: 16px;
  margin-right: 8px;
}

.feature-ranges-section {
  margin: 20px 10px;
  box-sizing: border-box;
  overflow-x: hidden;
}

.feature-ranges-table {
  margin-top: 16px;
  border: 1px solid var(--el-border-color-light);
  border-radius: 8px;
  max-height: 400px;
  overflow-y: scroll;
}


/* 自定义滚动条样式 */
.feature-ranges-table::-webkit-scrollbar {
  width: 6px;
}

.feature-ranges-table::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.feature-ranges-table::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.feature-ranges-table::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Styles for table header and rows (similar to multi-target optimization) */
.models-header {
  display: flex;
  background-color: var(--el-fill-color-light);
  border-bottom: 1px solid var(--el-border-color-light);
}

.header-cell {
  padding: 12px 16px;
  font-weight: 600;
  justify-content: center;
  text-align: center;
  color: var(--el-text-color-primary);
  border-right: 1px solid var(--el-border-color-light);
}

.header-cell:last-child {
  border-right: none;
}

.model-row {
  display: flex;
  border-bottom: 1px solid var(--el-border-color-light);
}

.model-row:last-child {
  border-bottom: none;
}

.model-cell {
  padding: 12px 16px;
  display: flex;
  align-items: center;
  border-right: 1px solid var(--el-border-color-light);
  justify-content: center;
  text-align: center;
}

.model-cell:last-child {
  border-right: none;
}

/* Specific column widths for feature ranges table */
.feature-ranges-table .feature-name {
  flex: 2;
  min-width: 200px;
}

.feature-ranges-table .min-value,
.feature-ranges-table .max-value {
  flex: 1;
  min-width: 120px;
}
</style>
