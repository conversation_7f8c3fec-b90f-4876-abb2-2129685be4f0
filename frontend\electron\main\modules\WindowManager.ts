import { BrowserWindow } from "electron";
import { join } from "node:path";

/**
 * 窗口管理器 - 负责创建和管理各种窗口
 */
export class WindowManager {
  private mainWindow: BrowserWindow | null = null;
  private loadingWindow: BrowserWindow | null = null;
  private allWindows = new Map<string, BrowserWindow>();
  private taskWindowMap = new Map<string, BrowserWindow>();
  private preFetchedResults = new Map<string, any>();
  private pendingModelCompleteEvents = new Map<string, any>();

  private preload = join(__dirname, "../preload/index.js");
  private url = process.env.VITE_DEV_SERVER_URL;
  private indexHtml = join(process.env.DIST, "index.html");

  /**
   * 创建闪屏窗口
   */
  createLoadingWindow() {
    const loadingPreload = join(__dirname, "../preload/loading.js");

    this.loadingWindow = new BrowserWindow({
      width: 450,
      height: 300,
      icon: join(process.env.PUBLIC, "favicon.ico"),
      frame: false,
      transparent: true,
      resizable: false,
      webPreferences: {
        preload: loadingPreload,
        contextIsolation: true,
        nodeIntegration: false,
      },
    });

    const loadingHtmlPath = join(process.env.PUBLIC, "loading.html");
    this.loadingWindow.loadFile(loadingHtmlPath);

    this.loadingWindow.on("closed", () => {
      this.loadingWindow = null;
    });

    return this.loadingWindow;
  }

  /**
   * 创建主窗口
   */
  async createMainWindow(initialRoute?: string) {
    this.mainWindow = new BrowserWindow({
      show: false,
      width: 1024,
      height: 768,
      minWidth: 1024,
      minHeight: 768,
      title: "ML Desktop",
      icon: join(process.env.PUBLIC, "favicon.ico"),
      frame: false,
      transparent: true,
      resizable: true,
      webPreferences: {
        preload: this.preload,
        nodeIntegration: false,
        contextIsolation: true,
      },
    });

    const targetUrl = initialRoute ? `${this.url}#${initialRoute}` : this.url;
    const targetIndexHtml = initialRoute
      ? { pathname: this.indexHtml, hash: initialRoute }
      : this.indexHtml;

    if (process.env.VITE_DEV_SERVER_URL) {
      this.mainWindow.loadURL(targetUrl);
      this.mainWindow.webContents.openDevTools({ mode: "bottom" });
    } else {
      this.mainWindow.loadFile(
        typeof targetIndexHtml === "string"
          ? targetIndexHtml
          : targetIndexHtml.pathname,
        typeof targetIndexHtml === "string"
          ? {}
          : { hash: targetIndexHtml.hash },
      );
    }

    // 设置窗口打开处理器
    this.mainWindow.webContents.setWindowOpenHandler(({ url }) => {
      const isModelResultWindow = url.includes("/modelManagement/");
      const childWindow = new BrowserWindow({
        width: 1024,
        height: 768,
        minWidth: 1024,
        minHeight: 768,
        autoHideMenuBar: true,
        frame: !isModelResultWindow,
        transparent: isModelResultWindow,
        backgroundColor: isModelResultWindow ? "#00000000" : "#fff",
        icon: join(process.env.PUBLIC, "favicon.ico"),
        webPreferences: {
          preload: this.preload,
          nodeIntegration: false,
          contextIsolation: true,
        },
        ...(isModelResultWindow
          ? {
              show: false,
            }
          : {}),
      });

      childWindow.loadURL(url);

      // 设置拖拽优化逻辑
      this.setupDragOptimization(childWindow);

      if (isModelResultWindow) {
        childWindow.once("ready-to-show", () => {
          childWindow.show();
        });
      }

      return { action: "deny" };
    });

    // 设置主窗口拖拽优化逻辑
    this.setupDragOptimization(this.mainWindow);

    // 设置窗口状态事件监听
    this.setupWindowStateEvents(this.mainWindow);

    return this.mainWindow;
  }

  /**
   * 创建子窗口
   */
  createChildWindow(url: string): BrowserWindow {
    const childWindow = new BrowserWindow({
      icon: join(process.env.PUBLIC, "favicon.ico"),
      webPreferences: {
        preload: this.preload,
        nodeIntegration: false,
        contextIsolation: true,
      },
    });

    if (process.env.VITE_DEV_SERVER_URL) {
      childWindow.loadURL(`${this.url}#${url}`);
    } else {
      childWindow.loadFile(this.indexHtml, { hash: url });
    }

    // 设置拖拽优化逻辑
    this.setupDragOptimization(childWindow);

    return childWindow;
  }

  /**
   * 获取主窗口
   */
  getMainWindow(): BrowserWindow | null {
    return this.mainWindow;
  }

  /**
   * 获取加载窗口
   */
  getLoadingWindow(): BrowserWindow | null {
    return this.loadingWindow;
  }

  /**
   * 关闭加载窗口
   */
  closeLoadingWindow() {
    if (this.loadingWindow && !this.loadingWindow.isDestroyed()) {
      this.loadingWindow.close();
      this.loadingWindow = null;
    }
  }

  /**
   * 显示主窗口
   */
  showMainWindow() {
    if (this.mainWindow && !this.mainWindow.isDestroyed()) {
      this.mainWindow.show();
      this.mainWindow.maximize();
    }
  }

  /**
   * 注册任务窗口映射
   */
  registerTaskWindow(uid: string, window: BrowserWindow) {
    this.taskWindowMap.set(uid, window);
  }

  /**
   * 获取任务窗口
   */
  getTaskWindow(uid: string): BrowserWindow | undefined {
    return this.taskWindowMap.get(uid);
  }

  /**
   * 缓存预取结果
   */
  cachePreFetchedResult(uid: string, result: any) {
    this.preFetchedResults.set(uid, result);
  }

  /**
   * 获取预取结果
   */
  getPreFetchedResult(uid: string): any {
    return this.preFetchedResults.get(uid);
  }

  /**
   * 删除预取结果
   */
  deletePreFetchedResult(uid: string) {
    this.preFetchedResults.delete(uid);
  }

  /**
   * 缓存待处理的模型完成事件
   */
  cachePendingModelCompleteEvent(uid: string, data: any) {
    this.pendingModelCompleteEvents.set(uid, data);
  }

  /**
   * 获取待处理的模型完成事件
   */
  getPendingModelCompleteEvent(uid: string): any {
    return this.pendingModelCompleteEvents.get(uid);
  }

  /**
   * 删除待处理的模型完成事件
   */
  deletePendingModelCompleteEvent(uid: string) {
    this.pendingModelCompleteEvents.delete(uid);
  }

  /**
   * 窗口控制 - 最小化
   */
  minimizeWindow(window?: BrowserWindow) {
    const targetWindow = window || this.mainWindow;
    if (targetWindow && !targetWindow.isDestroyed()) {
      targetWindow.minimize();
    }
  }

  /**
   * 窗口控制 - 最大化/还原
   */
  toggleMaximizeWindow(window?: BrowserWindow) {
    const targetWindow = window || this.mainWindow;
    if (targetWindow && !targetWindow.isDestroyed()) {
      if (targetWindow.isMaximized()) {
        targetWindow.unmaximize();
      } else {
        targetWindow.maximize();
      }
    }
  }

  /**
   * 窗口控制 - 关闭
   */
  closeWindow(window?: BrowserWindow) {
    const targetWindow = window || this.mainWindow;
    if (targetWindow && !targetWindow.isDestroyed()) {
      targetWindow.close();
    }
  }

  /**
   * 设置窗口拖拽优化逻辑
   * 当最大化窗口被拖拽时，先还原窗口大小再移动
   */
  private setupDragOptimization(window: BrowserWindow) {
    if (!window || window.isDestroyed()) return;

    let isProcessingMove = false;

    // 使用 will-move 事件来处理拖拽优化
    window.on("will-move", (event) => {
      if (window.isMaximized() && !isProcessingMove) {
        // 防止递归调用
        isProcessingMove = true;

        // 阻止默认移动行为
        event.preventDefault();

        // 先还原窗口
        window.unmaximize();

        // 在下一个事件循环中重置标志位
        // 这样可以确保 unmaximize 完成后再重置
        process.nextTick(() => {
          isProcessingMove = false;
        });
      }
    });

    // 监听窗口状态变化，确保标志位正确重置
    window.on("unmaximize", () => {
      // 当窗口从最大化状态还原时，确保标志位被重置
      isProcessingMove = false;
    });
  }

  /**
   * 设置窗口状态事件监听
   * 向渲染进程发送窗口状态变化事件
   */
  private setupWindowStateEvents(window: BrowserWindow) {
    if (!window || window.isDestroyed()) return;

    // 监听窗口最大化事件
    window.on("maximize", () => {
      window.webContents.send("window-maximized");
    });

    // 监听窗口还原事件
    window.on("unmaximize", () => {
      window.webContents.send("window-unmaximized");
    });

    // 监听窗口还原事件（从最小化状态）
    window.on("restore", () => {
      window.webContents.send("window-restored");
    });
  }

  /**
   * 清理资源
   */
  cleanup() {
    this.allWindows.clear();
    this.taskWindowMap.clear();
    this.preFetchedResults.clear();
    this.pendingModelCompleteEvents.clear();
  }
}
