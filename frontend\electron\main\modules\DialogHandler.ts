import { dialog, type BrowserWindow } from "electron";
import fs from "fs";

/**
 * 对话框处理器 - 负责处理各种系统对话框
 */
export class DialogHandler {
  /**
   * 选择目录对话框
   */
  async openDirectory() {
    const result = await dialog.showOpenDialog({
      properties: ["openDirectory"],
    });
    return result.filePaths[0];
  }

  /**
   * 选择文件对话框
   */
  async openFile() {
    const result = await dialog.showOpenDialog({
      properties: ["openFile"],
    });
    if (result.canceled || result.filePaths.length === 0) {
      return null;
    }
    return result.filePaths[0];
  }

  /**
   * 选择保存文件对话框
   */
  async saveFile(options: any) {
    const result = await dialog.showSaveDialog({
      ...options,
      filters: [
        { name: "JSON Files", extensions: ["json"] },
        { name: "All Files", extensions: ["*"] },
      ],
    });
    return result;
  }

  /**
   * 项目导出对话框
   */
  async exportProject(projectData: any) {
    try {
      const result = await dialog.showSaveDialog({
        title: "导出项目",
        defaultPath: `project_${new Date().toISOString().slice(0, 19).replace(/:/g, "-")}.json`,
        filters: [
          { name: "JSON Files", extensions: ["json"] },
          { name: "All Files", extensions: ["*"] },
        ],
      });

      if (result.canceled || !result.filePath) {
        return { success: false, message: "用户取消导出" };
      }

      await fs.promises.writeFile(
        result.filePath,
        JSON.stringify(projectData, null, 2),
        "utf-8",
      );
      return {
        success: true,
        filePath: result.filePath,
        message: "项目导出成功",
      };
    } catch (error) {
      console.error("导出项目失败:", error);
      return { success: false, message: `导出失败` };
    }
  }

  /**
   * 项目导入对话框
   */
  async importProject() {
    try {
      const result = await dialog.showOpenDialog({
        title: "导入项目",
        properties: ["openFile"],
        filters: [
          { name: "项目文件", extensions: ["zip"] },
          { name: "All Files", extensions: ["*"] },
        ],
      });

      if (result.canceled || result.filePaths.length === 0) {
        return { success: false, message: "用户取消导入" };
      }

      const filePath = result.filePaths[0];
      // 新版二进制格式导入
      const fileBuffer = await fs.promises.readFile(filePath);
      return {
        success: true,
        fileBuffer,
        filePath,
        message: "项目文件读取成功",
      };
    } catch (error) {
      console.error("导入项目失败:", error);
      return { success: false, message: `导入失败` };
    }
  }

  /**
   * 选择新的文件路径
   */
  async selectNewPath(options: any) {
    const result = await dialog.showOpenDialog({
      title: options.title || "选择新路径",
      properties: options.properties || ["openDirectory"],
      defaultPath: options.defaultPath || "",
    });

    if (result.canceled || result.filePaths.length === 0) {
      return null;
    }

    return result.filePaths[0];
  }

  /**
   * 显示打开对话框 (带窗口上下文)
   */
  async showOpenDialog(window: BrowserWindow, options: any) {
    if (!window) {
      return { canceled: true, filePaths: [] };
    }
    return await dialog.showOpenDialog(window, options);
  }

  /**
   * 显示保存对话框 (带窗口上下文)
   */
  async showSaveDialog(window: BrowserWindow, options: any) {
    if (!window) {
      return { canceled: true, filePath: "" };
    }
    return await dialog.showSaveDialog(window, options);
  }

  /**
   * 显示消息框
   */
  async showMessageBox(window: BrowserWindow, options: any) {
    if (!window) {
      return { response: 0 };
    }
    return await dialog.showMessageBox(window, options);
  }

  /**
   * 显示错误对话框
   */
  showErrorBox(title: string, content: string) {
    dialog.showErrorBox(title, content);
  }
}
