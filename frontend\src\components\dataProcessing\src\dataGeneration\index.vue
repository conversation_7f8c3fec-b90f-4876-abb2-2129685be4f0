<template>
  <div>
    <el-tooltip content="虚拟样本生成" placement="bottom">
      <el-button
        circle
        :disabled="props.disabled"
        class="menu-button"
        @click="openDialog"
      >
        <SampleIcon class="menu-icon normal" />
        <SampleSelectedIcon class="menu-icon hover" />
      </el-button>
    </el-tooltip>

    <el-dialog
      v-model="isDialogVisible"
      title="虚拟样本生成"
      width="90%"
      append-to-body
      class="virtual-sample-dialog"
    >
      <div class="virtual-sample-generator">
        <!-- 左侧工具箱 -->
        <ToolboxPanel
          :columns="props.columns"
          @item-click="handleItemClick"
        />

        <!-- 中间约束构建区 -->
        <ConstraintsPanel
          ref="constraintsPanelRef"
          v-model="constraints"
          @active-constraint-change="handleActiveConstraintChange"
        />

        <!-- 右侧生成设置区 -->
        <GenerationSettingsPanel
          v-model="generationParams"
          :available-algorithms="availableAlgorithms"
          :valid-constraints-count="validConstraintsCount"
          :columns-length="props.columns.length"
          :enabled-features-count="enabledFeaturesCount"
          @algorithm-change="handleAlgorithmChange"
          @show-advanced-settings="showAdvancedSettings = true"
        />
      </div>

      <template #footer>
        <el-button @click="isDialogVisible = false">取消</el-button>
        <el-button 
          type="primary" 
          :loading="isSubmitting"
          :disabled="isSubmitDisabled"
          @click="handleSubmit"
        >
          {{ isSubmitting ? '生成中...' : '生成虚拟样本' }}
        </el-button>
      </template>
    </el-dialog>

    <!-- 结果展示对话框 -->
    <ResultDialog
      v-model="isResultVisible"
      :result-data="resultData"
      @export-data="exportGeneratedSamples"
      @import-as-temp-file="handleImportAsTempFile"
      @save-to-workspace="handleSaveToWorkspace"
    />

    <!-- 高级设置对话框 -->
    <AdvancedSettingsDialog
      v-model="showAdvancedSettings"
      :advanced-settings="advancedSettings"
      :columns="props.columns"
      @update:advanced-settings="advancedSettings = $event"
      @apply="applyAdvancedSettings"
      @reset="resetAdvancedSettings"
    />
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, computed } from "vue";
import type { PropType } from "vue";
import { ElMessage } from "element-plus";
import SampleIcon from "@/assets/svg/sample.svg?component";
import SampleSelectedIcon from "@/assets/svg/sample_selected.svg?component";
import type { TableColumn } from "@/components/dataProcessing/src/dataTable";
import { reqVirtualSamplesGeneration, type VirtualSamplesGenerationRequest } from "@/api/models/virtualSamplesGeneration";
import { getGenerationAlgorithms, initializeModelManager } from "@/utils/dynamicModelLoader";
import { exportSingleSheet } from "@/utils/exportUtils";

// 导入拆分后的组件
import ToolboxPanel from "./components/ToolboxPanel.vue";
import ConstraintsPanel from "./components/ConstraintsPanel.vue";
import GenerationSettingsPanel from "./components/GenerationSettingsPanel.vue";
import ResultDialog from "./components/ResultDialog.vue";
import AdvancedSettingsDialog from "./components/AdvancedSettingsDialog.vue";

// 导入类型定义
import type { 
  Constraint, 
  ClickableItem, 
  GenerationParams, 
  AdvancedSettings, 
  FeatureRange, 
  ResultData, 
  AlgorithmConfig 
} from "./types";

// Emits 定义
const emit = defineEmits<{
  'import-as-temp-file': [data: any[][], columns: any[]]
  'save-to-workspace': [data: any[][], columns: any[]]
}>();

// Props 定义
const props = defineProps({
  disabled: { type: Boolean, default: false },
  columns: { type: Array as PropType<TableColumn[]>, default: () => [] },
  tableData: { type: Array as PropType<any[][]>, default: () => [] },
});

// 响应式数据
const isDialogVisible = ref(false);
const isResultVisible = ref(false);
const isSubmitting = ref(false);
const showAdvancedSettings = ref(false);
const constraints = ref<Constraint[]>([]);
const generationParams = ref<GenerationParams>({
  algorithm: '',
  algorithmParams: {},
});
const resultData = ref<ResultData | null>(null);
const advancedSettings = ref<AdvancedSettings>({ ranges: {} });
const availableAlgorithms = ref<AlgorithmConfig[]>([]);
const constraintsPanelRef = ref<InstanceType<typeof ConstraintsPanel>>();

// 计算属性
const validConstraintsCount = computed(() => 
  constraints.value.filter(c => c.latex.trim().length > 0).length
);

const enabledFeaturesCount = computed(() => {
  return Object.keys(advancedSettings.value.ranges).filter(key => 
    advancedSettings.value.ranges[key] !== undefined
  ).length;
});

const calculatedSampleCount = computed(() => {
  if (generationParams.value.algorithm !== 'GridGeneration') return 0;
  
  const ranges = advancedSettings.value.ranges;
  const enabledRanges = Object.values(ranges).filter(range => range !== undefined);
  
  if (enabledRanges.length === 0) {
    // 使用全局网格步数计算
    const gridCount = generationParams.value.algorithmParams.grid_count || 10;
    return Math.pow(gridCount, props.columns.length);
  }
  
  // 使用高级设置计算
  return enabledRanges.reduce((total, range) => {
    return total * (range.count || 10);
  }, 1);
});

// 提交按钮禁用逻辑
const isSubmitDisabled = computed(() => {
  // 如果没有选择算法，禁用
  if (!generationParams.value.algorithm) return true;
  
  // 如果没有表格数据，禁用
  if (!props.tableData || props.tableData.length === 0) return true;
  
  // 计算当前算法的样本数量
  let sampleCount = 0;
  if (generationParams.value.algorithm === 'GridGeneration') {
    sampleCount = calculatedSampleCount.value;
  } else if (generationParams.value.algorithm === 'RandomGeneration') {
    sampleCount = generationParams.value.algorithmParams.sample_count || 0;
  }
  
  // 如果样本数量超过10000，禁用
  return sampleCount > 10000;
});

// 方法
const openDialog = () => {
  if (props.disabled) return ElMessage.warning("请先加载数据");
  isDialogVisible.value = true;
};

// 处理工具箱项目点击
const handleItemClick = (item: ClickableItem) => {
  const success = constraintsPanelRef.value?.insertToActiveConstraint(item);
  if (!success) {
    ElMessage.warning("请先选择一个约束条件框");
  }
};

// 处理活动约束条件变化
const handleActiveConstraintChange = (index: number | null) => {
  // 这里可以添加额外的逻辑，比如更新UI状态
  console.log(`活动约束条件索引: ${index}`);
};

// 初始化算法配置
const initializeAlgorithms = async () => {
  try {
    // 确保动态模型管理器已初始化
    await initializeModelManager();
    
    // 直接使用 dynamicModelLoader 获取生成算法
    const algorithms = getGenerationAlgorithms();
    availableAlgorithms.value = algorithms;
    
    console.log(`Successfully loaded ${algorithms.length} generation algorithms`);
    
    // 设置默认算法
    if (algorithms.length > 0) {
      generationParams.value.algorithm = algorithms[0].id;
      initializeAlgorithmParams(algorithms[0]);
    }
  } catch (error) {
    console.error("Failed to load generation algorithms:", error);
    ElMessage.error("加载生成算法配置失败");
  }
};

// 初始化算法参数
const initializeAlgorithmParams = (algorithm: AlgorithmConfig) => {
  if (!algorithm?.config?.defaultParams) return;
  
  const params: Record<string, any> = {};
  Object.entries(algorithm.config.defaultParams).forEach(([key, param]: [string, any]) => {
    params[key] = param.value;
  });
  
  generationParams.value.algorithmParams = params;
};

// 处理算法变化
const handleAlgorithmChange = (algorithmId: string) => {
  const algorithm = availableAlgorithms.value.find(alg => alg.id === algorithmId);
  if (algorithm) {
    initializeAlgorithmParams(algorithm);
    // 切换算法时重置高级设置
    resetAdvancedSettings();
  }
};

// 高级设置相关方法
const getDefaultRange = (): FeatureRange => {
  return {
    min: 0,
    max: 1,
    count: 10
  };
};

const resetAdvancedSettings = () => {
  advancedSettings.value.ranges = {};
};

const applyAdvancedSettings = () => {
  if (calculatedSampleCount.value > 10000) {
    ElMessage.warning('样本数量超过10000，无法应用设置');
    return;
  }
  
  ElMessage.success('高级设置已应用');
  showAdvancedSettings.value = false;
};

// 提交处理
const handleSubmit = async () => {
  // 检查是否被禁用
  if (isSubmitDisabled.value) {
    // 计算当前样本数量用于显示具体的错误信息
    let sampleCount = 0;
    if (generationParams.value.algorithm === 'GridGeneration') {
      sampleCount = calculatedSampleCount.value;
    } else if (generationParams.value.algorithm === 'RandomGeneration') {
      sampleCount = generationParams.value.algorithmParams.sample_count || 0;
    }
    
    if (sampleCount > 10000) {
      return ElMessage.warning(`样本数量(${formatSampleCount(sampleCount)})超过10000，请调整参数后重试`);
    }
    return ElMessage.warning("请检查输入条件");
  }

  const validConstraints = constraints.value
    .filter((c) => c.latex.trim().length > 0)
    .map((c) => c.latex.trim());

  if (!props.tableData || props.tableData.length === 0) {
    return ElMessage.warning("没有可用的表格数据");
  }

  if (!generationParams.value.algorithm) {
    return ElMessage.warning("请选择生成算法");
  }

  try {
    isSubmitting.value = true;
    
    // 准备算法参数
    const algorithmParams = { ...generationParams.value.algorithmParams };
    
    // 准备数据部分，包含高级设置
    const dataSection: any = {
      values: props.tableData,
      columns: props.columns.map(col => col.title || col.data)
    };
    
    // 如果是网格生成法且有高级设置，将ranges添加到data字段中
    if (generationParams.value.algorithm === 'GridGeneration' && enabledFeaturesCount.value > 0) {
      // 将ranges的key从column.data(索引)转换为column.title(列名)
      const convertedRanges: Record<string, FeatureRange> = {};
      Object.entries(advancedSettings.value.ranges).forEach(([columnData, range]) => {
        if (range !== undefined) {
          // 根据column.data找到对应的column.title
          const column = props.columns.find(col => col.data === columnData);
          if (column && column.title) {
            convertedRanges[column.title] = range;
          }
        }
      });
      dataSection.ranges = convertedRanges;
    }
    
    // 根据新的接口定义构建请求数据
    const requestData: VirtualSamplesGenerationRequest = {
      constraints: validConstraints,
      data: dataSection,
      generation: {
        method: generationParams.value.algorithm,
        params: algorithmParams
      }
    };

    console.log("发送虚拟样本生成请求:", requestData);

    const response = await reqVirtualSamplesGeneration(requestData);
    
    if (response.code === 200) {
      // 根据新的响应接口处理结果
      const apiData = response.data;
      
      // 检查约束条件是否有错误
      const constraintErrors = apiData.constraints?.filter(c => c.error === true) || [];
      
      if (constraintErrors.length > 0) {
        // 构建错误信息
        const errorMessages = constraintErrors.map(c => {
          if (c.latex) {
            return `表达式 "${c.latex}" 存在问题`;
          }
          return "约束条件存在问题";
        }).join("；");
        
        ElMessage.error(`约束条件验证失败：${errorMessages}`);
        return;
      }
      
      resultData.value = {
        generatedSamples: apiData.data?.values || [],
        originalData: props.tableData,
        constraints: apiData.constraints?.map(c => c.latex).filter(Boolean) || [], // 使用API返回的约束条件
        columns: (apiData.data?.columns || []).map((col: string, index: number) => ({
          data: col,
          title: col,
          index: index
        })),
        metadata: {
          sampleCount: getSampleCountFromShape(apiData.data?.shape),
          generationTime: 0, // 新接口未返回，设为0
          method: apiData.generation?.method || 'Unknown',
          originalRowCount: props.tableData.length
        }
      };
      
      // 添加安全检查，避免访问 undefined 属性
      const sampleCount = getSampleCountFromShape(apiData.data?.shape);
      ElMessage.success(`成功生成 ${sampleCount} 个虚拟样本`);
      
      isDialogVisible.value = false;
      isResultVisible.value = true;
    } else {
      ElMessage.error(response.msg || "虚拟样本生成失败");
    }
  } catch (error) {
    console.error("虚拟样本生成请求失败:", error);
    ElMessage.error("请求失败，请重新启动应用");
  } finally {
    isSubmitting.value = false;
  }
};

// 从shape获取样本数量
const getSampleCountFromShape = (shape: number[] | undefined) => {
  if (!shape || !Array.isArray(shape) || shape.length === 0) return 0;
  // shape[0] 表示行数，即样本数量
  return shape[0] || 0;
};

// 导出结果
const exportGeneratedSamples = async () => {
  if (!resultData.value?.generatedSamples || !resultData.value?.columns) {
    return ElMessage.warning("没有可导出的数据");
  }
  // 准备导出数据
  const exportData = {
    headers: resultData.value.columns.map(col => col.title || col.data),
    content: resultData.value.generatedSamples
  };

  // 生成建议的文件名
  const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
  const suggestedName = `虚拟样本生成结果_${timestamp}`;

  // 使用 exportUtils 导出数据
  await exportSingleSheet(exportData, {
    suggestedName,
    sheetName: '虚拟样本',
    exportType: 'excel'
  });
};

// 将样本数量转换为科学计数法格式
const formatSampleCount = (count: number) => {
  if (count >= 1000) {
    return count.toExponential(1);
  }
  return count.toString();
};

// 导入临时文件
const handleImportAsTempFile = (data: any[][], columns: any[]) => {
  // 通过emit将数据传递给父组件
  emit('import-as-temp-file', data, columns);
};

// 保存到当前工作区
const handleSaveToWorkspace = (data: any[][], columns: any[]) => {
  // 通过emit将数据传递给父组件
  emit('save-to-workspace', data, columns);
};

// 生命周期
onMounted(() => {
  initializeAlgorithms();
});
</script>

<style scoped>
/* 对话框样式 */
.virtual-sample-dialog :deep(.el-dialog__body) {
  padding: 20px;
  height: 80vh;
  overflow: hidden;
}

.virtual-sample-generator {
  display: flex;
  gap: 20px;
  height: 100%;
  min-height: 600px;
}

/* 按钮样式优化 */
.menu-button {
  width: 36px; 
  height: 36px; 
  border-radius: 2px; 
  background: #ffffff;
  box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.08); 
  position: relative;
  display: flex; 
  align-items: center; 
  justify-content: center;
}

.menu-button:hover .menu-icon.normal, 
.menu-button.is-active .menu-icon.normal { 
  opacity: 0; 
}

.menu-button:hover .menu-icon.hover, 
.menu-button.is-active .menu-icon.hover { 
  opacity: 1; 
}

.menu-icon {
  width: 22px; 
  height: 22px; 
  position: absolute; 
  top: 50%; 
  left: 50%;
  transform: translate(-50%, -50%); 
  transition: opacity 0.2s ease;
}

.menu-icon.normal { 
  opacity: 1; 
}

.menu-icon.hover { 
  opacity: 0; 
}
</style>
