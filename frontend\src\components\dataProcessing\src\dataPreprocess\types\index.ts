/**
 * 数据预处理对话框模块类型定义
 */

// 预处理配置
export interface PreprocessConfig {
  dataset: {
    meta: {
      headers: string[];
    };
    data: any[][];
  };
  preprocess: {
    algorithm: {
      name: string;
      params?: Record<string, any>;
    };
  };
}

// 检测结果
export interface DetectionResult {
  success: boolean;
  labels?: number[];
  totalRows?: number;
  algorithm?: string;
  message?: string;
}
