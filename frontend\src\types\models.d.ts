import type { Dataset } from "@/src/types/dataset";
interface ModelMetaEvaluation {
  cv?: {
    k: number;
    randomState: number;
  };
  loocv?: boolean;
  test?: {
    size: number;
    randomState: number;
  };
  shap?: boolean;
}
interface ModelMeta {
  headers: {
    index: string[];
    target: string[];
    features: string[];
    all: string[];
  };
  evaluation: ModelMetaEvaluation;
  name: string;
  path: string;
}
interface ModelAlgorithm {
  name: string;
  params: Record<string, any>;
}
interface ModelConfig {
  uid?: string;
  dataset: Dataset;
  model: {
    algorithm: ModelAlgorithm;
    meta: ModelMeta;
  };
}

interface RegPredictions {
  obs: number[];
  pred: number[];
  err: number[];
  errPer: number[];
}
interface RegMetrics {
  mae: number;
  mre: number;
  mse: number;
  rmse: number;
  r2: number;
  corr: number;
  maxErr: number;
}
interface RegModelResult {
  train: {
    predictions: RegPredictions;
    metrics: RegMetrics;
  };
  test?: {
    predictions: RegPredictions;
    metrics: RegMetrics;
  };
  cv?: {
    predictions: RegPredictions;
    metrics: RegMetrics;
  };
  loocv?: {
    predictions: RegPredictions;
    metrics: RegMetrics;
  };
}

/**
 * 模型信息接口
 */
interface Model {
  id: number;
  name: string;
  category: string;
  uid: string;
  targetName: string;
  targetValues: number[];
  modelType: string;
  status: "completed" | "running" | "pending" | "upload" | "failed";
  progress: string;
  asynchronous: boolean;
  isRabbitmqReady: boolean;
  createdAt: string;
  updatedAt: string;
  error: string | null;
}

/**
 * 模型列表响应
 */
interface GetModelListResponse {
  code: number;
  data: Model[];
  msg: string;
}

interface MLModelResponse {
  code: number;
  msg: string;
  data: {
    uid: string;
  };
}

interface MLModelUploadResponse {
  code: number;
  msg: string;
  data: {
    file_size: string;
    filename: string;
    model_id: string;
    upload_time: string;
  };
}

interface PredictionResponse {
  code: number;
  msg: string;
  data: {
    predictions: any[];
    label: string;
  };
}

interface ModelParamsResponse {
  code: number;
  msg: string;
  data: ModelConfig;
}

interface GetModelListParams {
  buildDate?: string;
  searchKeyword?: string;
  modelName?: string;
  path?: string;
}

export {
  Model,
  ModelConfig,
  RegModelResult,
  ModelMeta,
  ModelMetaEvaluation,
  ModelAlgorithm,
  GetModelListResponse,
  MLModelResponse,
  MLModelUploadResponse,
  PredictionResponse,
  ModelParamsResponse,
  GetModelListParams,
};
