{"name": "DifferentialEvolution", "displayName": "差分进化算法", "description": "通过差分进化算法设计配方", "type": "single-target-search", "defaultParams": {"populationSize": {"value": 50, "type": "number", "description": "种群大小", "displayName": "种群大小", "min": 20, "max": 200, "step": 10}, "F": {"value": 0.5, "type": "number", "description": "权重因子", "displayName": "权重因子", "min": 0.1, "max": 1.0, "step": 0.05}, "CR": {"value": 0.7, "type": "number", "description": "交叉概率", "displayName": "交叉概率", "min": 0.1, "max": 1.0, "step": 0.05}, "maxIter": {"value": 100, "type": "number", "description": "最大迭代次数", "displayName": "最大迭代次数", "min": 20, "max": 1000, "step": 20}, "maxTime": {"value": 10000, "type": "number", "description": "最大时间(秒)", "displayName": "最大时间(秒)", "min": 10, "max": 10000, "step": 100}}, "tips": ["差分进化算法是一种基于群体进化的优化算法", "DE具有参数少、鲁棒性强的特点", "缩放因子F控制差分向量的影响程度", "交叉概率CR影响新个体的多样性"], "introduction": {"detailedDescription": "差分进化算法（DE）是一种基于群体进化的优化算法，通过差分变异、交叉和选择操作来产生新的候选解。DE具有参数少、鲁棒性强的特点，适用于各种优化问题", "usageTips": ["DE具有参数少、鲁棒性强的特点", "缩放因子F控制差分向量的影响程度，较大的F有利于全局搜索", "交叉概率CR影响新个体的多样性，较大的CR有利于局部搜索", "变异策略的选择影响算法的性能，不同问题适合不同的策略"], "scenarios": "适用于连续优化问题，如参数优化、函数优化、工程设计等", "mainParams": [{"name": "population_size", "description": "种群大小"}, {"name": "max_iter", "description": "最大迭代次数"}, {"name": "F", "description": "缩放因子"}, {"name": "CR", "description": "交叉概率"}, {"name": "strategy", "description": "变异策略"}]}}