<template>
  <el-dialog
    v-model="isVisible"
    title="网格生成法 - 高级设置"
    width="80%"
    append-to-body
    class="advanced-settings-dialog"
  >
    <div class="advanced-settings-content">
      <div class="settings-description">
        <el-alert
          title="高级设置说明"
          description="为每个特征单独设置数值范围和网格数。应用后将覆盖全局网格数设置。"
          type="info"
          :closable="false"
          show-icon
        />
      </div>
      
      <div class="features-grid">
        <div
          v-for="column in columns"
          :key="column.data"
          class="feature-setting-card"
        >
          <div class="feature-header">
            <h6>{{ column.title || column.data }}</h6>
            <el-switch
              :model-value="isFeatureEnabled(column.data)"
              @update:model-value="(value) => handleFeatureToggle(column.data, value)"
            />
          </div>
          
          <div
            v-if="advancedSettings.ranges[column.data]"
            class="feature-inputs"
          >
            <div class="input-group">
              <label>最小值</label>
              <el-input-number
                v-model="advancedSettings.ranges[column.data].min"
                :precision="3"
                size="small"
                class="range-input"
                @change="handleRangeChange"
              />
            </div>
            
            <div class="input-group">
              <label>最大值</label>
              <el-input-number
                v-model="advancedSettings.ranges[column.data].max"
                :precision="3"
                size="small"
                class="range-input"
                @change="handleRangeChange"
              />
            </div>
            
            <div class="input-group">
              <label>网格数</label>
              <el-input-number
                v-model="advancedSettings.ranges[column.data].count"
                :min="2"
                :max="100"
                size="small"
                class="range-input"
                @change="handleRangeChange"
              />
            </div>
          </div>
        </div>
      </div>
      
      <div class="sample-preview">
        <el-card class="preview-card">
          <template #header>
            <div class="card-header">
              <span>样本预览</span>
            </div>
          </template>
          <div class="preview-content">
            <p>启用特征: <strong>{{ enabledFeaturesCount }}</strong> / {{ columns.length }}</p>
            <p>预计样本数: <strong :class="{ 'warning-count': calculatedSampleCount > 10000 }">{{ formatSampleCount(calculatedSampleCount) }}</strong></p>
            <el-alert
              v-if="calculatedSampleCount > 10000"
              title="样本数量过多"
              description="建议减少网格数或特征数量，以免生成时间过长"
              type="warning"
              :closable="false"
              show-icon
            />
          </div>
        </el-card>
      </div>
    </div>
    
    <template #footer>
      <el-button @click="handleReset">重置</el-button>
      <el-button @click="handleCancel">取消</el-button>
      <el-button 
        type="primary" 
        @click="handleApply"
        :disabled="calculatedSampleCount > 10000"
      >
        应用设置
      </el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { computed } from "vue";
import type { PropType } from "vue";
import { ElMessage } from "element-plus";
import type { TableColumn } from "@/components/dataProcessing/src/dataTable";
import type { AdvancedSettings, FeatureRange } from "./types";

// Props 定义
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  advancedSettings: {
    type: Object as PropType<AdvancedSettings>,
    required: true
  },
  columns: {
    type: Array as PropType<TableColumn[]>,
    default: () => []
  }
});

// Emits 定义
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'update:advancedSettings': [value: AdvancedSettings]
  'apply': []
  'reset': []
}>();

// 响应式数据
const isVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
});

// 计算属性
const enabledFeaturesCount = computed(() => {
  return Object.keys(props.advancedSettings.ranges).filter(key => 
    props.advancedSettings.ranges[key] !== undefined
  ).length;
});

const calculatedSampleCount = computed(() => {
  const ranges = props.advancedSettings.ranges;
  const enabledRanges = Object.values(ranges).filter(range => range !== undefined);
  
  if (enabledRanges.length === 0) {
    return 0;
  }
  
  // 使用高级设置计算
  return enabledRanges.reduce((total, range) => {
    return total * (range.count || 10);
  }, 1);
});

// 方法
const getDefaultRange = (): FeatureRange => {
  return {
    min: 0,
    max: 1,
    count: 10
  };
};

const isFeatureEnabled = (featureKey: string): boolean => {
  return props.advancedSettings.ranges[featureKey] !== undefined;
};

const handleFeatureToggle = (featureKey: string, enabled: boolean) => {
  const newSettings = { ...props.advancedSettings };
  
  if (enabled) {
    newSettings.ranges[featureKey] = getDefaultRange();
  } else {
    delete newSettings.ranges[featureKey];
  }
  
  emit('update:advancedSettings', newSettings);
};

const handleRangeChange = () => {
  // 通知父组件更新设置
  emit('update:advancedSettings', props.advancedSettings);
};

const handleReset = () => {
  emit('reset');
};

const handleCancel = () => {
  isVisible.value = false;
};

const handleApply = () => {
  if (calculatedSampleCount.value > 10000) {
    ElMessage.warning('样本数量超过10000，无法应用设置');
    return;
  }
  
  ElMessage.success('高级设置已应用');
  emit('apply');
  isVisible.value = false;
};

// 将样本数量转换为科学计数法格式
const formatSampleCount = (count: number) => {
  if (count >= 1000) {
    return count.toExponential(1);
  }
  return count.toString();
};
</script>

<style scoped>
/* 高级设置弹窗样式 */
.advanced-settings-dialog :deep(.el-dialog__body) {
  padding: 20px;
  max-height: 70vh;
  overflow-y: auto;
}

.advanced-settings-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.settings-description {
  margin-bottom: 10px;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 16px;
  max-height: 400px;
  overflow-y: auto;
  padding: 10px;
  border: 1px solid var(--el-border-color-lighter);
  border-radius: 8px;
  background: #fafbfc;
}

/* 自定义滚动条样式 */
.features-grid::-webkit-scrollbar {
  width: 6px;
}

.features-grid::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.features-grid::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.features-grid::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.feature-setting-card {
  background: white;
  border: 1px solid var(--el-border-color-lighter);
  border-radius: 8px;
  padding: 16px;
  transition: all 0.3s ease;
}

.feature-setting-card:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.feature-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.feature-header h6 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.feature-inputs {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.input-group {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.input-group label {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  font-weight: 500;
}

.range-input {
  width: 100%;
}

.sample-preview {
  margin-top: 10px;
}

.preview-card {
  border-radius: 8px;
}

.card-header {
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.preview-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.preview-content p {
  margin: 0;
  font-size: 14px;
  color: var(--el-text-color-regular);
}

.warning-count {
  color: #faad14 !important;
}
</style>
