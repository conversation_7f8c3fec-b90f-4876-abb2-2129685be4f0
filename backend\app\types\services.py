from typing import Literal, TypedDict, Union, Optional, Dict, Any
from sqlalchemy.orm import DeclarativeBase, Mapped, mapped_column
from sqlalchemy import Integer, String, JSON, DateTime, Boolean, Enum as SQLAlchemyEnum
from datetime import datetime
import json

RabbitmqHost = str
RabbitmqPort = int

class Status(str, SQLAlchemyEnum):
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"

class ModelType(str, SQLAlchemyEnum):
    REGRESSION = "reg"
    CLASSIFICATION = "cls"

class Base(DeclarativeBase):
    def to_json(self) -> str:
        """
        将ModelTask对象转换为JSON字符串
        
        Returns:
            str: JSON格式的字符串
        """
        return json.dumps(self.to_dict(), ensure_ascii=False)

class ModelTask(Base):
    '''
    ModelTask = {
        id: int
        uid: str
        name: str
        category: str
        model_type: str
        params: dict
        status: str
        progress: int
        created_at: datetime
        updated_at: datetime
        result: dict
        error: str
        is_rabbitmq_ready: bool
        asynchronous: bool
        source: str
        origin_params: dict
    }
    '''
    __tablename__ = "model_task"
    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    uid: Mapped[str] = mapped_column(String(255), nullable=True)
    name: Mapped[str] = mapped_column(String(255))
    category: Mapped[str] = mapped_column(String(255))
    model_type: Mapped[str] = mapped_column(String(255), default="reg")
    params: Mapped[Dict] = mapped_column(JSON)
    status: Mapped[str] = mapped_column(String(255))
    progress: Mapped[int] = mapped_column(String(255), nullable=True)
    created_at: Mapped[datetime] = mapped_column(DateTime)
    updated_at: Mapped[datetime] = mapped_column(DateTime)
    result: Mapped[Dict] = mapped_column(JSON, nullable=True)
    error: Mapped[str] = mapped_column(String(255), nullable=True)
    is_rabbitmq_ready: Mapped[bool] = mapped_column(Boolean, default=False)
    asynchronous: Mapped[bool] = mapped_column(Boolean, default=False)
    source: Mapped[str] = mapped_column(String(255), nullable=True)
    origin_params: Mapped[Dict] = mapped_column(JSON, nullable=False)

    def to_dict(self) -> Dict[str, Any]:
        """
        将ModelTask对象转换为可JSON序列化的字典
        
        Returns:
            Dict[str, Any]: 包含所有字段的字典，datetime对象转换为ISO格式字符串
        """
        return {
            'id': self.id,
            'uid': self.uid,
            'name': self.name,
            'category': self.category,
            'model_type': self.model_type,
            'params': self.params,
            'status': self.status,
            'progress': self.progress,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'result': self.result,
            'error': self.error,
            'is_rabbitmq_ready': self.is_rabbitmq_ready,
            'asynchronous': self.asynchronous,
            'source': self.source,
            'origin_params': self.origin_params
        }

class SearchTask(Base):

    '''
    SearchTask = {
        id: int
        uid: str
        model_uid: str
        name: str
        params: dict
        status: str
        progress: int
        created_at: datetime
        updated_at: datetime
        result: dict
        error: str
        is_rabbitmq_ready: bool
        asynchronous: bool
    }
    '''

    __tablename__ = "search_task"
    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    uid: Mapped[str] = mapped_column(String(255), nullable=True)
    model_uid: Mapped[str] = mapped_column(String(255), nullable=True)
    name: Mapped[str] = mapped_column(String(255))
    params: Mapped[Dict] = mapped_column(JSON)
    status: Mapped[str] = mapped_column(String(255))
    progress: Mapped[int] = mapped_column(String(255), nullable=True)
    created_at: Mapped[datetime] = mapped_column(DateTime)
    updated_at: Mapped[datetime] = mapped_column(DateTime)
    result: Mapped[Dict] = mapped_column(JSON, nullable=True)
    error: Mapped[str] = mapped_column(String(255), nullable=True)
    is_rabbitmq_ready: Mapped[bool] = mapped_column(Boolean, default=False)
    asynchronous: Mapped[bool] = mapped_column(Boolean, default=False)

    def to_dict(self) -> Dict[str, Any]:
        """
        将SearchTask对象转换为可JSON序列化的字典
        
        Returns:
            Dict[str, Any]: 包含所有字段的字典，datetime对象转换为ISO格式字符串
        """
        return {
            'id': self.id,
            'uid': self.uid,
            'name': self.name,
            'params': self.params,
            'status': self.status,
            'progress': self.progress,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'result': self.result,
            'error': self.error,
            'is_rabbitmq_ready': self.is_rabbitmq_ready,
            'asynchronous': self.asynchronous
        }
