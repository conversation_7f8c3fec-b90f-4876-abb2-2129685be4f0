<template>
  <el-config-provider :locale="zhCn">
    <router-view />
    <ReDialog />
    <StartupModal v-if="route.name === 'Welcome'" />
  </el-config-provider>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted } from "vue";
import { ElConfigProvider } from "element-plus";
import { ReDialog } from "@/components/ReDialog";
import { StartupModal } from "@/components/StartupModal";
import { useRoute, useRouter } from "vue-router";
import zhCn from "element-plus/es/locale/lang/zh-cn";
import { getSocket } from "@/utils/socket";
import { projectManager } from "@/utils/projectManager";

defineOptions({
  name: "App"
});

const route = useRoute();
const router = useRouter();

const handleBeforeUnload = () => {
  const socket = getSocket();
  if (socket) {
    socket.disconnect();
  }
};

// 处理项目管理相关事件
const handleProjectExport = async () => {
  try {
    await projectManager.exportProjectState();
  } catch (error) {
    console.error("导出项目状态失败:", error);
  }
};

const handleProjectImport = async () => {
  try {
    const success = await projectManager.importProjectState();
    if (success) {
      // 监听项目状态导入完成事件，进行导航
      const handleProjectStateImported = (event: CustomEvent) => {
        const { workspacePath } = event.detail;
        if (workspacePath) {
          router.push(`/workspace/${encodeURIComponent(workspacePath)}`);
        }
        // 移除事件监听器
        window.removeEventListener('project-state-imported', handleProjectStateImported as EventListener);
      };
      
      window.addEventListener('project-state-imported', handleProjectStateImported as EventListener);
    }
  } catch (error) {
    console.error("导入项目状态失败:", error);
  }
};

onMounted(() => {
  window.addEventListener("beforeunload", handleBeforeUnload);
  
  // 监听菜单触发的项目管理事件
  if (window.ipcRenderer) {
    window.ipcRenderer.on("menu-triggered-export-project", handleProjectExport);
    window.ipcRenderer.on("menu-triggered-import-project-state", handleProjectImport);
  }
});

onUnmounted(() => {
  window.removeEventListener("beforeunload", handleBeforeUnload);
  
  // 移除事件监听器
  if (window.ipcRenderer) {
    window.ipcRenderer.removeAllListeners("menu-triggered-export-project");
    window.ipcRenderer.removeAllListeners("menu-triggered-import-project-state");
  }
});
</script>
