<template>
  <div class="model-charts">
    <div v-if="modelResult?.eval" class="charts-section">
      <div class="model-controls">
        <el-button type="primary" @click="saveAllCharts">
          <el-icon><Download /></el-icon>
          保存全部图表
        </el-button>
      </div>

      <div class="charts-grid">
        <ChartCard
          v-for="dataset in availableDatasets"
          :key="dataset"
          :ref="(el) => setChartComponentRef(dataset, el)"
          :dataset="dataset"
          :title="datasetConfig.titles[dataset]"
          :data="modelResult.eval[dataset]"
          chart-type="scatter"
          @save="saveSingleChart(dataset)"
        />
      </div>
    </div>
    <el-empty v-else description="暂无图表数据" class="model-empty" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, defineComponent, h, onMounted, onUnmounted, watch, nextTick } from "vue";
import { Download } from "@element-plus/icons-vue";
import { ElMessage, ElCard, ElButton, ElIcon, ElSelect, ElOption } from "element-plus";
import { useDark, useECharts } from "@pureadmin/utils";
import { exportChartImage } from "@/utils/exportUtils";

interface Props {
  modelResult: any;
  datasetConfig?: {
    order: readonly string[];
    titles: Record<string, string>;
  };
}

const props = withDefaults(defineProps<Props>(), {
  datasetConfig: () => ({
    order: ["train", "test", "cv", "loocv"] as const,
    titles: {
      train: "训练集",
      test: "测试集",
      cv: "交叉验证",
      loocv: "留一法交叉验证", 
    },
  }),
});

// 可用数据集（根据 eval 对象中存在的键来确定）
const availableDatasets = computed(() => {
  const result = props.modelResult;
  if (!result || !result.eval) return [];
  // 新逻辑: 直接根据 eval 对象中存在的键来确定可用的数据集
  return props.datasetConfig.order.filter(key => result.eval[key] != null && result.eval[key].yTrue && result.eval[key].yPredict);
});

const selectedDataset = ref(
  availableDatasets.value.length > 0 ? availableDatasets.value[0] : "train",
);

// ChartCard 组件
const ChartCard = defineComponent({
  name: "ChartCard",
  props: {
    dataset: String,
    title: String,
    data: Object,
    chartType: {
      type: String,
      default: 'scatter'
    }
  },
  emits: ["save"],
  setup(props, { emit, expose }) {
    const chartRef = ref(null);
    const { isDark } = useDark();
    const theme = computed(() => (isDark.value ? "dark" : "default"));
    const localChartType = ref(props.chartType);
    const chartOptions = [
      { label: "散点图", value: "scatter" },
      { label: "残差直方图", value: "residual-histogram" }
    ];
    const { setOptions, getInstance } = useECharts(chartRef, { theme });

    function renderChart() {
      nextTick(() => {
        const chartInstance = getInstance();
        if (!chartRef.value || !chartInstance || !props.data) return;
        const { yTrue, yPredict } = props.data;
        if (!yTrue || !yPredict) return;
        if (localChartType.value === 'scatter') {
          const scatterData = yTrue.map((val, i) => [val, yPredict[i]]);
          const minVal = Math.min(...yTrue, ...yPredict);
          const maxVal = Math.max(...yTrue, ...yPredict);
          const range = [minVal - 1, maxVal + 1];
          const { clientWidth, clientHeight } = chartRef.value;
          const gridSize = Math.min(clientWidth, clientHeight) * 0.75;
          const left = (clientWidth - gridSize) / 2;
          const top = (clientHeight - gridSize) / 2;
          const grid = {
            left: left,
            right: left,
            top: top,
            bottom: top,
            containLabel: true
          };
          setOptions({
            backgroundColor: "#F9FBFF",
            tooltip: {
              trigger: "item",
              formatter: (params) => `真实值: ${params.value[0].toFixed(2)}<br/>预测值: ${params.value[1].toFixed(2)}`
            },
            grid: grid,
            xAxis: {
              type: "value",
              name: "真实值",
              nameLocation: "end",
              nameTextStyle: { color: "#005DFF", opacity: 0.6 },
              min: range[0],
              max: range[1],
              axisLabel: { formatter: v => v.toFixed(2), color: "#999999" },
              axisLine: { show: true, lineStyle: { color: "rgba(0, 93, 255, 0.3)" } },
              splitLine: { show: false },
            },
            yAxis: {
              type: "value",
              name: "预测值",
              nameLocation: "end",
              nameTextStyle: { color: "#005DFF", opacity: 0.6 },
              min: range[0],
              max: range[1],
              axisLabel: { formatter: v => v.toFixed(2), color: "#999999" },
              axisLine: { show: true, lineStyle: { color: "rgba(0, 93, 255, 0.3)" } },
              splitLine: { show: false },
            },
            series: [
              { name: "预测点", type: "scatter", data: scatterData, itemStyle: { color: "#005DFF" } },
              { name: "理想线", type: "line", data: [[range[0], range[0]], [range[1], range[1]]], lineStyle: { type: "dotted", color: "#005DFF" }, symbol: 'none' }
            ]
          });
          chartInstance.resize();
        } else if (localChartType.value === 'residual-histogram') {
          const residuals = yTrue.map((val, i) => val - yPredict[i]);
          const numBins = 30;
          const minResidual = Math.min(...residuals);
          const maxResidual = Math.max(...residuals);
          let binWidth = (maxResidual - minResidual) / numBins;

          // 特殊处理：所有残差相等
          if (binWidth === 0) {
            binWidth = 1; // 任意非零，所有数据都进第一个bin
          }

          const bins = Array.from({ length: numBins }, (_, i) => ({
            range: [minResidual + i * binWidth, minResidual + (i + 1) * binWidth],
            count: 0
          }));

          residuals.forEach(r => {
            let binIndex = Math.floor((r - minResidual) / binWidth);
            // 精度保护
            if (binIndex < 0) binIndex = 0;
            if (binIndex >= numBins) binIndex = numBins - 1;
            bins[binIndex].count++;
          });
          setOptions({
            backgroundColor: "#F9FBFF",
            tooltip: {
              trigger: 'axis',
              formatter: params => {
                const bin = bins[params[0].dataIndex];
                return `范围: [${bin.range[0].toFixed(2)}, ${bin.range[1].toFixed(2)})<br/>频数: ${bin.count}`;
              }
            },
            grid: { left: '10%', right: '10%', bottom: '15%', top: '25%', containLabel: true },
            xAxis: {
              type: 'category',
              name: '残差',
              nameLocation: "end",
              nameTextStyle: { color: "#005DFF", opacity: 0.6 },
              data: bins.map(b => `${b.range[0].toFixed(2)}-${b.range[1].toFixed(2)}`),
              axisLabel: { rotate: 45, interval: Math.floor(numBins / 10), color: "#999999" },
              axisLine: { show: true, lineStyle: { color: "rgba(0, 93, 255, 0.3)" } },
              splitLine: { show: false },
            },
            yAxis: {
              type: 'value',
              name: '频数',
              nameLocation: "end",
              nameTextStyle: { color: "#005DFF", opacity: 0.6 },
              axisLabel: { color: "#999999" },
              axisLine: { show: true, lineStyle: { color: "rgba(0, 93, 255, 0.3)" } },
              splitLine: { show: false },
            },
            series: [
              { name: '残差', type: 'bar', data: bins.map(b => b.count), itemStyle: { color: '#005DFF' } }
            ]
          });
          chartInstance.resize();
        }
      });
    }

    let resizeObserver = null;
    onMounted(() => {
      renderChart();
      window.addEventListener('resize', renderChart);
      // 监听容器尺寸变化
      if (window.ResizeObserver && chartRef.value) {
        resizeObserver = new ResizeObserver(() => {
          renderChart();
        });
        resizeObserver.observe(chartRef.value);
      }
    });
    
    // 监听数据变化，重新渲染图表
    watch(() => props.data, () => {
      renderChart();
    }, { deep: true });
    
    watch(localChartType, () => {
      renderChart();
    });
    onUnmounted(() => {
      window.removeEventListener('resize', renderChart);
      if (resizeObserver && chartRef.value) {
        resizeObserver.unobserve(chartRef.value);
        resizeObserver.disconnect();
      }
    });

    expose({
      getChartInstance: getInstance,
      resizeChart: renderChart
    });

    return () =>
      h(
        "div",
        { class: "chart-container" },
        [
          h("div", { class: "chart-header" }, [
            h("span", { class: "chart-title" }, props.title),
            h("div", { class: "chart-controls" }, [
              h(
                "div",
                { class: "selection-controls" },
                chartOptions.map(option =>
                  h(
                    "button",
                    {
                      key: option.value,
                      type: "button",
                      class: [
                        "selection-button",
                        { "is-active": localChartType.value === option.value },
                      ],
                      onClick: () => {
                        localChartType.value = option.value;
                      },
                    },
                    option.label,
                  ),
                ),
              ),
              h(
                ElButton,
                { type: "primary", size: "small", style: "margin-left: 10px;", onClick: () => emit("save") },
                { default: () => [h(ElIcon, () => h(Download)), " 保存"] },
              ),
            ]),
          ]),
          h("div", { ref: chartRef, class: "chart" }),
        ],
      );
  },
});

// 图表组件引用集合
const chartComponents = ref<Record<string, any>>({});
const setChartComponentRef = (dataset: string, el: any) => {
  if (el) chartComponents.value[dataset] = el;
};

const saveSingleChart = async (dataset: string) => {
  const comp = chartComponents.value[dataset];
  if (!comp) {
    ElMessage.error("图表组件未找到");
    return;
  }
  const inst = comp.getChartInstance();
  if (!inst) {
    ElMessage.error("图表未初始化");
    return;
  }
  const dataURL = inst.getDataURL({
    type: "png",
    pixelRatio: 2,
    backgroundColor: "#fff",
  });
  const filename = `${props.datasetConfig.titles[dataset]}_预测结果_${Date.now()}.png`;
  await exportChartImage(dataURL, filename);
};

const saveAllCharts = async () => {
  for (const ds of availableDatasets.value) {
    saveSingleChart(ds);
    await new Promise((resolve) => setTimeout(resolve, 100));
  }
};
</script>

<style lang="scss" scoped>
.charts-section {
  .model-controls {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    margin-bottom: 16px;
  }
}

.charts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
  gap: 20px;
}

:deep(.chart-container) {
  position: relative;
  border-radius: 8px;
  overflow: hidden;

  .chart-header {
    position: absolute;
    top: 15px;
    left: 20px;
    right: 20px;
    z-index: 10;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .chart-title {
    font-size: 16px;
    font-weight: 500;
  }

  .chart-controls {
    display: flex;
    align-items: center;
  }

  .selection-controls {
    display: flex;
    gap: 8px;
  }

  .selection-button {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 12px;
    height: 28px;
    border-radius: 14px;
    border: 1px solid transparent;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.3s ease;
    background: rgba(206, 206, 206, 0.1);
    color: #666;

    &.is-active {
      background: rgba(0, 93, 255, 0.1);
      border-color: #005dff;
      color: #005dff;
      font-weight: 500;
    }
  }

  .chart {
    width: 100%;
    height: 450px;
  }
}

@media (max-width: 1200px) {
  .charts-grid {
    grid-template-columns: 1fr;
  }
}
</style>


