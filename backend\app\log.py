import logging
import logging.config
from pathlib import Path

def configure_logging(app, log_dir: Path):  
    LOGGING_CONFIG = {
        'version': 1,
        'disable_existing_loggers': False,
        'formatters': {
            'standard': {
                'format': '%(asctime)s [%(levelname)s] %(name)s: %(message)s',
                'datefmt': '%Y-%m-%d %H:%M:%S'
            },
            'detailed': {
                'format': '%(asctime)s [%(levelname)s] %(name)s (%(filename)s:%(lineno)d): %(message)s',
                'datefmt': '%Y-%m-%d %H:%M:%S'
            }
        },
        'handlers': {
            'console': {
                'class': 'logging.StreamHandler',
                'formatter': 'standard',
                'level': 'INFO',
                'stream': 'ext://sys.stdout'
            },
            'file': {
                'class': 'logging.handlers.RotatingFileHandler',
                'formatter': 'standard',
                'filename': log_dir / 'app.log',
                'maxBytes': 10485760,  # 10MB
                'backupCount': 5,
                'encoding': 'utf8'
            },
            'error_file': {
                'class': 'logging.handlers.RotatingFileHandler',
                'formatter': 'detailed',
                'filename': log_dir / 'error.log',
                'level': 'ERROR',
                'maxBytes': 10485760,
                'backupCount': 5,
                'encoding': 'utf8'
            },
            'rabbitmq_file': {
                'class': 'logging.handlers.RotatingFileHandler',
                'formatter': 'standard',
                'filename': log_dir / 'rabbitmq.log',
                'maxBytes': 10485760,
                'backupCount': 3,
                'encoding': 'utf8'
            },
            'socketio': {
                'class': 'logging.handlers.RotatingFileHandler',
                'formatter': 'standard',
                'filename': log_dir / 'socketio.log',
                'maxBytes': 10485760,
                'backupCount': 3,
                'encoding': 'utf8'
            },
            'werkzeug': {
                'class': 'logging.handlers.RotatingFileHandler',
                'formatter': 'standard',
                'filename': log_dir / 'werkzeug.log',
                'maxBytes': 10485760,
                'backupCount': 3,
                'encoding': 'utf8'
            }
        },
        'loggers': {
            '': {  # root logger
                'handlers': ['console', 'file', 'error_file'],
                'level': 'INFO',
                'propagate': False
            },
            'flask.app': {
                'handlers': ['console', 'file', 'error_file'],
                'level': 'INFO',
                'propagate': False
            },
            'werkzeug': {
                'handlers': ['console', 'werkzeug', 'error_file'],
                'level': 'INFO',
                'propagate': False
            },
            'rabbitmq': {
                'handlers': ['rabbitmq_file', 'console', 'error_file'],
                'level': 'INFO',
                'propagate': False
            },
            'socketio': {
                'handlers': ['console', 'socketio', 'error_file'],
                'level': 'INFO',
                'propagate': False
            },
            'pika': {
                'handlers': ['rabbitmq_file', 'error_file'],
                'level': 'INFO',
                'propagate': False
            },
            'pika.adapters': {
                'handlers': ['rabbitmq_file', 'error_file'],
                'level': 'INFO',
                'propagate': False
            },
            'pika.adapters.utils': {
                'handlers': ['rabbitmq_file', 'error_file'],
                'level': 'INFO',
                'propagate': False
            },
            'pika.adapters.blocking_connection': {
                'handlers': ['rabbitmq_file', 'error_file'],
                'level': 'INFO',
                'propagate': False
            },
            'pika.connection': {
                'handlers': ['rabbitmq_file', 'error_file'],
                'level': 'INFO',
                'propagate': False
            },
            'pika.channel': {
                'handlers': ['rabbitmq_file', 'error_file'],
                'level': 'INFO',
                'propagate': False
            }
        }
    }
    
    logging.config.dictConfig(LOGGING_CONFIG)
    
    # 禁用Flask默认的日志处理器
    app.logger.handlers.clear()
    app.logger.propagate = True
    
    # 设置Flask logger
    app.logger = logging.getLogger('flask.app')