import { ElMessage, ElMessageBox } from "element-plus";
import <PERSON><PERSON><PERSON><PERSON> from "jszip";

/**
 * 工作区大小限制配置
 */
export const WORKSPACE_SIZE_LIMITS = {
  WARNING_SIZE: 100 * 1024 * 1024, // 100MB
  MAX_SIZE: 500 * 1024 * 1024, // 500MB
};

/**
 * 获取工作区大小信息
 * @param workspacePath - 工作区路径
 * @returns Promise<{size: number, fileCount: number}> - 大小和文件数量信息
 */
export const getWorkspaceSize = async (
  workspacePath: string,
): Promise<{
  size: number;
  fileCount: number;
}> => {
  try {
    if (typeof window !== "undefined" && window.ipcRenderer) {
      const result = await window.ipcRenderer.invoke(
        "fs:getDirectorySize",
        workspacePath,
      );
      return {
        size: result.size || 0,
        fileCount: result.fileCount || 0,
      };
    }
    return { size: 0, fileCount: 0 };
  } catch (error) {
    console.error("获取工作区大小失败:", error);
    return { size: 0, fileCount: 0 };
  }
};

/**
 * 格式化文件大小
 * @param bytes - 字节数
 * @returns string - 格式化后的大小字符串
 */
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return "0 B";

  const k = 1024;
  const sizes = ["B", "KB", "MB", "GB", "TB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`;
};

/**
 * 检查工作区大小并显示警告
 * @param workspacePath - 工作区路径
 * @returns Promise<boolean> - 是否继续导出
 */
export const checkWorkspaceSizeAndWarn = async (
  workspacePath: string,
): Promise<boolean> => {
  try {
    const { size, fileCount } = await getWorkspaceSize(workspacePath);

    if (size > WORKSPACE_SIZE_LIMITS.MAX_SIZE) {
      ElMessage.error(
        `工作区过大 (${formatFileSize(size)})，无法导出。请清理工作区后重试。`,
      );
      return false;
    }

    if (size > WORKSPACE_SIZE_LIMITS.WARNING_SIZE) {
      try {
        await ElMessageBox.confirm(
          `工作区较大 (${formatFileSize(size)}, ${fileCount} 个文件)，导出可能需要较长时间。\n\n是否继续导出？`,
          "工作区大小警告",
          {
            confirmButtonText: "继续导出",
            cancelButtonText: "取消",
            type: "warning",
          },
        );
        return true;
      } catch {
        return false;
      }
    }

    return true;
  } catch (error) {
    console.error("检查工作区大小失败:", error);
    // 如果检查失败，允许继续导出
    return true;
  }
};

/**
 * 打包工作区文件夹
 * @param workspacePath - 工作区路径
 * @returns Promise<Blob> - 打包后的zip文件
 */
export const packageWorkspace = async (
  workspacePath: string,
): Promise<Blob> => {
  try {
    if (typeof window !== "undefined" && window.ipcRenderer) {
      // 在 Electron 环境中，通过主进程读取文件
      const workspaceData = await window.ipcRenderer.invoke(
        "fs:readDirectoryRecursive",
        workspacePath,
      );

      const zip = new JSZip();

      // 添加工作区文件到zip
      for (const file of workspaceData.files) {
        if (file.isFile) {
          const fileContent = await window.ipcRenderer.invoke(
            "fs:readFileBuffer",
            file.fullPath,
          );
          zip.file(file.relativePath, fileContent);
        }
      }

      // 生成zip文件
      const zipBlob = await zip.generateAsync({ type: "blob" });
      return zipBlob;
    } else {
      // 浏览器环境暂不支持文件系统访问
      throw new Error("浏览器环境不支持工作区打包");
    }
  } catch (error) {
    console.error("打包工作区失败:", error);
    throw new Error("打包工作区失败");
  }
};

/**
 * 合并项目数据和工作区数据到一个zip文件
 * @param projectBlob - 项目数据Blob
 * @param workspaceBlob - 工作区数据Blob
 * @param projectName - 项目名称
 * @returns Promise<Blob> - 合并后的zip文件
 */
export const mergeProjectAndWorkspace = async (
  projectBlob: Blob,
  workspaceBlob: Blob,
  projectName: string = "project",
): Promise<Blob> => {
  try {
    const zip = new JSZip();

    // 添加项目数据
    zip.file(`${projectName}_data.zip`, projectBlob);

    // 添加工作区数据
    zip.file(`${projectName}_workspace.zip`, workspaceBlob);

    // 添加元数据文件
    const metadata = {
      exportTime: new Date().toISOString(),
      projectName,
      description:
        "固体推进配方剂智能设计软件项目导出，包含项目数据和工作区文件",
    };

    zip.file("export_metadata.json", JSON.stringify(metadata, null, 2));

    // 生成最终的zip文件
    const finalZip = await zip.generateAsync({ type: "blob" });
    return finalZip;
  } catch (error) {
    console.error("合并项目和工作区数据失败:", error);
    throw new Error("合并数据失败");
  }
};

/**
 * 解压缩工作区文件
 * @param workspaceBlob - 工作区zip数据
 * @param targetPath - 目标解压路径
 * @returns Promise<boolean> - 是否解压成功
 */
export const extractWorkspace = async (
  workspaceBlob: Blob,
  targetPath: string,
): Promise<boolean> => {
  try {
    if (typeof window !== "undefined" && window.ipcRenderer) {
      const zip = new JSZip();
      const zipContent = await zip.loadAsync(workspaceBlob);

      // 遍历zip文件中的所有文件
      for (const [relativePath, zipObject] of Object.entries(
        zipContent.files,
      )) {
        if (!zipObject.dir) {
          // 读取文件内容
          const content = await zipObject.async("uint8array");
          const fullPath = `${targetPath}/${relativePath}`;

          // 通过主进程写入文件
          await window.ipcRenderer.invoke(
            "fs:writeFileBuffer",
            fullPath,
            content,
          );
        }
      }
      return true;
    } else {
      throw new Error("浏览器环境不支持文件系统写入");
    }
  } catch (error) {
    console.error("解压工作区失败:", error);
    return false;
  }
};

/**
 * 检查路径是否存在
 * @param path - 文件或文件夹路径
 * @returns Promise<boolean> - 路径是否存在
 */
export const checkPathExists = async (path: string): Promise<boolean> => {
  try {
    if (typeof window !== "undefined" && window.ipcRenderer) {
      return await window.ipcRenderer.invoke("fs:pathExists", path);
    }
    return false;
  } catch (error) {
    console.error("检查路径失败:", error);
    return false;
  }
};

/**
 * 选择目录对话框
 * @param title - 对话框标题
 * @param defaultPath - 默认路径
 * @returns Promise<string | null> - 选择的路径，取消则返回null
 */
export const selectDirectory = async (
  title: string = "选择目录",
  defaultPath?: string,
): Promise<string | null> => {
  try {
    if (typeof window !== "undefined" && window.ipcRenderer) {
      const result = await window.ipcRenderer.invoke("dialog:showOpenDialog", {
        title,
        defaultPath,
        properties: ["openDirectory", "createDirectory"],
      });

      if (!result.canceled && result.filePaths.length > 0) {
        return result.filePaths[0];
      }
    }
    return null;
  } catch (error) {
    console.error("选择目录失败:", error);
    return null;
  }
};

/**
 * 解析导出包中的项目数据和工作区数据
 * @param exportBlob - 导出的zip包或blob文件
 * @returns Promise<{projectBlob: Blob | null, workspaceBlob: Blob | null, metadata: any}> - 解析结果
 */
export const parseExportPackage = async (
  exportBlob: Blob,
): Promise<{
  projectBlob: Blob | null;
  workspaceBlob: Blob | null;
  metadata: any;
}> => {
  try {
    // 尝试解析为zip文件
    try {
      const zip = new JSZip();
      const zipContent = await zip.loadAsync(exportBlob);

      let projectBlob: Blob | null = null;
      let workspaceBlob: Blob | null = null;
      let metadata: any = {};

      // 查找项目数据文件
      for (const [filename, zipObject] of Object.entries(zipContent.files)) {
        if (filename.endsWith("_data.zip")) {
          projectBlob = await zipObject.async("blob");
        } else if (filename.endsWith("_workspace.zip")) {
          workspaceBlob = await zipObject.async("blob");
        } else if (filename === "export_metadata.json") {
          const metadataText = await zipObject.async("text");
          metadata = JSON.parse(metadataText);
        }
      }
      return { projectBlob, workspaceBlob, metadata };
    } catch (zipError) {
      console.warn("无法解析为zip文件，尝试作为单个项目文件处理:", zipError);

      // 如果不是zip文件，假设整个blob就是项目数据
      const metadata = {
        exportTime: new Date().toISOString(),
        projectName: "导入的项目",
        description: "从单个文件导入的项目数据",
      };

      return {
        projectBlob: exportBlob,
        workspaceBlob: null,
        metadata,
      };
    }
  } catch (error) {
    console.error("解析导出包失败:", error);
    throw new Error("解析导出包失败");
  }
};
