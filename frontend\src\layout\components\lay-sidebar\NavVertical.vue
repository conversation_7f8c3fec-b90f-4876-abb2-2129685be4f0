<script setup lang="ts">
import { useRoute, useRouter } from "vue-router";
import { emitter } from "@/utils/mitt";
import { useNav } from "@/layout/hooks/useNav";
import { responsiveStorageNameSpace } from "@/config";
import { storageLocal, isAllEmpty } from "@pureadmin/utils";
import { findRouteByPath, getParentPaths } from "@/router/utils";
import { usePermissionStoreHook } from "@/store/modules/permission";
import { ref, computed, watch, onMounted, onBeforeUnmount, nextTick } from "vue";
import LaySidebarItem from "../lay-sidebar/components/SidebarItem.vue";
import { useWorkspaceStoreHook } from "@/store/modules/workspace";
import { ElIcon } from 'element-plus';
import WorkspaceNormalIcon from "@/assets/svg/workspace_normal.svg?component";
import WorkspaceHoverIcon from "@/assets/svg/workspace_hover.svg?component";
import WorkspaceSelectedIcon from "@/assets/svg/workspace_selected.svg?component";
import MenuNormalIcon from "@/assets/svg/menu_normal.svg?component";
import MenuHoverIcon from "@/assets/svg/menu_hover.svg?component";
import MenuSelectedIcon from "@/assets/svg/menu_selected.svg?component";
import SettingNormalIcon from "@/assets/svg/setting_normal.svg?component";
import SettingHoverIcon from "@/assets/svg/setting_hover.svg?component";
import SettingSelectedIcon from "@/assets/svg/setting_selected.svg?component";
import ModelNormalIcon from "@/assets/svg/model_normal.svg?component";
import ModelHoverIcon from "@/assets/svg/model_hover.svg?component";
import ModelSelectedIcon from "@/assets/svg/model_selected.svg?component";
import { Select as IconSelect } from '@element-plus/icons-vue';

const route = useRoute();
const router = useRouter();
const workspaceStore = useWorkspaceStoreHook();
const showLogo = ref(
  storageLocal().getItem<StorageConfigs>(
    `${responsiveStorageNameSpace()}configure`
  )?.showLogo ?? true
);

const {
  device,
  pureApp,
  tooltipEffect,
  menuSelect,
  onPanel
} = useNav();

const subMenuData = ref([]);

// Use computed properties from store with defensive checks
const currentView = computed(() => {
  try {
    return workspaceStore.getCurrentView || 'workspace';
  } catch (error) {
    console.warn('Error accessing currentView:', error);
    return 'workspace';
  }
});

const isWorkspaceModified = computed(() => {
  try {
    return workspaceStore.getIsWorkspaceModified || false;
  } catch (error) {
    console.warn('Error accessing isWorkspaceModified:', error);
    return false;
  }
});

const currentWorkspacePath = computed(() => {
  try {
    return workspaceStore.getCurrentWorkspacePath || '';
  } catch (error) {
    console.warn('Error accessing currentWorkspacePath:', error);
    return '';
  }
});

const menuData = computed(() => {
  return pureApp.layout === "mix" && device.value !== "mobile"
    ? subMenuData.value
    : usePermissionStoreHook().wholeMenus;
});

const loading = computed(() =>
  pureApp.layout === "mix" ? false : menuData.value.length === 0 ? true : false
);

const defaultActive = computed(() =>
  !isAllEmpty(route.meta?.activePath) ? route.meta.activePath : route.path
);

function getSubMenuData() {
  let path = "";
  path = defaultActive.value;
  subMenuData.value = [];
  const parentPathArr = getParentPaths(
    path,
    usePermissionStoreHook().wholeMenus
  );
  const parenetRoute = findRouteByPath(
    parentPathArr[0] || path,
    usePermissionStoreHook().wholeMenus
  );
  if (!parenetRoute?.children) return;
  subMenuData.value = parenetRoute?.children;
}

watch(
  () => [route.path, usePermissionStoreHook().wholeMenus],
  () => {
    if (route.path.includes("/redirect")) return;
    getSubMenuData();
    menuSelect(route.path);
  }
);

onMounted(() => {
  getSubMenuData();
  emitter.on("logoChange", key => {
    showLogo.value = key;
  });

  // Initialize workspace store to ensure arrays are properly set up
  workspaceStore.initializeStore();

  // Initialize workspace view if there's a persisted workspace
  if (workspaceStore.hasWorkspace) {
    workspaceStore.setCurrentView('workspace');
  }
});

onBeforeUnmount(() => {
  emitter.off("logoChange");
});

</script>

<template>
  <div v-loading="loading" :class="[
    'sidebar-container',
    showLogo ? 'has-logo' : 'no-logo',
    `view-${currentView}`
  ]">
    <!-- Icons - arranged vertically -->
    <div class="sidebar-activity-icons">
      <button @click="workspaceStore.setCurrentView('workspace')" :class="{ active: currentView === 'workspace' }"
        title="工作区" class="icon-button workspace-icon">
        <span v-if="isWorkspaceModified" class="modification-dot">
          <el-icon :size="8">
            <IconSelect />
          </el-icon>
        </span>
        <WorkspaceNormalIcon class="icon normal" />
        <WorkspaceHoverIcon class="icon hover" />
        <WorkspaceSelectedIcon class="icon selected" />
      </button>
      <button @click="workspaceStore.setCurrentView('navigation')" :class="{ active: currentView === 'navigation' }"
        title="导航" class="icon-button menu-icon">
        <MenuNormalIcon class="icon normal" />
        <MenuHoverIcon class="icon hover" />
        <MenuSelectedIcon class="icon selected" />
      </button>
      <button @click="workspaceStore.setCurrentView('model')" :class="{ active: currentView === 'model' }"
        title="模型" class="icon-button model-icon">
        <ModelNormalIcon class="icon normal" />
        <ModelHoverIcon class="icon hover" />
        <ModelSelectedIcon class="icon selected" />
      </button>
    </div>
  </div>
</template>

<style scoped>
/* Styles for the activity icons section - vertical layout */
.sidebar-activity-icons {
  display: flex;
  flex-direction: column;
  /* Vertical layout */
  align-items: center;
  justify-content: flex-start;
  padding: 8px 0;
  gap: 16px;
  /* 增加图标间距 */
  height: 100%;
}

.sidebar-activity-icons .icon-button {
  background: none;
  border: none;
  padding: 8px;
  margin: 0;
  cursor: pointer;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: all 0.2s ease;
  min-width: 36px;
  min-height: 36px;
}

/* 图标状态管理 */
.icon-button .icon {
  width: 20px;
  height: 20px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  transition: opacity 0.2s ease;
}

.icon-button .icon.normal {
  opacity: 1;
}

.icon-button .icon.hover,
.icon-button .icon.selected {
  opacity: 0;
}

/* 鼠标悬停效果 */
.icon-button:hover .icon.normal {
  opacity: 0;
}

.icon-button:hover .icon.hover {
  opacity: 1;
}

.icon-button:hover .icon.selected {
  opacity: 0;
}

/* 选中状态 */
.icon-button.active .icon.normal,
.icon-button.active .icon.hover {
  opacity: 0;
}

.icon-button.active .icon.selected {
  opacity: 1;
}

/* 已选中状态下的悬停不改变图标 */
.icon-button.active:hover .icon.selected {
  opacity: 1;
}

.modification-dot {
  position: absolute;
  top: 0px;
  right: 0px;
  line-height: 0;
  color: #f56c6c;
  z-index: 2;
}

/* Adjust existing styles */
.sidebar-container {
  background: white !important;
  /* 白色背景 - 使用!important覆盖变量 */
  display: flex;
  flex-direction: column;
  width: 56px !important;
  /* 固定宽度为侧边栏收起状态宽度 */
  max-width: 56px !important;
  /* 添加最大宽度限制 */
  min-width: 56px !important;
  /* 添加最小宽度保证 */
  height: 100%;
  border-right: none;
  /* 明确移除右侧边框 */
  box-shadow: none;
  border-radius: 0;
  position: relative;
  /* 确保定位正确 */
  overflow: hidden;
  /* 确保内容不会溢出 */
  flex-shrink: 0;
  /* 防止侧边栏被挤压 */
}

/* 深色模式适配 */
html.dark .sidebar-container {
  background: var(--el-bg-color);
}

:deep(.el-loading-mask) {
  opacity: 0.45;
}
</style>
