// store/modules/tableData.ts
import { defineStore } from "pinia";

/* 表格数据存储到pinia */
export const useTableDataStore = defineStore("tableData", {
  state: () => ({
    currentTableData: [] as any[][], // 存储当前工作表的数据
    currentTableHeader: [] as any[], // 表头
    isModified: false, // 添加修改状态
  }),
  actions: {
    setTableData(data: any[][]) {
      this.currentTableData = data; // 设置数据
    },
    setTableHeader(header: any[]) {
      this.currentTableHeader = header; //设置表头
    },
    setModified(modified: boolean) {
      this.isModified = modified; // 设置修改状态
    },
  },
  persist: true,
});
