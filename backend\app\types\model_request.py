from typing import Literal, TypedDict, Union, Optional, Any, Dict, List
import json
from collections import OrderedDict

HeaderKey = Literal["index", "target", "features", "deletes", "all"]
HeaderMap = Dict[HeaderKey, List[str]]

class DatasetMeta(TypedDict):
    '''数据元信息'''
    headers: HeaderMap

class Dataset(TypedDict):
    '''数据集结构
    dataset: Dataset = {
    data: List[List[float]]
    meta: DatasetMeta = {
        headers: HeaderMap = {
            index: List[str]
            target: List[str]
            features: List[str]
            deletes: List[str]
            all: List[str]
        }
    }
    '''
    data: List[List[float]]
    meta: DatasetMeta

class ModelAlgorithm(TypedDict):
    '''模型算法'''
    name: str
    params: Dict[str, Any] # optimize: bool, ...: Dict[str, Any]

class CrossValidationConfig(TypedDict):
    """交叉验证配置"""
    k: int
    random_state: Optional[int]

class TestSplitConfig(TypedDict):
    """测试集分割配置"""
    size: float  # 测试集比例
    random_state: Optional[int]

class ModelMetaEvaluation(TypedDict):
    '''模型评估配置'''
    cv: Optional[CrossValidationConfig]
    loocv: Optional[bool]
    test: Optional[TestSplitConfig]

class ModelMeta(TypedDict):
    '''模型元信息'''
    headers: HeaderMap
    evaluation: ModelMetaEvaluation

class Model(TypedDict):
    '''模型配置
    model: Model = {
    uid: str
    algorithm: ModelAlgorithm = {
        name: str
        params: {
            optimize: bool
            ...: Dict[str, Any]
        }
    }
    meta: ModelMeta = {
        headers: HeaderMap = {
            index: List[str]
            target: List[str]
            features: List[str]
            deletes: List[str]
            all: List[str]
        }
        evaluation: ModelMetaEvaluation = {
            cv: Optional[CrossValidationConfig] = {
                k: int
                random_state: Optional[int]
            }
            loocv: Optional[bool]
            test: Optional[TestSplitConfig] = {
                size: float
                random_state: Optional[int]
            }
            shap: Optional[bool]
        }
    }
}
    '''
    uid: str
    algorithm: ModelAlgorithm
    meta: ModelMeta

class ModelRequest(TypedDict):
    '''
    模型request参数
    ModelRequest = {
        dataset: Dataset = {
            data: List[List[float]]
            meta: DatasetMeta = {
                headers: HeaderMap = {
                    index: List[str]
                    target: List[str]
                    features: List[str]
                    deletes: List[str]
                    all: List[str]
                }
            }
        }
        model: Model = {
            uid: str
            algorithm: ModelAlgorithm = {
                name: str
                params: {
                    optimize: bool
                    ...: Dict[str, Any]
                }
            }
            meta: ModelMeta = {
                headers: HeaderMap = {
                    index: List[str]
                    target: List[str]
                    features: List[str]
                    deletes: List[str]
                    all: List[str]
                }
                evaluation: ModelMetaEvaluation = {
                    cv: Optional[CrossValidationConfig] = {
                        k: int
                        random_state: Optional[int]
                    }
                    loocv: Optional[bool]
                    test: Optional[TestSplitConfig] = {
                        size: float
                        random_state: Optional[int]
                    }
                    shap: Optional[bool]
                }
                name: str
            }
        }
    }
    '''
    dataset: Dataset
    model: Model

from dataclasses import dataclass
import pandas as pd
@dataclass
class ModelConfig(object):
    '''
    模型配置
    ModelConfig = {
        uid: str
        name: str
        x_train: Union[pd.DataFrame, pd.Series]
        y_train: Union[pd.DataFrame, pd.Series]
        x_test: Optional[Union[pd.DataFrame, pd.Series]]
        y_test: Optional[Union[pd.DataFrame, pd.Series]]
        cv: Optional[Union[bool, int]]
        loocv: Optional[bool]
        test: Optional[bool]
        asynchronous: Optional[bool]
        alg_name: str
        alg_param: Optional[Dict[str, Any]]
        is_rabbitmq_ready: bool
        optimize: Optional[bool]
        shap: Optional[bool]
    }
    '''
    uid: str
    name: str
    x_train: Union[pd.DataFrame, pd.Series]
    y_train: Union[pd.DataFrame, pd.Series]
    x_test: Optional[Union[pd.DataFrame, pd.Series]]
    y_test: Optional[Union[pd.DataFrame, pd.Series]]
    cv: Optional[Union[bool, int]]
    loocv: Optional[bool]
    test: Optional[bool]
    asynchronous: Optional[bool]
    alg_name: str
    alg_param: Optional[Dict[str, Any]]
    is_rabbitmq_ready: bool
    optimize: Optional[bool]
    shap: Optional[bool]

    def to_dict(self) -> Dict[str, Any]:
        '''将ModelConfig对象转换为可JSON序列化的字典'''
        x_train = self.x_train.to_dict(into=OrderedDict)
        x_test = self.x_test.to_dict(into=OrderedDict) if self.x_test is not None else None
        if isinstance(self.y_train, pd.Series):
            y_train = self.y_train.to_frame()
            y_test = self.y_test.to_frame() if self.y_test is not None else None
        else:
            y_train = self.y_train
            y_test = self.y_test
        y_train = y_train.to_dict(into=OrderedDict)
        y_test = y_test.to_dict(into=OrderedDict) if y_test is not None else None
        return {
            "uid": self.uid,
            "name": self.name,
            "x_train": x_train,
            "y_train": y_train,
            "x_test": x_test,
            "y_test": y_test,
            "cv": self.cv,
            "loocv": self.loocv,
            "test": self.test,
            "asynchronous": self.asynchronous,
            "alg_name": self.alg_name,
            "alg_param": self.alg_param,
            "is_rabbitmq_ready": self.is_rabbitmq_ready,
            "optimize": self.optimize,
            "shap": self.shap,
        }
    
    def to_json(self) -> str:
        '''将ModelConfig对象转换为JSON字符串'''
        return json.dumps(self.to_dict(), ensure_ascii=False)
    
    def from_dict(data: dict) -> 'ModelConfig':
        x_train = pd.DataFrame(data["x_train"])
        x_test = pd.DataFrame(data["x_test"]) if data["x_test"] is not None else None
        y_train = pd.DataFrame(data["y_train"])
        y_test = pd.DataFrame(data["y_test"]) if data["y_test"] is not None else None
        return ModelConfig(
            uid=data["uid"],
            name=data["name"],
            x_train=x_train,
            y_train=y_train,
            x_test=x_test,
            y_test=y_test,
            cv=data["cv"],
            loocv=data["loocv"],
            test=data["test"],
            asynchronous=data["asynchronous"],
            alg_name=data["alg_name"],
            alg_param=data["alg_param"],
            is_rabbitmq_ready=data["is_rabbitmq_ready"],
            optimize=data["optimize"],
            shap=data["shap"]
        )