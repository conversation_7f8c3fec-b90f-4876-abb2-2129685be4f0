import request from "@/utils/request";
import type { DataPreprocessResponse, PreprocessConfig } from "@/types/preprocess";
import { AxiosHeaders } from "axios";

export const reqDataFill = (data?: PreprocessConfig) => {
  return request.post<DataPreprocessResponse>("/fill/", data, {
    headers: new AxiosHeaders({}),
    skipLoading: true,
  });
};

export const reqOutlierDetection = (data?: PreprocessConfig) => {
  return request.post<DataPreprocessResponse>("/outlier/", data, {
    headers: new AxiosHeaders({}),
    skipLoading: true,
  });
};
