import * as XLSX from "xlsx";
import { ElMessage } from "element-plus";
import { exportProject, importProject } from "@/api/models/project";
import { verifyBlobWithMessage } from "@/utils/checksumUtils";
import {
  checkWorkspaceSizeAndWarn,
  packageWorkspace,
  mergeProjectAndWorkspace,
} from "@/utils/workspaceUtils";
import {
  extractWorkspace,
  selectDirectory,
  parseExportPackage,
} from "@/utils/workspaceUtils";
import { useWorkspaceStoreHook } from "@/store/modules/workspace";

const workspaceStore = useWorkspaceStoreHook();

// 传统下载方法（用于非 Electron 环境）
const downloadFile = (
  content: string | ArrayBuffer,
  filename: string,
  mimeType: string,
) => {
  const blob = new Blob([content], { type: mimeType });
  const url = URL.createObjectURL(blob);
  const link = document.createElement("a");
  link.href = url;
  link.download = filename;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(url);
};

export interface ExportData {
  headers: string[];
  content: any[][];
}

export interface ExportOptions {
  suggestedName?: string;
  sheetName?: string;
  exportType?: "csv" | "excel" | "auto";
  currentSheetName?: string;
}

export interface MultiSheetData {
  [sheetName: string]: ExportData;
}

/**
 * 导出单个工作表数据
 */
export const exportSingleSheet = async (
  data: ExportData,
  options: ExportOptions = {},
): Promise<void> => {
  const {
    suggestedName = `数据导出_${Date.now()}`,
    sheetName = "Sheet1",
    exportType = "auto",
  } = options;

  try {
    if (typeof window !== "undefined" && window.ipcRenderer) {
      // 在 Electron 环境中使用保存对话框
      const filters = [
        { name: "Excel 文件", extensions: ["xlsx"] },
        { name: "CSV 文件", extensions: ["csv"] },
        { name: "所有文件", extensions: ["*"] },
      ];

      const result = await window.ipcRenderer.invoke("dialog:showSaveDialog", {
        defaultPath: suggestedName,
        filters: filters,
      });

      if (!result.canceled && result.filePath) {
        const filePath = result.filePath;

        if (filePath.toLowerCase().endsWith(".csv") || exportType === "csv") {
          const csvContent = [
            data.headers.join(","),
            ...data.content.map((row) =>
              row.map((cell) => `"${cell || ""}"`).join(","),
            ),
          ].join("\n");
          await window.ipcRenderer.invoke("fs:writeFile", filePath, csvContent);
        } else {
          // 默认或明确指定导出为 Excel
          const wb = XLSX.utils.book_new();
          const dataForSheet = [data.headers, ...data.content];
          const ws = XLSX.utils.aoa_to_sheet(dataForSheet);
          XLSX.utils.book_append_sheet(wb, ws, sheetName);
          const excelBuffer = XLSX.write(wb, {
            bookType: "xlsx",
            type: "array",
          });
          await window.ipcRenderer.invoke(
            "fs:writeFileBuffer",
            filePath,
            excelBuffer,
          );
        }
      }
    } else {
      // 在浏览器环境中使用传统下载方式
      if (exportType === "csv") {
        const csvContent = [
          data.headers.join(","),
          ...data.content.map((row) =>
            row.map((cell) => `"${cell || ""}"`).join(","),
          ),
        ].join("\n");
        downloadFile(csvContent, `${suggestedName}.csv`, "text/csv");
      } else {
        // 默认导出为 Excel
        const wb = XLSX.utils.book_new();
        const dataForSheet = [data.headers, ...data.content];
        const ws = XLSX.utils.aoa_to_sheet(dataForSheet);
        XLSX.utils.book_append_sheet(wb, ws, sheetName);
        const excelBuffer = XLSX.write(wb, { bookType: "xlsx", type: "array" });
        downloadFile(
          excelBuffer,
          `${suggestedName}.xlsx`,
          "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        );
      }
    }
  } catch (error: any) {
    console.warn("导出失败:", error);
    throw error;
  }
};

/**
 * 导出多个工作表数据
 */
export const exportMultiSheet = async (
  sheetsData: MultiSheetData,
  options: ExportOptions = {},
): Promise<void> => {
  const { suggestedName = `数据导出_${Date.now()}`, currentSheetName } =
    options;

  try {
    if (typeof window !== "undefined" && window.ipcRenderer) {
      // 在 Electron 环境中使用保存对话框
      const filters = [
        { name: "Excel 文件 (所有工作表)", extensions: ["xlsx"] },
        { name: "CSV 文件 (当前工作表)", extensions: ["csv"] },
        { name: "所有文件", extensions: ["*"] },
      ];

      const result = await window.ipcRenderer.invoke("dialog:showSaveDialog", {
        defaultPath: suggestedName,
        filters: filters,
      });

      if (!result.canceled && result.filePath) {
        const filePath = result.filePath;

        if (filePath.toLowerCase().endsWith(".csv")) {
          // 导出当前工作表为 CSV
          const sheetNameToExport =
            currentSheetName || Object.keys(sheetsData)[0];
          const currentSheetData = sheetsData[sheetNameToExport];
          if (currentSheetData) {
            const csvContent = [
              currentSheetData.headers.join(","),
              ...currentSheetData.content.map((row) =>
                row.map((cell) => `"${cell || ""}"`).join(","),
              ),
            ].join("\n");
            await window.ipcRenderer.invoke(
              "fs:writeFile",
              filePath,
              csvContent,
            );
          }
        } else {
          // 导出所有工作表为 Excel
          const wb = XLSX.utils.book_new();
          Object.entries(sheetsData).forEach(([sheetName, sheetData]) => {
            const dataForSheet = [sheetData.headers, ...sheetData.content];
            const ws = XLSX.utils.aoa_to_sheet(dataForSheet);
            XLSX.utils.book_append_sheet(wb, ws, sheetName);
          });
          const excelBuffer = XLSX.write(wb, {
            bookType: "xlsx",
            type: "array",
          });
          await window.ipcRenderer.invoke(
            "fs:writeFileBuffer",
            filePath,
            excelBuffer,
          );
        }
      }
    } else {
      // 在浏览器环境中使用传统下载方式，默认导出为 Excel
      const wb = XLSX.utils.book_new();
      Object.entries(sheetsData).forEach(([sheetName, sheetData]) => {
        const dataForSheet = [sheetData.headers, ...sheetData.content];
        const ws = XLSX.utils.aoa_to_sheet(dataForSheet);
        XLSX.utils.book_append_sheet(wb, ws, sheetName);
      });
      const excelBuffer = XLSX.write(wb, { bookType: "xlsx", type: "array" });
      downloadFile(
        excelBuffer,
        `${suggestedName}.xlsx`,
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      );
    }
  } catch (error: any) {
    console.warn("导出失败:", error);
    throw error;
  }
};

/**
 * 导出图表数据
 */
export const exportChartData = async (
  chartData: { columns: string[]; data: any[][] },
  chartTitle: string,
  options: ExportOptions = {},
): Promise<void> => {
  const { suggestedName = `${chartTitle}_数据_${Date.now()}` } = options;

  try {
    if (typeof window !== "undefined" && window.ipcRenderer) {
      // 在 Electron 环境中使用保存对话框
      const filters = [
        { name: "Excel 文件", extensions: ["xlsx"] },
        { name: "CSV 文件", extensions: ["csv"] },
        { name: "所有文件", extensions: ["*"] },
      ];

      const result = await window.ipcRenderer.invoke("dialog:showSaveDialog", {
        defaultPath: suggestedName,
        filters: filters,
      });

      if (!result.canceled && result.filePath) {
        const filePath = result.filePath;

        if (filePath.toLowerCase().endsWith(".csv")) {
          const csvContent = [
            chartData.columns.join(","),
            ...chartData.data.map((row) =>
              row.map((cell) => `"${cell || ""}"`).join(","),
            ),
          ].join("\n");
          await window.ipcRenderer.invoke("fs:writeFile", filePath, csvContent);
        } else {
          // 默认或明确指定导出为 Excel
          const wb = XLSX.utils.book_new();
          const ws = XLSX.utils.aoa_to_sheet([
            chartData.columns,
            ...chartData.data,
          ]);
          XLSX.utils.book_append_sheet(wb, ws, "Sheet1");
          const excelBuffer = XLSX.write(wb, {
            bookType: "xlsx",
            type: "array",
          });
          await window.ipcRenderer.invoke(
            "fs:writeFileBuffer",
            filePath,
            excelBuffer,
          );
        }
      }
    } else {
      // 在浏览器环境中使用传统下载方式，默认导出为 Excel
      const wb = XLSX.utils.book_new();
      const ws = XLSX.utils.aoa_to_sheet([
        chartData.columns,
        ...chartData.data,
      ]);
      XLSX.utils.book_append_sheet(wb, ws, "Sheet1");
      const excelBuffer = XLSX.write(wb, { bookType: "xlsx", type: "array" });
      downloadFile(
        excelBuffer,
        `${suggestedName}.xlsx`,
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      );
    }
  } catch (error: any) {
    console.warn("导出失败:", error);
    throw error;
  }
};

/**
 * 导出图表为图片
 * @param dataURL - 图表的 dataURL
 * @param filename - 建议的文件名
 */
export const exportChartImage = async (
  dataURL: string,
  filename: string,
): Promise<void> => {
  try {
    if (typeof window !== "undefined" && window.ipcRenderer) {
      // 在 Electron 环境中使用保存对话框
      const result = await window.ipcRenderer.invoke("dialog:showSaveDialog", {
        defaultPath: filename,
        filters: [
          { name: "PNG 文件", extensions: ["png"] },
          { name: "所有文件", extensions: ["*"] },
        ],
      });

      if (!result.canceled && result.filePath) {
        // 从 dataURL 中提取 base64 数据并转换为 Buffer
        const base64Data = dataURL.replace(/^data:image\/png;base64,/, "");
        await window.ipcRenderer.invoke(
          "fs:writeFileBase64Buffer",
          result.filePath,
          base64Data,
        );
      }
    } else {
      // 在浏览器环境中使用传统下载方式
      const link = document.createElement("a");
      link.download = filename;
      link.href = dataURL;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  } catch (error: any) {
    console.warn("导出图片失败:", error);
    throw error;
  }
};

/**
 * 导出文本文件
 * @param content 文本内容
 * @param filename 建议的文件名
 * @param mimeType Mime类型
 */
export const exportTextFile = async (
  content: string,
  filename: string,
  mimeType = "text/plain",
): Promise<void> => {
  try {
    if (typeof window !== "undefined" && window.ipcRenderer) {
      // Electron 环境
      const result = await window.ipcRenderer.invoke("dialog:showSaveDialog", {
        defaultPath: filename,
        filters: [
          { name: "Text Files", extensions: ["txt", "dot"] },
          { name: "All Files", extensions: ["*"] },
        ],
      });

      if (!result.canceled && result.filePath) {
        await window.ipcRenderer.invoke(
          "fs:writeFile",
          result.filePath,
          content,
        );
      }
    } else {
      // 浏览器环境
      downloadFile(content, filename, mimeType);
    }
  } catch (error: any) {
    console.warn(`导出文本文件失败:`, error);
    throw error;
  }
};

/**
 * 将 SVG 元素导出为图片
 * @param svgElement - 要导出的 SVG 元素
 * @param filename - 建议的文件名
 * @param backgroundColor - 图片背景色
 */
export const exportSvgToImage = (
  svgElement: SVGSVGElement,
  filename: string,
  backgroundColor = "#ffffff",
): Promise<void> => {
  return new Promise((resolve, reject) => {
    const svgString = new XMLSerializer().serializeToString(svgElement);
    const canvas = document.createElement("canvas");
    const ctx = canvas.getContext("2d");
    if (!ctx) {
      return reject(new Error("Could not get canvas context."));
    }
    const img = new Image();
    const svgBlob = new Blob([svgString], {
      type: "image/svg+xml;charset=utf-8",
    });
    const url = URL.createObjectURL(svgBlob);

    img.onload = async () => {
      try {
        const pixelRatio = 2;
        canvas.width = img.width * pixelRatio;
        canvas.height = img.height * pixelRatio;
        ctx.fillStyle = backgroundColor;
        ctx.fillRect(0, 0, canvas.width, canvas.height);
        ctx.drawImage(img, 0, 0, canvas.width, canvas.height);
        const dataURL = canvas.toDataURL("image/png");
        await exportChartImage(dataURL, filename);
        URL.revokeObjectURL(url);
        resolve();
      } catch (error) {
        reject(error);
      }
    };

    img.onerror = () => {
      URL.revokeObjectURL(url);
      reject(new Error("加载 SVG 图像失败，无法导出"));
    };

    img.src = url;
  });
};
/**
 * 导出模型文件
 * @param modelData - 模型数据（Blob 格式）
 * @param filename - 建议的文件名
 * @param uids - 模型UID数组，用于判断文件类型
 */
export const exportModelFile = async (
  modelData: Blob,
  filename: string,
  uids?: string[],
): Promise<void> => {
  try {
    if (typeof window !== "undefined" && window.ipcRenderer) {
      // 根据UID数量判断文件类型
      const isMultipleModels = uids && uids.length > 1;
      const fileExtensions = isMultipleModels ? ["zip"] : ["joblib", "model"];
      const defaultExtension = isMultipleModels ? ".zip" : ".joblib";

      // 确保文件名有正确的扩展名
      let finalFilename = filename;
      if (!filename.includes(".")) {
        finalFilename = filename + defaultExtension;
      }

      // Electron 环境：使用保存对话框
      const result = await window.ipcRenderer.invoke("dialog:showSaveDialog", {
        defaultPath: finalFilename,
        filters: [
          {
            name: isMultipleModels ? "ZIP 压缩包" : "模型文件",
            extensions: fileExtensions,
          },
          { name: "所有文件", extensions: ["*"] },
        ],
      });

      if (!result.canceled && result.filePath) {
        // 将 Blob 转换为 Buffer 并保存
        const arrayBuffer = await modelData.arrayBuffer();
        await window.ipcRenderer.invoke(
          "fs:writeFileBuffer",
          result.filePath,
          arrayBuffer,
        );
      }
    } else {
      // 浏览器环境：使用传统下载方式
      const url = URL.createObjectURL(modelData);
      const link = document.createElement("a");
      link.href = url;
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
    }
  } catch (error: any) {
    console.warn("导出模型文件失败:", error);
    throw error;
  }
};

export const exportProjectFile = async (
  projectData: Blob,
  filename: string,
): Promise<void> => {
  try {
    if (typeof window !== "undefined" && window.ipcRenderer) {
      // 确保文件名有正确的扩展名
      let finalFilename = filename;
      if (!filename.includes(".")) {
        finalFilename = filename + ".zip";
      }

      // Electron 环境：使用保存对话框
      const result = await window.ipcRenderer.invoke("dialog:showSaveDialog", {
        defaultPath: finalFilename,
        filters: [
          {
            name: "项目文件",
            extensions: [".zip"],
          },
          { name: "所有文件", extensions: ["*"] },
        ],
      });

      if (!result.canceled && result.filePath) {
        // 将 Blob 转换为 Buffer 并保存
        const arrayBuffer = await projectData.arrayBuffer();
        await window.ipcRenderer.invoke(
          "fs:writeFileBuffer",
          result.filePath,
          arrayBuffer,
        );
      }
    } else {
      // 浏览器环境：使用传统下载方式
      const url = URL.createObjectURL(projectData);
      const link = document.createElement("a");
      link.href = url;
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
    }
  } catch (error: any) {
    console.warn("导出模型文件失败:", error);
    throw error;
  }
};

/**
 * 增强的项目导出功能
 * 包含数据校验、工作区打包和压缩功能
 */
export const exportProjectWithWorkspace = async (
  currentWorkspacePath?: string,
): Promise<boolean> => {
  try {
    // 1. 检查工作区大小
    if (currentWorkspacePath) {
      const canContinue = await checkWorkspaceSizeAndWarn(currentWorkspacePath);
      if (!canContinue) {
        return false;
      }
    }

    ElMessage.info("正在导出项目数据...");

    // 2. 获取工作区首层目录名称
    const workspaceName = currentWorkspacePath
      ? currentWorkspacePath
          .replace(/[\\/]+$/, "")
          .split(/[\\/]/)
          .pop() || ""
      : "";

    if (!workspaceName) {
      ElMessage.error("工作区路径不存在，请检查工作区状态");
      return false;
    }

    // 3. 向后端请求项目数据
    const response = await exportProject(workspaceName);
    const projectBlob = (response as any).data;
    const headers = (response as any).headers || {};

    // 4. 获取校验和
    const checksum = headers["x-zip-md5"];

    console.log("Checksum:", checksum);
    console.log("Blob:", projectBlob);
    console.log("Headers:", headers);

    // 5. 验证数据校验和
    if (checksum) {
      const isValid = await verifyBlobWithMessage(projectBlob, checksum);
      if (!isValid) {
        return false;
      }
    } else {
      console.warn("未找到校验和，跳过数据验证");
      ElMessage.warning("未找到数据校验和，跳过验证步骤");
    }

    // 6. 打包工作区文件（如果存在工作区）
    let workspaceBlob: Blob | null = null;
    if (currentWorkspacePath) {
      ElMessage.info("正在打包工作区文件...");
      try {
        workspaceBlob = await packageWorkspace(currentWorkspacePath);
      } catch (error) {
        console.error("打包工作区失败:", error);
        ElMessage.warning("工作区打包失败，将仅导出项目数据");
      }
    }

    // 7. 合并所有数据到一个zip文件
    ElMessage.info("正在生成最终导出文件...");
    let finalBlob: Blob;

    if (workspaceBlob) {
      // 如果有工作区数据，合并项目数据和工作区数据
      finalBlob = await mergeProjectAndWorkspace(
        projectBlob,
        workspaceBlob,
        "mldesktop_project",
      );
    } else {
      // 如果没有工作区数据，仅导出项目数据
      finalBlob = projectBlob;
    }

    // 8. 使用现有的导出工具保存文件
    const timestamp = new Date().toISOString().replace(/[:.]/g, "-");
    const filename = `MLDesktop_Export_${timestamp}.zip`;

    await exportProjectFile(finalBlob, filename);

    ElMessage.success("项目导出完成！");
    return true;
  } catch (error) {
    console.error("导出项目状态失败:", error);
    ElMessage.error("导出项目状态失败");
    return false;
  }
};

/**
 * 选择导入文件
 * @returns Promise<File | null> - 选择的文件，取消则返回null
 */
export const selectImportFile = async (): Promise<File | null> => {
  try {
    if (typeof window !== "undefined" && window.ipcRenderer) {
      // Electron 环境：使用文件选择对话框
      const result = await window.ipcRenderer.invoke("dialog:showOpenDialog", {
        title: "选择项目导入文件",
        filters: [
          { name: "项目文件", extensions: ["zip", "blob"] },
          { name: "所有文件", extensions: ["*"] },
        ],
        properties: ["openFile"],
      });

      if (!result.canceled && result.filePaths.length > 0) {
        const filePath = result.filePaths[0];
        const fileBuffer = await window.ipcRenderer.invoke(
          "fs:readFileBuffer",
          filePath,
        );
        const fileName = filePath.split(/[\\/]/).pop() || "import_file";

        // 创建File对象
        const file = new File([fileBuffer], fileName, {
          type: filePath.endsWith(".zip")
            ? "application/zip"
            : "application/octet-stream",
        });

        return file;
      }
    } else {
      // 浏览器环境：使用文件输入
      return new Promise((resolve) => {
        const input = document.createElement("input");
        input.type = "file";
        input.accept = ".zip,.blob";
        input.onchange = (event) => {
          const file = (event.target as HTMLInputElement).files?.[0] || null;
          resolve(file);
        };
        input.click();
      });
    }
    return null;
  } catch (error) {
    console.error("选择导入文件失败:", error);
    return null;
  }
};

/**
 * 导入项目功能
 * @returns Promise<boolean> - 是否导入成功
 */
export const importProjectWithWorkspace = async (): Promise<boolean> => {
  try {
    // 1. 选择导入文件
    const importFile = await selectImportFile();
    if (!importFile) {
      return false;
    }

    ElMessage.info("正在解析导入文件...");

    // 2. 解析导出包
    const { projectBlob, workspaceBlob } = await parseExportPackage(importFile);

    if (!projectBlob) {
      ElMessage.error("导入文件格式错误：未找到项目数据");
      return false;
    }

    // 3. 处理工作区恢复
    let targetWorkspacePath = null;
    if (workspaceBlob) {
      ElMessage.info("正在恢复工作区文件...");
      const selectedPath = await selectDirectory("选择工作区恢复路径");
      if (selectedPath) {
        targetWorkspacePath = selectedPath;
      }
      if (targetWorkspacePath) {
        const extractSuccess = await extractWorkspace(
          workspaceBlob,
          targetWorkspacePath,
        );
        if (extractSuccess) {
          ElMessage.success("工作区文件恢复完成");
        } else {
          ElMessage.warning("工作区文件恢复失败，但项目数据已成功导入");
        }
        workspaceStore.setWorkspacePath(targetWorkspacePath);
        if (workspaceStore.clearSingleFileMode) workspaceStore.clearSingleFileMode();
      } else {
        ElMessage.info("已跳过工作区恢复");
      }
    }

    const workspaceName = targetWorkspacePath
      ? targetWorkspacePath
          .replace(/[\\/]+$/, "")
          .split(/[\\/]/)
          .pop() || ""
      : "";
    ElMessage.info("正在导入项目数据...");
    const importResponse = await importProject(workspaceName, projectBlob);

    if (!importResponse.data.success) {
      ElMessage.error(`项目数据导入失败: ${importResponse.data.message}`);
      return false;
    }

    ElMessage.success("项目导入完成！");
    return true;
  } catch (error) {
    console.error("导入项目失败:", error);
    ElMessage.error("导入项目失败");
    return false;
  }
};
