/**
 * 优化请求参数
 */
interface SingleOptimizationRequest {
  uid: string; // 预测模型ID，更名为uid
  target: {
    // 优化目标对象
    name: string; // 优化目标名称
    value: number; // 目标值
  };
  search: {
    // 搜索算法参数对象
    algorithm: string; // 优化算法ID
    params: Record<string, any>; // 优化算法参数
  };
  path: string;
}

interface MultipleOptimizationTarget {
  modelUid: string;
  target: {
    name: string;
    value: number;
  };
  weight: number;
  criterion: string;
}

interface MultipleOptimizationRequest {
  targets: MultipleOptimizationTarget[];
  search: {
    // 搜索算法参数对象
    algorithm: string; // 优化算法ID
    params: Record<string, any>; // 优化算法参数
  };
  path: string;
}

/**
 * 优化结果接口
 */
interface OptimizationResult {
  code: number;
  data: Array<Record<string, number>>; // 优化结果数组
  msg: string;
}

interface Search {
  id: number;
  uid: string;
  name: string;
  status: "completed" | "running" | "pending" | "failed";
  progress: string;
  asynchronous: boolean;
  isRabbitmqReady: boolean;
  createdAt: string;
  updatedAt: string;
  error: string | null;
  params: {
    algName: string;
    algParam: Record<string, any>;
    asynchronous: boolean;
    isRabbitmqReady: boolean;
    modelUid: string;
    targetName: string;
    targetValue: number;
    uid: string;
  };
}

interface GetSearchListResponse {
  code: number;
  data: Search[];
  msg: string;
}

interface GetSearchInfoResponse {
  code: number;
  data: Record<string, any>[];
  msg: string;
}

export {
  SingleOptimizationRequest,
  MultipleOptimizationTarget,
  MultipleOptimizationRequest,
  OptimizationResult,
  Search,
  GetSearchListResponse,
  GetSearchInfoResponse,
};
