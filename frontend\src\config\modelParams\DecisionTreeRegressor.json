{"name": "DecisionTreeRegressor", "displayName": "决策树回归", "description": "基于树结构的回归算法", "category": "tree", "type": "regression", "defaultParams": {"maxDepth": {"value": 5, "type": "number", "optimizeParam": true, "description": "决策树的最大深度，控制树的复杂度", "displayName": "最大深度", "min": 3, "max": 8, "step": 1, "nullable": true}, "minSamplesSplit": {"value": 2, "type": "number", "optimizeParam": true, "description": "分割内部节点所需的最小样本数", "displayName": "最小分割样本数", "min": 2, "max": 8, "step": 1}, "minSamplesLeaf": {"value": 1, "type": "number", "optimizeParam": true, "description": "叶节点所需的最小样本数", "displayName": "叶节点最小样本数", "min": 2, "max": 10, "step": 1}, "criterion": {"value": "squared_error", "type": "select", "description": "衡量分割质量的函数", "displayName": "分割标准", "options": [{"label": "均方误差", "value": "squared_error"}, {"label": "平均绝对误差", "value": "absolute_error"}, {"label": "泊松偏差", "value": "poisson"}, {"label": "<PERSON>均方误差", "value": "friedman_mse"}]}, "randomState": {"value": 42, "type": "number", "optimizeParam": false, "description": "控制随机性的种子值", "displayName": "随机种子", "nullable": true}}, "evaluation": {"splitDataset": {"value": false, "description": "是否划分训练/测试集"}, "trainRatio": {"value": 70, "min": 50, "max": 90, "step": 5, "description": "训练集比例(%)"}, "randomState": {"value": 42, "min": 0, "max": 1000, "description": "随机种子"}, "useModelValidation": {"value": false, "description": "是否使用交叉验证"}, "validationType": {"value": "k-fold", "options": ["k-fold", "leave-one-out"], "description": "验证方法"}, "kFolds": {"value": 5, "min": 2, "max": 10, "description": "k折交叉验证的k值"}}, "tips": ["决策树适用于分类和回归问题，具有良好的可解释性", "max_depth参数控制模型复杂度，过大容易过拟合", "min_samples_split和min_samples_leaf可以防止过拟合", "Gini不纯度计算速度快，信息增益更准确但计算复杂"], "introduction": {"detailedDescription": "决策树算法通过递归地选择最优特征和分割点来构建树结构。每个内部节点代表一个特征上的测试，每个分支代表测试的结果，每个叶节点代表一个类别标签或数值。算法使用信息增益、基尼不纯度等指标来选择最优分割。", "usageTips": ["适用于特征和目标变量关系较为明确的问题", "对缺失值和异常值相对不敏感", "容易过拟合，建议设置适当的树深度", "结果易于解释和可视化"], "scenarios": "适用于特征和目标变量关系较为明确的问题，结果易于解释和可视化。", "mainParams": [{"name": "max_depth", "description": "树的最大深度，控制模型复杂度"}, {"name": "min_samples_split", "description": "分割内部节点所需的最小样本数"}, {"name": "criterion", "description": "分割质量的评估标准（gini或entropy）"}]}}