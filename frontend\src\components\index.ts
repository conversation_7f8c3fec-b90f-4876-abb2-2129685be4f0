// 全局组件导出文件

// 基础UI组件
export * from './ReAuth';
export * from './ReCol';
export * from './ReDialog';
export * from './ReIcon';
export * from './RePerms';
export * from './RePureTableBar';
export * from './ReSegmented';
export * from './ReText';

// 业务模块组件
export * from './fileManagement';
export * from './dataProcessing';
export * from './modelManagement';

// 向后兼容 - 直接导出常用组件
export { default as DataTable } from './dataProcessing/src/dataTable/index.vue';
export { default as WorkspaceFileTree } from './fileManagement/src/WorkspaceFileTree/index.vue';
export { default as UploadFileButton } from './fileManagement/src/uploadFileButton/index.vue';

// 模型相关组件
export { default as ModelDialog } from "./modelManagement/src/ModelDialog/index.vue";

// 数据处理组件
export { default as DataPreprocess } from './dataProcessing/src/DataPreprocess/index.vue';

// 模型结果组件
export { default as ModelInfoCard } from "./modelManagement/src/ModelInfoCard/index.vue";
export { default as ModelCharts } from "./modelManagement/src/ModelCharts/index.vue";
export { default as MetricsTable } from "./modelManagement/src/MetricsTable/index.vue";
export { default as PredictionTable } from "./modelManagement/src/PredictionTable/index.vue";
export { default as DataSplitTable } from "./modelManagement/src/DataSplitTable/index.vue";
