/**
 * 虚拟样本生成组件导出
 */

import VirtualSampleGeneration from './index.vue';
import ToolboxPanel from './components/ToolboxPanel.vue';
import ConstraintsPanel from './components/ConstraintsPanel.vue';
import GenerationSettingsPanel from './components/GenerationSettingsPanel.vue';
import ResultDialog from './components/ResultDialog.vue';
import AdvancedSettingsDialog from './components/AdvancedSettingsDialog.vue';

// 默认导出
export default VirtualSampleGeneration;

// 命名导出
export { 
  VirtualSampleGeneration,
  ToolboxPanel,
  ConstraintsPanel,
  GenerationSettingsPanel,
  ResultDialog,
  AdvancedSettingsDialog
};

// 导出类型定义
export * from './types/index';
