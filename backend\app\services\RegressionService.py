from app.types.model_request import ModelConfig
from app.utils.Regression import reg_model_config
from typing import Optional, Tuple, Any, Union, Callable, TypedDict
import re
from sklearn.preprocessing import StandardScaler
from app.utils.eval import cal_reg_metric
from sklearn.model_selection import KFold, LeaveOneOut
from sklearn.inspection import permutation_importance
import numpy as np
import pandas as pd
import time
from collections import OrderedDict
from app.utils.shap import extract_shap_values
from itertools import product

def check_alg_param(alg_name: str, alg_param: Optional[dict]) -> Tuple[Any, dict]:
    # check params
    if alg_name not in reg_model_config.keys():
        raise ValueError("Invalid algorithm name")
    alg = reg_model_config[alg_name]["model"]
    params = reg_model_config[alg_name]["model_params"].copy()
    if alg_param is not None:
        for key in alg_param.keys():
            params[key] = alg_param[key]

    # SVM 特殊处理
    if alg_name == "SVR":
        if 'c' in params.keys():
            C = params.pop('c')
            params['C'] = C

    # MLPRegressor 特殊处理
    if alg_name == "MLPRegressor":
        hidden_layer_sizes = re.findall(r'\d+', params['hidden_layer_sizes'])
        params['hidden_layer_sizes'] = tuple(int(size) for size in hidden_layer_sizes)
    
    return alg, params

def extract_tree_structure(model: object) -> None:
    if hasattr(model, "tree_"):
        from sklearn import tree
        tree_structure = tree.export_graphviz(model, out_file=None)
    else:
        return None
    return tree_structure

def extract_feature_importance(model: object, feature_names: list) -> None:
    feature_importance = []
    if hasattr(model, "feature_importances_"):
        importance_values = np.abs(model.feature_importances_)
    elif hasattr(model, "coef_"):
        importance_values = np.abs(model.coef_)
    else:
        return None
    indices = np.argsort(importance_values)[::-1]
    if importance_values is not None:
        for idx in indices:
            fname = feature_names[idx]
            value = importance_values[idx]
            feature_importance.append({
                "feature": fname,
                "importance": float(value)
            })
    return feature_importance

def extract_shap_analysis(alg: object, x_train: pd.DataFrame, y_train: pd.Series, feature_names: list, params: dict) -> list:
    shap_values = extract_shap_values(alg, x_train, y_train, fit_params=params)
    if isinstance(shap_values, list):
        shap_values = shap_values[0]

    if len(shap_values.shape) == 1:
        shap_values = shap_values.reshape(1, -1)

    feature_importance = shap_values.abs.mean(0).values

    sorted_indices = np.argsort(feature_importance)[::-1]

    max_display = min(10, len(feature_names))
    selected_indices = sorted_indices[:max_display]

    beeswarm_data = []
    for y_i, idx in enumerate(selected_indices):
        feature_name = feature_names[idx]
        feature_shap_values = shap_values[:, idx].values
        feature_values = x_train.iloc[:, idx].values
        
        feature_data = []
        for j in range(len(feature_shap_values)):
            feature_data.append({
                "shap_value": float(feature_shap_values[j]),
                "feature_value": float(feature_values[j]),
            })
        feature_data = pd.DataFrame(feature_data)
        feature_data['y_offset'] = y_i + np.random.normal(0, 0.5, len(feature_data))
        feature_data = feature_data.to_dict(orient="records")
        
        beeswarm_data.append({
            "feature_name": feature_name,
            "points": feature_data,
            "importance": float(feature_importance[idx])
        })
    return beeswarm_data

def extract_pfi(model: object, x_train: pd.DataFrame, y_train: pd.Series, feature_names: list) -> None:
    pfi = []
    r = permutation_importance(model, x_train, y_train, n_repeats=30, random_state=0)
    for i in r.importances_mean.argsort()[::-1]:
        pfi.append({
            "feature": feature_names[i],
            "importance": r.importances_mean[i],
            "std": r.importances_std[i]
        })
    return pfi

def check_params_type(params: Union[dict, OrderedDict]) -> Union[dict, OrderedDict]:
    for k, v in params.items():
        if k in [
            "n_estimators", 
            "n_neighbors", 
            "n_jobs", 
            "n_splits", 
            "n_components", 
            "n_features", 
            "n_features_out", 
            "n_iter", 
            "max_iter", 
            "random_state", 
            "max_depth",
            "min_samples_leaf",
            "min_samples_split",
            "degree"
            ]:
            if isinstance(v, list):
                params[k] = [int(size) for size in v]
            else:
                params[k] = int(v) if v is not None else None
        else:
            params[k] = v
    return params

def check_optimize_params(optimize_params: dict) -> dict:
    """
    return {
        "params": params,
        "other_params": other_params,
        "total_length": total_length
    }
    """
    params = {}
    other_params = {}
    total_length = 1
    for k, v in optimize_params.items():
        if isinstance(v, list) and len(v) > 1:
            if isinstance(v[0], str) and v[0].replace("_", "").isalpha():
                params[k] = v
                total_length *= len(v)
            elif len(v) == 2:
                step = int((v[1]-v[0])/(v[1]*0.1))+1
                params[k] = np.linspace(v[0], v[1], step, endpoint=True).tolist()
                total_length *= step
            elif len(v) == 3:
                step = int((v[1]-v[0])/v[2])+1
                params[k] = np.linspace(v[0], v[1], step, endpoint=True).tolist()
                total_length *= step
            else:
                raise ValueError(f"Invalid parameter: {k}")
        else:
            other_params[k] = v
    return {
        "params": params,
        "other_params": other_params,
        "total_length": total_length
    }

class Eval(TypedDict):
    """
    {
        "y_true": y_true,
        "y_predict": y_predict,
        "metrics": metrics
    }
    """
    y_true: Optional[list]
    y_predict: Optional[list]
    metrics: Optional[dict]

class ModelFile(TypedDict):
    """
    {
        "model": model,
        "scaler": scaler,
        "feature_names": feature_names,
        "info": info
    }
    """
    model: Optional[object]
    scaler: Optional[object]
    feature_names: Optional[list]
    info: Optional[dict]

class ModelInfo(TypedDict):
    """
    {
        "task_type": "reg",
        "alg": "alg_name",
        "model_params": model_config.to_dict(),
        "optimized_result": {},
        "optimized_params": {},
        "eval": {},
        "feature_analysis": {}
    }
    """
    task_type: str
    alg: str
    model_params: dict
    optimized_result: dict
    optimized_params: dict
    eval: dict
    feature_analysis: dict

class RegressionService(object):

    @staticmethod
    def init_model_info(model_config: ModelConfig) -> ModelInfo:
        info: ModelInfo = {
            "task_type": "reg",
            "alg": model_config.alg_name,
            "model_params": model_config.to_dict(),
            "optimized_result": {},
            "optimized_params": {},
            "eval": {},
            "feature_analysis": {}
        }
        for yname in model_config.y_train.columns:
            info["eval"][yname] = {
                "train": {
                    "y_true": None,
                    "y_predict": None,
                    "metrics": None
                },
                "cv": {
                    "y_true": None,
                    "y_predict": None,
                    "metrics": None
                },
                "loocv": {
                    "y_true": None,
                    "y_predict": None,
                    "metrics": None
                },
                "test": {
                    "y_true": None,
                    "y_predict": None,
                    "metrics": None
                },
            }
            info["optimized_result"][yname] = None
            info["optimized_params"][yname] = None
            info["feature_analysis"][yname] = {
                "importance": None,
                "tree_structure": None,
                "pfi": None,
                "shap_analysis": None
            }
        return info
    
    @staticmethod
    def init_model_file(model_config: ModelConfig) -> ModelFile:
        file: ModelFile = {
            "model": {},
            "scaler": {},
            "feature_names": {},
            "info": {}
        }
        for yname in model_config.y_train.columns:
            file["model"][yname] = None
            file["scaler"][yname] = None
            file["feature_names"][yname] = model_config.x_train.columns.tolist()
            file["info"][yname] = None
        return file

    @staticmethod
    def cal_steps(model_config: ModelConfig) -> int:
        return (np.array([model_config.optimize, model_config.cv, model_config.loocv, model_config.test]).astype(int).sum() + 1) * len(model_config.y_train.columns)

    @staticmethod
    def check_model_config(model_config: ModelConfig) -> dict:
        alg, params = check_alg_param(model_config.alg_name, model_config.alg_param)
        if not model_config.optimize:
            params = check_params_type(params)
        else:
            params = check_optimize_params(params)
        return alg, params

    @staticmethod
    def train(alg: object, x_train: pd.DataFrame, y_train: pd.Series, params: dict, __callable__: Callable) -> Tuple[object, Eval]:
        model = alg(**params).fit(x_train, y_train)
        __callable__(stage="train", progress=50, sub_progress="")
        return model, {
            "y_true": y_train.tolist(),
            "y_predict": model.predict(x_train).tolist(),
            "metrics": cal_reg_metric(y_train, model.predict(x_train))
        }
    
    @staticmethod
    def test(model: object, x_test: pd.DataFrame, y_test: pd.Series, __callable__: Callable) -> Eval:
        predictions = model.predict(x_test)
        __callable__(stage="test", progress=100, sub_progress="")
        return {
            "y_true": y_test.tolist(),
            "y_predict": predictions.tolist(),
            "metrics": cal_reg_metric(y_test, predictions)
        }
    
    @staticmethod
    def predict(model: object, x_test: pd.DataFrame) -> np.ndarray:
        return model.predict(x_test)
    
    @staticmethod
    def leave_one_out(alg: object, x_train: pd.DataFrame, y_train: pd.Series, params: dict, __callable__: Callable) -> Eval:
        loo = LeaveOneOut()
        predictions = []
        for train_index, test_index in loo.split(x_train):
            sub_progress = f"{len(predictions)+1}/{x_train.shape[0]}"
            progress = len(predictions)/x_train.shape[0]*100
            __callable__(stage="loocv", progress=progress, sub_progress=sub_progress)
            xtrain, ytrain = x_train.iloc[train_index], y_train.iloc[train_index]
            xtest, ytest = x_train.iloc[test_index], y_train.iloc[test_index]
            model = alg(**params).fit(xtrain, ytrain)
            predictions.append(model.predict(xtest).astype(float)[0])
        return {
            "y_true": y_train.tolist(),
            "y_predict": predictions,
            "metrics": cal_reg_metric(y_train, predictions)
        }
    
    @staticmethod
    def cross_validation(cv: int, alg: object, x_train: pd.DataFrame, y_train: pd.Series, params: dict, __callable__: Callable) -> Eval:
        cv = cv or 5
        kf = KFold(n_splits=cv, shuffle=True, random_state=42)
        predictions = []
        obs = []
        for index, (train_index, test_index) in enumerate(kf.split(x_train)):
            sub_progress = f"{index+1}/{x_train.shape[0]}"
            progress = index/x_train.shape[0]*100
            __callable__(stage="cv", progress=progress, sub_progress=sub_progress)
            xtrain, ytrain = x_train.iloc[train_index], y_train.iloc[train_index]
            xtest, ytest = x_train.iloc[test_index], y_train.iloc[test_index]
            model = alg(**params).fit(xtrain, ytrain)
            predictions += model.predict(xtest).astype(float).tolist()
            obs += ytest.tolist()
        predictions = np.array(predictions)
        obs = np.array(obs)
        return {
            "y_true": obs.tolist(),
            "y_predict": predictions.tolist(),
            "metrics": cal_reg_metric(obs, predictions)
        }
    
    @staticmethod
    def optimize(alg: object, x_train: pd.DataFrame, y_train: pd.Series, params: dict, __callable__: Callable) -> dict:
        total_length = params.get("total_length")
        other_params = params.get("other_params")
        params = params.get("params")
        results = []
        for i, params_values in enumerate(product(*params.values())):
            sub_progress = f"{i+1}/{total_length}"
            progress = i/total_length*100
            __callable__(stage="optimize", progress=progress, sub_progress=sub_progress)
            param_dict = OrderedDict(zip(params.keys(), params_values))
            param_dict = check_params_type(param_dict)
            param_dict.update(other_params)
            metrics = RegressionService.cross_validation(cv=5, alg=alg, x_train=x_train, y_train=y_train, params=param_dict, __callable__=lambda *args, **kwargs: None)['metrics']
            if metrics is not None:
                param_dict.update(metrics)
                results.append(param_dict)
        optimized_result = pd.DataFrame(results)
        optimized_params = optimized_result.iloc[optimized_result["MAE"].idxmin()].loc[params.keys()].to_dict()
        optimized_params = check_params_type(optimized_params)
        return {
            "optimized_result": optimized_result,
            "optimized_params": optimized_params
        }

    @staticmethod
    def feature_analysis(model: object, alg: object, x_train: pd.DataFrame, y_train: pd.Series, feature_names: list, params: dict, shap: bool, __callable__: Callable) -> dict:
        analysis_result = {}
        __callable__(stage="feature_analysis", progress=60, sub_progress="importance")
        analysis_result["importance"] = extract_feature_importance(model, feature_names)
        analysis_result["tree_structure"] = extract_tree_structure(model)
        analysis_result["pfi"] = extract_pfi(model, x_train, y_train, feature_names)
        if shap:
            __callable__(stage="feature_analysis", progress=75, sub_progress="shap_analysis")
            analysis_result["shap_analysis"] = extract_shap_analysis(alg, x_train, y_train, feature_names, params)
        __callable__(stage="feature_analysis", progress=100, sub_progress="completed")
        return analysis_result