import type { LoadingInstance } from "element-plus/es/components/loading/src/loading";
import type { AxiosResponse, AxiosError } from "axios";
import axios from "axios";
import { ElLoading, ElMessage } from "element-plus";
import type {
  ApiResponse,
  CustomRequestConfig,
  CustomAxiosInstance,
} from "@/types/request";

/**
 * @description axios初始化
 */
const instance: CustomAxiosInstance = axios.create({
  baseURL: `${import.meta.env.VITE_APP_BASE_URL}` as string,
  timeout: 50000,
  headers: {
    "Content-Type": "application/json;charset=UTF-8",
  },
});

let loadingInstance: LoadingInstance | null = null;
let needLoadingRequestCount = 0;

instance.interceptors.request.use(
  (config: CustomRequestConfig): CustomRequestConfig => {
    if (!config.skipLoading) {
      if (needLoadingRequestCount === 0) {
        loadingInstance = ElLoading.service({
          fullscreen: true,
          text: "加载中...",
          background: "rgba(0, 0, 0, 0.7)",
        });
      }
      needLoadingRequestCount++;
    }
    return config;
  },
  (error: AxiosError) => {
    if (!(error.config as CustomRequestConfig)?.skipLoading) {
      needLoadingRequestCount--;
      if (needLoadingRequestCount === 0 && loadingInstance) {
        loadingInstance.close();
      }
    }
    return Promise.reject(error);
  },
);

instance.interceptors.response.use(
  (response: AxiosResponse<ApiResponse<any>>): any => {
    const config = response.config as CustomRequestConfig;
    if (!config.skipLoading) {
      needLoadingRequestCount--;
      if (needLoadingRequestCount === 0 && loadingInstance) {
        loadingInstance.close();
      }
    }

    const { status, data } = response;

    // Handle blob responses (like file downloads)
    if (config.responseType === "blob") {
      if (status !== 200) {
        const errorMsg = `请求错误${status}`;
        ElMessage.error(errorMsg);
        return Promise.reject(new Error(errorMsg));
      }
      // Return the raw response for blob data with headers
      return {
        data: data,
        headers: response.headers,
      };
    }

    // Handle regular JSON responses
    if (status !== 200 || (data.code !== 200 && !data.size)) {
      const errorMsg = data.msg || `请求错误${status}`;
      ElMessage.error(errorMsg);
      return Promise.reject(new Error(errorMsg));
    }

    return data;
  },
  async (error: AxiosError<ApiResponse>) => {
    const config = error.config as CustomRequestConfig;
    if (!config.skipLoading) {
      needLoadingRequestCount--;
      if (needLoadingRequestCount === 0 && loadingInstance) {
        loadingInstance.close();
      }
    }

    let errorMsg = "请求失败";
    if (error.response) {
      const { status, data } = error.response;
      errorMsg = data.msg || `请求错误${status}`;
    } else if (error.request) {
      errorMsg = "请求超时";
    }
    ElMessage.error(errorMsg);
    return Promise.reject(error);
  },
);

export default instance;
