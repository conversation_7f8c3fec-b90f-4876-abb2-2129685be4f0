export default {
  path: "/model",
  redirect: "/model/list",
  meta: {
    icon: "ri:list-indefinite",
    showLink: true,
    title: "模型",
    rank: 10,
  },
  children: [
    {
      path: "/model/list",
      name: "modelList",
      component: () => import("@/views/model/modelList/index.vue"),
      meta: {
        title: "模型列表",
        keepAlive: true,
      },
    },
  ],
} satisfies RouteConfigsTable;
