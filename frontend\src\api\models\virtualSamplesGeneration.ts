import request from "@/utils/request";
import { AxiosHeaders } from "axios";
import { VirtualSamplesGenerationRequest, VirtualSamplesGenerationResponse } from "@/types/virtualSamples";

// 虚拟样本生成 API 调用
export const reqVirtualSamplesGeneration = (data: VirtualSamplesGenerationRequest) => {
  return request.post<VirtualSamplesGenerationResponse>("/virtual/generate", data, {
    headers: new AxiosHeaders({}),
    skipLoading: true,
  });
};