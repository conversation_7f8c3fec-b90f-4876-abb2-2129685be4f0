import { ElMessage } from "element-plus";
import md5 from "crypto-js/md5";
import { enc, lib } from "crypto-js";

/**
 * 将 Blob 转换为 ArrayBuffer
 * @param blob - 要转换的 Blob 数据
 * @returns Promise<ArrayBuffer>
 */
const blobToArrayBuffer = (blob: Blob): Promise<ArrayBuffer> => {
  return new Promise((resolve, reject) => {
    // 检查是否支持 arrayBuffer 方法
    if (blob.arrayBuffer && typeof blob.arrayBuffer === "function") {
      blob.arrayBuffer().then(resolve).catch(reject);
    } else {
      // 使用 FileReader 作为后备方案
      const reader = new FileReader();
      reader.onload = () => {
        if (reader.result instanceof ArrayBuffer) {
          resolve(reader.result);
        } else {
          reject(new Error("Failed to convert blob to ArrayBuffer"));
        }
      };
      reader.onerror = () => reject(reader.error);
      reader.readAsArrayBuffer(blob);
    }
  });
};

/**
 * 将 ArrayBuffer 转换为 WordArray (crypto-js 格式)
 * @param arrayBuffer - ArrayBuffer 数据
 * @returns WordArray
 */
const arrayBufferToWordArray = (arrayBuffer: ArrayBuffer): lib.WordArray => {
  const uint8Array = new Uint8Array(arrayBuffer);
  const words: number[] = [];

  for (let i = 0; i < uint8Array.length; i += 4) {
    const word =
      (uint8Array[i] << 24) |
      (uint8Array[i + 1] << 16) |
      (uint8Array[i + 2] << 8) |
      uint8Array[i + 3];
    words.push(word);
  }

  return lib.WordArray.create(words, uint8Array.length);
};

/**
 * 计算 Blob 数据的 MD5 校验和
 * @param blob - 要计算校验和的 Blob 数据
 * @returns Promise<string> - 十六进制格式的校验和字符串
 */
export const calculateBlobChecksum = async (blob: Blob): Promise<string> => {
  try {
    // 验证输入
    if (!blob || !(blob instanceof Blob)) {
      throw new Error("Invalid blob object");
    }

    // 将 Blob 转换为 ArrayBuffer
    const arrayBuffer = await blobToArrayBuffer(blob);

    // 将 ArrayBuffer 转换为 WordArray
    const wordArray = arrayBufferToWordArray(arrayBuffer);

    // 计算 MD5
    const hash = md5(wordArray);

    // 返回十六进制字符串
    return hash.toString(enc.Hex);
  } catch (error) {
    console.error("计算校验和失败:", error);
    throw new Error("校验和计算失败");
  }
};

/**
 * 验证 Blob 数据的校验和
 * @param blob - 要验证的 Blob 数据
 * @param expectedChecksum - 预期的校验和
 * @returns Promise<boolean> - 校验是否通过
 */
export const verifyBlobChecksum = async (
  blob: Blob,
  expectedChecksum: string,
): Promise<boolean> => {
  try {
    console.log(blob, expectedChecksum);
    if (!blob || !expectedChecksum) {
      console.error("Invalid parameters for checksum verification");
      return false;
    }

    const calculatedChecksum = await calculateBlobChecksum(blob);
    // 不区分大小写比较
    return calculatedChecksum.toLowerCase() === expectedChecksum.toLowerCase();
  } catch (error) {
    console.error("校验和验证失败:", error);
    return false;
  }
};

/**
 * 验证并提示校验结果
 * @param blob - 要验证的 Blob 数据
 * @param expectedChecksum - 预期的校验和
 * @returns Promise<boolean> - 校验是否通过
 */
export const verifyBlobWithMessage = async (
  blob: Blob,
  expectedChecksum: string,
): Promise<boolean> => {
  try {
    const isValid = await verifyBlobChecksum(blob, expectedChecksum);

    if (isValid) {
      ElMessage.success("数据校验通过");
      return true;
    } else {
      ElMessage.error("数据校验失败，文件可能已损坏");
      return false;
    }
  } catch (error) {
    console.error("校验过程中发生错误:", error);
    ElMessage.error("校验过程中发生错误");
    return false;
  }
};

/**
 * 分块计算大文件的 MD5（用于处理大文件）
 * @param blob - 要计算校验和的 Blob 数据
 * @param chunkSize - 每块大小（默认 2MB）
 * @returns Promise<string> - 十六进制格式的校验和字符串
 */
export const calculateBlobChecksumChunked = async (
  blob: Blob,
  chunkSize: number = 2 * 1024 * 1024, // 2MB
): Promise<string> => {
  try {
    if (!blob || !(blob instanceof Blob)) {
      throw new Error("Invalid blob object");
    }

    const chunks = Math.ceil(blob.size / chunkSize);
    const wordArrays: lib.WordArray[] = [];

    for (let i = 0; i < chunks; i++) {
      const start = i * chunkSize;
      const end = Math.min(start + chunkSize, blob.size);
      const chunk = blob.slice(start, end);

      const arrayBuffer = await blobToArrayBuffer(chunk);
      const wordArray = arrayBufferToWordArray(arrayBuffer);
      wordArrays.push(wordArray);
    }

    // 合并所有 WordArray
    const concatenated = wordArrays.reduce((acc, curr) => acc.concat(curr));

    // 计算 MD5
    const hash = md5(concatenated);

    return hash.toString(enc.Hex);
  } catch (error) {
    console.error("计算校验和失败:", error);
    throw new Error("校验和计算失败");
  }
};
