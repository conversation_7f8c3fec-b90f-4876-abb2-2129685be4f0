<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>正在加载...</title>
    <style>
        body, html {
            margin: 0;
            padding: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            background: transparent;
            font-family: "Segoe UI", "Microsoft YaHei",-apple-system, BlinkMacSystemFont, Roboto, "Helvetica Neue", Arial, sans-serif;
        }

        .container {
            display: flex;
            flex-direction: column;
            width: 100%;
            height: 100%;
            background-color: #ffffff;
            border-radius: 8px;
            color: #444;
            box-sizing: border-box;
        }

        .header-container {
            padding: 10px;
            margin: 10px;
            border-radius: 8px;
            background-color: #0078d4;
            width: fit-content;
        }

        .header {
            text-align: left;
            font-size: 12px;
            color: #ffffff;
            font-weight: bold;
        }

        .content {
            flex-grow: 1;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .logo {
            width: 100px;
            height: 100px;
        }

        .footer {
            padding: 20px;
            text-align: center;
        }

        .progress-container {
            width: 90%;
            max-width: 400px;
            height: 4px; /* 进度条更细一些 */
            background-color: #e0e0e0;
            border-radius: 2px;
            overflow: hidden;
            margin: 0 auto 10px; /* 居中并与下方文字拉开距离 */
        }

        .progress-bar {
            width: 0;
            height: 100%;
            background-color: #0078d4; /* Office 主题蓝 */
            border-radius: 2px;
            transition: width 0.3s ease-in-out;
        }

        .status-text {
            font-size: 12px;
            color: #666;
            height: 15px; /* 预留空间 */
        }

        .close-button {
            position: absolute;
            top: 10px;
            right: 15px;
            font-size: 24px;
            color: #999;
            cursor: pointer;
            font-weight: bold;
            -webkit-app-region: no-drag; /* 确保按钮可以点击 */
        }

        .close-button:hover {
            color: #333;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="close-button" id="closeBtn">&times;</div>
        <div class="header-container">
            <div class="header" id="appTitle">
                固体推进剂配方智能设计系统
            </div>
        </div>
        <div class="content">
            <img src="../src/assets/svg/logo_no_title.svg" alt="Logo" class="logo">
        </div>
        <div class="footer">
            <div class="progress-container">
                <div class="progress-bar" id="progressBar"></div>
            </div>
            <div class="status-text" id="statusText">正在初始化...</div>
        </div>
    </div>

    <script>
        // 监听版本号设置
        window.api.onSetVersion((event, version) => {
            const appTitle = document.getElementById('appTitle');
            if (appTitle) {
                appTitle.innerText = `固体推进剂配方智能设计系统 ${version}`;
            }
        });

        // 通过预加载脚本暴露的 API 监听来自主进程的进度更新
        window.api.onUpdateProgress((event, progress, text) => {
            const progressBar = document.getElementById('progressBar');
            const statusText = document.getElementById('statusText');
            if (progressBar) {
                progressBar.style.width = progress + '%';
            }
            if (statusText) {
                statusText.innerText = text;
            }
        });

        // 关闭按钮点击事件
        document.getElementById('closeBtn').addEventListener('click', () => {
            window.api.quitApp();
        });
    </script>
</body>
</html>