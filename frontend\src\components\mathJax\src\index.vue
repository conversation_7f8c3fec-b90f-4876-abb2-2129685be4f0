<template>
  <div ref="mathElement" class="mathjax-content" />
</template>

<script setup lang="ts">
import { ref, watch, onMounted, onBeforeUnmount, nextTick } from 'vue';

// MathJax imports (带 .js 后缀以兼容多数打包器)
import { mathjax } from 'mathjax-full/js/mathjax.js';
import { TeX } from 'mathjax-full/js/input/tex.js';
import { SVG } from 'mathjax-full/js/output/svg.js';
import { AllPackages } from 'mathjax-full/js/input/tex/AllPackages.js';
import { browserAdaptor } from 'mathjax-full/js/adaptors/browserAdaptor.js';
import { RegisterHTMLHandler } from 'mathjax-full/js/handlers/html.js';

// 定义组件名称
defineOptions({
  name: "MathJax"
});

const props = defineProps({
  formula: { type: String, default: '' },
  displayMode: { type: Boolean, default: true }
});

const mathElement = ref<HTMLElement | null>(null);

let mathDocument: any = null;
let resizeObserver: ResizeObserver | null = null;

// 初始化 MathJax document（在 onMounted 中调用）
const initMathJax = () => {
  const adaptor = browserAdaptor();
  RegisterHTMLHandler(adaptor);

  // InputJax（TeX）配置：保留常用选项
  const tex = new TeX({
    packages: AllPackages,
    inlineMath: [['$', '$']],
    displayMath: [['$$', '$$']],
    processEscapes: true,
    processEnvironments: true,
    processRefs: true,
    tags: 'ams',
    tagSide: 'right',
    tagIndent: '0.8em',
    multlineWidth: '85%',
    useLabelIds: true
  });

  const svg = new SVG({});

  // 将整个 document 交给 MathJax 处理（这样 convert 的 containerWidth 更准确）
  mathDocument = mathjax.document(document as any, { InputJax: tex, OutputJax: svg });
};

const renderMath = async () => {
  if (!mathElement.value) return;
  if (!mathDocument) initMathJax();
  if (!props.formula) {
    mathElement.value.innerHTML = '';
    return;
  }

  // 保证在 DOM 更新后再渲染
  await nextTick();

  // 清空旧内容
  mathElement.value.innerHTML = '';

  // 计算 containerWidth，更可靠的做法：使用 clientWidth
  const containerWidth = mathElement.value.clientWidth || 800;

  // convert 会返回一个 DOM node，可以直接 append
  try {
    const node = mathDocument.convert(props.formula, {
      display: props.displayMode,
      em: 16,
      ex: 8,
      containerWidth
    });
    // appendChild 之前确保 node 是 Element（MathJax 返回的是 DOM 树）
    mathElement.value.appendChild(node);
  } catch (err) {
    // 渲染失败时保留错误信息到控制台（便于调试）
    // 也可以在页面上显示占位或原始公式
    console.error('MathJax render error:', err);
    mathElement.value.textContent = props.formula;
  }
};

watch(() => props.formula, () => { renderMath(); });
watch(() => props.displayMode, () => { renderMath(); });

onMounted(() => {
  initMathJax();
  renderMath();

  // ResizeObserver：当容器宽度变化时重新渲染
  if (typeof ResizeObserver !== 'undefined' && mathElement.value) {
    resizeObserver = new ResizeObserver(() => {
      renderMath();
    });
    resizeObserver.observe(mathElement.value);
  } else {
    // 回退：监听窗口 resize
    window.addEventListener('resize', renderMath);
  }
});

onBeforeUnmount(() => {
  if (resizeObserver && mathElement.value) {
    resizeObserver.unobserve(mathElement.value);
    resizeObserver.disconnect();
    resizeObserver = null;
  } else {
    window.removeEventListener('resize', renderMath);
  }
  mathDocument = null;
});
</script>

<style scoped>
.mathjax-content {
  display: block; /* 改为 block 更稳妥 */
  width: 100%;
  line-height: 1.25;
  word-break: break-word;
}

</style>
