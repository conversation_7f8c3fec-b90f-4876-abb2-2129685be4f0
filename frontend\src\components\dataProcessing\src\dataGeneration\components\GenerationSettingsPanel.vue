<template>
  <div class="generation-settings-panel">
    <div class="panel-header">
      <h4>生成设置</h4>
      <el-icon class="settings-icon"><Setting /></el-icon>
    </div>
    
    <div class="scrollable-content">
      <!-- 生成算法选择 -->
      <div class="setting-group">
        <h5>生成算法</h5>
        <el-select
          v-model="generationParams.algorithm" 
          placeholder="请选择生成算法"
          class="full-width-select"
          @change="handleAlgorithmChange"
          :teleported="false"
        >
          <el-option 
            v-for="algorithm in availableAlgorithms" 
            :key="algorithm.id"
            :label="algorithm.config.displayName" 
            :value="algorithm.id" 
          />
        </el-select>
        <div v-if="selectedAlgorithm" class="algorithm-description">
          {{ selectedAlgorithm.config.description }}
        </div>
      </div>

      <!-- 动态参数设置 -->
      <div v-if="selectedAlgorithm" class="setting-group">
        <div class="setting-group-header">
          <h5>算法参数</h5>
          <el-button
            v-if="generationParams.algorithm === 'GridGeneration'"
            type="primary"
            size="small"
            @click="handleAdvancedSettings"
            :icon="Setting"
          >
            高级设置
          </el-button>
        </div>
        <div 
          v-for="(param, key) in selectedAlgorithm.config.defaultParams" 
          :key="key"
          class="param-item"
        >
          <div class="param-header">
            <div class="param-description">{{ param.description }}</div>
          </div>
          
          <!-- 数字类型参数 -->
          <el-input-number
            v-if="param.type === 'number'"
            v-model="generationParams.algorithmParams[key]"
            :min="param.min"
            :max="param.max"
            :step="param.step"
            :placeholder="`请输入${param.displayName || param.name}`"
            class="full-width-input"
            controls-position="right"
            :class="{ 'warning-input': isParamWarning(key, param) }"
            :disabled="isParamDisabled(key)"
            @change="handleParamChange"
          />
          
          <!-- 参数禁用提示 -->
          <div v-if="isParamDisabled(key)" class="param-disabled-tip">
            <el-icon class="tip-icon"><InfoFilled /></el-icon>
            <span class="tip-text">{{ getDisabledTipMessage(key) }}</span>
          </div>

          <div v-if="isParamWarning(key, param)" class="param-warning">
            <el-icon class="warning-icon"><Warning /></el-icon>
            <span class="warning-text">{{ getWarningMessage(key, param) }}</span>
          </div>
          
          <!-- 选择类型参数 -->
          <el-select
            v-else-if="param.type === 'select'"
            v-model="generationParams.algorithmParams[key]"
            :placeholder="`请选择${param.displayName || param.name}`"
            class="full-width-input"
            @change="handleParamChange"
          >
            <el-option
              v-for="option in param.options"
              :key="option.value"
              :value="option.value"
              :label="option.label"
            />
          </el-select>
          
          <!-- 布尔类型参数 -->
          <el-switch
            v-else-if="param.type === 'boolean'"
            v-model="generationParams.algorithmParams[key]"
            :active-text="param.displayName || param.name"
            inactive-text=""
            @change="handleParamChange"
          />
        </div>
      </div>

      <!-- 生成预览 -->
      <div class="setting-group">
        <h5>生成预览</h5>
        <div class="preview-info">
          <p>使用算法: <strong>{{ selectedAlgorithm?.config.displayName || '未选择' }}</strong></p>
          <p>约束条件: <strong>{{ validConstraintsCount > 0 ? validConstraintsCount + ' 个' : '无约束' }}</strong></p>
          <p>预计样本数: <strong :class="{ 'warning-count': calculatedSampleCount > 10000 }">{{ formatSampleCount(calculatedSampleCount) }}</strong></p>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed } from "vue";
import type { PropType } from "vue";
import { Setting, Warning, InfoFilled } from "@element-plus/icons-vue";
import type { GenerationParams, AlgorithmConfig } from "./types";

// Props 定义
const props = defineProps({
  modelValue: {
    type: Object as PropType<GenerationParams>,
    required: true
  },
  availableAlgorithms: {
    type: Array as PropType<AlgorithmConfig[]>,
    default: () => []
  },
  validConstraintsCount: {
    type: Number,
    default: 0
  },
  columnsLength: {
    type: Number,
    default: 0
  },
  enabledFeaturesCount: {
    type: Number,
    default: 0
  }
});

// Emits 定义
const emit = defineEmits<{
  'update:modelValue': [value: GenerationParams]
  'algorithm-change': [algorithmId: string]
  'show-advanced-settings': []
}>();

// 响应式数据
const generationParams = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
});

// 当前选中的算法
const selectedAlgorithm = computed(() => {
  if (!generationParams.value.algorithm) return null;
  return props.availableAlgorithms.find(alg => alg.id === generationParams.value.algorithm);
});

// 计算预计样本数量
const calculatedSampleCount = computed(() => {
  if (!generationParams.value.algorithm) return 0;
  
  if (generationParams.value.algorithm === 'GridGeneration') {
    // 如果有高级设置启用的特征，使用enabledFeaturesCount
    if (props.enabledFeaturesCount > 0) {
      // 这里需要从父组件传递实际的计算结果
      return calculateGridSampleCount();
    }
    // 使用全局网格步数计算
    const gridCount = generationParams.value.algorithmParams.grid_count || 10;
    return Math.pow(gridCount, props.columnsLength);
  } else if (generationParams.value.algorithm === 'RandomGeneration') {
    return generationParams.value.algorithmParams.sample_count || 0;
  }
  
  return 0;
});

// 计算网格样本数量（简化版，实际计算在父组件进行）
const calculateGridSampleCount = () => {
  if (props.enabledFeaturesCount > 0) {
    // 简化计算，实际应该从父组件获取精确值
    const avgGridCount = generationParams.value.algorithmParams.grid_count || 10;
    return Math.pow(avgGridCount, props.enabledFeaturesCount);
  }
  return 0;
};

// 处理算法变化
const handleAlgorithmChange = (algorithmId: string) => {
  emit('algorithm-change', algorithmId);
};

// 处理参数变化
const handleParamChange = () => {
  // 通知父组件参数已变化
  emit('update:modelValue', generationParams.value);
};

// 处理高级设置
const handleAdvancedSettings = () => {
  emit('show-advanced-settings');
};

// 参数状态检测逻辑
const isParamWarning = (key: string, param: any) => {
  return calculateSamplesNum(key, param) > 10000;
};

const isParamDisabled = (key: string) => {
  // 网格参数在有高级设置时被禁用
  if (key === 'grid_count' && generationParams.value.algorithm === 'GridGeneration') {
    return props.enabledFeaturesCount > 0;
  }
  return false;
};

const getDisabledTipMessage = (key: string) => {
  if (key === 'grid_count') {
    return '已启用高级设置，请在高级设置中单独配置各特征的网格参数';
  }
  return '';
};

const calculateSamplesNum = (key: string, param: any) => {
  if (param.type !== 'number') return 0;
  
  const currentValue = generationParams.value.algorithmParams[key];
  if (!currentValue) return 0;
  
  // 根据参数类型和值判断是否需要警告
  switch (key) {
    case 'grid_count':
      // 网格生成法的步长 - 考虑高级设置
      if (generationParams.value.algorithm === 'GridGeneration' && props.enabledFeaturesCount > 0) {
        return calculatedSampleCount.value;
      }
      return currentValue ** props.columnsLength;
    case 'sample_count':
      // 随机生成法的样本数量
      return currentValue;
    default:
      return 0;
  }
};

// 将样本数量转换为科学计数法格式
const formatSampleCount = (count: number) => {
  if (count >= 1000) {
    return count.toExponential(1);
  }
  return count.toString();
};

const getWarningMessage = (key: string, param: any) => {
  if (param.type !== 'number') return '';
  
  const currentValue = generationParams.value.algorithmParams[key];
  if (!currentValue) return '';

  const sample_count = calculateSamplesNum(key, param);
  
  // 根据参数类型生成相应的警告信息
  switch (key) {
    case 'grid_count':
      return `网格步长 ${currentValue} 过大，无约束条件下预计生成 ${formatSampleCount(sample_count)} 个样本。`;
    case 'sample_count':
      return `样本数量 ${currentValue} 过大，无约束条件下预计生成 ${formatSampleCount(sample_count)} 个样本。`;
    default:
      return '';
  }
};
</script>

<style scoped>
/* 右侧生成设置区 */
.generation-settings-panel {
  width: 400px;
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  margin: 10px;
}

/* 面板头部 */
.panel-header {
  padding: 15px 20px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 1px solid var(--el-border-color-lighter);
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
}

.panel-header h4 {
  margin: 0;
  color: var(--el-text-color-primary);
  font-size: 16px;
  font-weight: 600;
}

.settings-icon {
  color: var(--el-color-primary);
  font-size: 18px;
}

/* 可滚动内容区域 */
.scrollable-content {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 20px;
}

/* 自定义滚动条样式 */
.scrollable-content::-webkit-scrollbar {
  width: 6px;
}

.scrollable-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.scrollable-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.scrollable-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.setting-group {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 15px;
  border: 1px solid var(--el-border-color-lighter);
  margin-bottom: 15px;
}

.setting-group:last-child {
  margin-bottom: 0;
}

.setting-group h5 {
  margin: 0 0 12px 0;
  color: var(--el-text-color-primary);
  font-size: 14px;
  font-weight: 600;
}

.setting-group-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.setting-group-header h5 {
  margin: 0;
}

.full-width-select,
.full-width-input {
  width: 100%;
  margin-bottom: 10px;
}

/* 算法描述样式 */
.algorithm-description {
  background: #e8f4fd;
  border-radius: 6px;
  padding: 10px;
  margin-top: 8px;
  font-size: 13px;
  color: var(--el-text-color-regular);
  border-left: 3px solid var(--el-color-primary);
}

.param-item {
  margin-bottom: 15px;
}

.param-item:last-child {
  margin-bottom: 0;
}

.param-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.param-description {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  line-height: 1.4;
}

.param-warning {
  display: flex;
  align-items: center;
  gap: 5px;
  background-color: #fffbe6;
  border: 1px solid #ffe58f;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 12px;
  color: #faad14;
  font-weight: 500;
}

.warning-icon {
  color: #faad14;
  font-size: 14px;
}

.warning-text {
  font-weight: 500;
}

.warning-input {
  border-color: #ffe58f;
  box-shadow: 0 0 0 2px rgba(250, 189, 47, 0.2);
}

.param-disabled-tip {
  display: flex;
  align-items: center;
  gap: 5px;
  background-color: #f0f9ff;
  border: 1px solid #bfdbfe;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 12px;
  color: #1e40af;
  font-weight: 500;
  margin-top: 4px;
}

.tip-icon {
  color: #3b82f6;
  font-size: 14px;
}

.tip-text {
  font-weight: 500;
}

.preview-info {
  background: #e8f4fd;
  border-radius: 6px;
  padding: 12px;
  border-left: 4px solid var(--el-color-primary);
}

.preview-info p {
  margin: 5px 0;
  font-size: 13px;
  color: var(--el-text-color-regular);
}

.preview-info strong {
  color: var(--el-color-primary);
}

.warning-count {
  color: #faad14 !important;
}
</style>
