<template>
  <div class="constraints-panel">
    <div class="panel-header">
      <div class="header-content">
        <h4>约束条件</h4>
        <span class="header-subtitle">（可选，不设置约束将生成全部样本）</span>
      </div>
      <el-button
        type="primary"
        :icon="Plus"
        circle
        @click="addConstraint"
      />
    </div>
    
    <div class="scrollable-content">
      <div
        v-for="(constraint, index) in constraints"
        :key="constraint.id"
        class="constraint-card"
        :class="{ active: activeConstraintIndex === index }"
        @click="setActiveConstraint(index)"
      >
        <div class="constraint-header">
          <span class="constraint-title">约束条件 {{ index + 1 }}</span>
          <el-button
            type="danger"
            :icon="Delete"
            circle
            plain
            size="small"
            @click.stop="removeConstraint(index)"
            class="remove-constraint-btn"
          />
        </div>
        <div class="editor-container">
          <el-input
            :ref="el => (constraintInputs[index] = el)"
            v-model="constraint.latex"
            type="textarea"
            :autosize="{ minRows: 2, maxRows: 4 }"
            placeholder="点击左侧项目或手动输入 LaTeX 表达式"
            @focus="setActiveConstraint(index)"
            @input="handleConstraintInput(index, $event)"
          />
        </div>
        <div class="preview-header">实时预览</div>
        <div class="math-preview">
          <!-- 使用 VueMathjax 组件 -->
          <MathJaxRenderer 
            :formula="constraint.latex" 
            :display-mode="true"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, nextTick, computed } from "vue";
import { Plus, Delete } from "@element-plus/icons-vue";
import type { Constraint, ClickableItem } from "./types";
import { MathJax as MathJaxRenderer } from "@/components/mathJax";

// Props 定义
const props = defineProps({
  modelValue: {
    type: Array as () => Constraint[],
    default: () => []
  }
});

// Emits 定义
const emit = defineEmits<{
  'update:modelValue': [value: Constraint[]]
  'active-constraint-change': [index: number | null]
}>();

// 响应式数据
const constraints = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
});

const constraintInputs = ref<any[]>([]);
const activeConstraintIndex = ref<number | null>(null);
let nextConstraintId = ref(1);

// 添加约束条件
const addConstraint = () => {
  const newConstraints = [...constraints.value];
  newConstraints.push({
    id: nextConstraintId.value++,
    latex: "",
  });
  constraints.value = newConstraints;
  
  nextTick(() => {
    const newIndex = constraints.value.length - 1;
    setActiveConstraint(newIndex);
    constraintInputs.value[newIndex]?.focus();
  });
};

// 删除约束条件
const removeConstraint = (index: number) => {
  const newConstraints = [...constraints.value];
  newConstraints.splice(index, 1);
  constraints.value = newConstraints;
  
  if (activeConstraintIndex.value === index) {
    activeConstraintIndex.value = null;
    emit('active-constraint-change', null);
  } else if (activeConstraintIndex.value !== null && activeConstraintIndex.value > index) {
    activeConstraintIndex.value--;
    emit('active-constraint-change', activeConstraintIndex.value);
  }
};

// 设置当前活动的约束条件
const setActiveConstraint = (index: number) => {
  activeConstraintIndex.value = index;
  emit('active-constraint-change', index);
};

// 处理约束条件输入
const handleConstraintInput = (index: number, value: string) => {
  const newConstraints = [...constraints.value];
  newConstraints[index].latex = value;
  constraints.value = newConstraints;
};

// 向当前活动约束插入内容
const insertToActiveConstraint = (item: ClickableItem) => {
  if (activeConstraintIndex.value === null) {
    return false; // 没有活动约束，返回false表示插入失败
  }

  const activeConstraint = constraints.value[activeConstraintIndex.value];
  const inputComponent = constraintInputs.value[activeConstraintIndex.value];
  if (!inputComponent || !activeConstraint) return false;

  const textarea = inputComponent.textarea as HTMLTextAreaElement;
  const start = textarea.selectionStart;
  const end = textarea.selectionEnd;
  const currentLatex = activeConstraint.latex;

  let textToInsert = "";
  let cursorOffset = 0;
  
  switch (item.type) {
    case "feature": 
      textToInsert = `\\text{${filterSpecialChars(item.value.title)}}`;
      break;
    case "operator": 
      textToInsert = item.value; 
      if(item.value.includes("{}")){
        cursorOffset = -3;
        if(item.value === "^{}"){
          cursorOffset = -1;
        }
      }
      break;
  }
  
  textToInsert = ` ${textToInsert} `;

  const newLatex = currentLatex.substring(0, start) + textToInsert + currentLatex.substring(end);
  
  // 更新约束条件
  const newConstraints = [...constraints.value];
  newConstraints[activeConstraintIndex.value].latex = newLatex;
  constraints.value = newConstraints;

  nextTick(() => {
    textarea.focus();
    const newCursorPos = start + textToInsert.length + cursorOffset;
    textarea.selectionStart = textarea.selectionEnd = newCursorPos;
  });
  
  return true; // 插入成功
};

// 过滤特殊字符
const filterSpecialChars = (text: string) => {
  if (!text) return '';

  const replacements: Record<string, string> = {
    '\\': '\\\\',
    '{': '\\{',
    '}': '\\}',
    '%': '\\%',
    '$': '\\$',
    '#': '\\#',
    '_': '\\_',
    '&': '\\&',
    '^': '\\^{}',
    '~': '\\~{}'
  };

  return text.replace(/([\\{}%$#_^&~])/g, (match) => replacements[match] || match);
};

// 暴露给父组件的方法
defineExpose({
  insertToActiveConstraint,
  setActiveConstraint,
  addConstraint
});
</script>

<style scoped>
/* 中间约束构建区 */
.constraints-panel {
  flex: 1;
  min-width: 0; /* 防止flex项目溢出 */
  display: flex;
  flex-direction: column;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  margin: 10px;
}

/* 面板头部 */
.panel-header {
  padding: 15px 20px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 1px solid var(--el-border-color-lighter);
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
}

.panel-header h4 {
  margin: 0;
  color: var(--el-text-color-primary);
  font-size: 16px;
  font-weight: 600;
}

.header-content {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.header-subtitle {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  font-weight: 400;
  font-style: italic;
}

/* 可滚动内容区域 */
.scrollable-content {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 20px;
}

/* 自定义滚动条样式 */
.scrollable-content::-webkit-scrollbar {
  width: 6px;
}

.scrollable-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.scrollable-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.scrollable-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 约束条件卡片样式 */
.constraint-card {
  border: 2px solid var(--el-border-color-lighter);
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  position: relative;
  transition: all 0.3s ease;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}

.constraint-card:hover {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
}

.constraint-card.active {
  border-color: var(--el-color-primary);
  box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.1), 0 8px 25px rgba(0, 0, 0, 0.12);
}

.constraint-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.constraint-title {
  font-weight: 600;
  color: var(--el-text-color-primary);
  font-size: 14px;
}

.remove-constraint-btn {
  opacity: 0.7;
  transition: opacity 0.3s ease;
}

.remove-constraint-btn:hover {
  opacity: 1;
}

.editor-container {
  margin-bottom: 15px;
}

.editor-container :deep(.el-textarea) {
  border-radius: 8px;
}

.editor-container :deep(.el-textarea__inner) {
  border-radius: 8px;
  transition: all 0.3s ease;
}

.editor-container :deep(.el-textarea__inner:focus) {
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

.preview-header {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  margin-bottom: 8px;
  font-weight: 500;
}

.math-preview {
  min-height: 40px;
  padding: 12px;
  border-radius: 8px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  font-size: 18px;
  overflow-x: auto;
  border: 1px solid var(--el-border-color-lighter);
}

.math-preview :deep(.MathJax .merror) {
  color: #c00;
  background-color: #fdd;
  border: 1px solid #c00;
  padding: 2px;
}
</style>
