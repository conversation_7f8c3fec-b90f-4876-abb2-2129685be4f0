<template>
  <div class="prediction-table">
    <div class="model-controls">
      <div class="selection-controls">
        <button
          v-for="key in availableDatasets"
          :key="key"
          type="button"
          :class="['selection-button', { 'is-active': selectedDataset === key }]"
          @click="selectedDataset = key"
        >
          {{ datasetConfig.titles[key] }}
        </button>
      </div>
      <el-button type="primary" @click="exportPredictData">
        <el-icon><Download /></el-icon>
        导出数据
      </el-button>
    </div>

    <!-- 统计信息 -->
    <div class="model-statistics" v-if="currentPredictData.length > 0">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-statistic title="样本数量" :value="currentPredictData.length" />
        </el-col>
        <el-col :span="6">
          <el-statistic title="平均误差" :value="averageError" :precision="4" />
        </el-col>
        <el-col :span="6">
          <el-statistic title="最大误差" :value="maxError" :precision="4" />
        </el-col>
        <el-col :span="6">
          <el-statistic title="最小误差" :value="minError" :precision="4" />
        </el-col>
      </el-row>
    </div>
    
    <ReTable :data="currentPredictData">
      <template #default="{ data: paginatedData }">
        <el-table
          :data="paginatedData"
          style="width: 100%"
          height="400"
          class="model-table"
        >
          <el-table-column label="序号" type="index" width="80" />
          <el-table-column label="真实值" prop="true" sortable />
          <el-table-column label="预测值" prop="predict" sortable />
          <el-table-column label="绝对误差" prop="error" sortable>
            <template #default="{ row }">
              <span :class="getErrorClass(row.error)">
                {{ row.error }}
              </span>
            </template>
          </el-table-column>
          <el-table-column label="相对误差%" prop="errorRate" sortable v-if="showErrorRate">
            <template #default="{ row }">
              <span :class="getErrorClass(row.errorRate)">
                {{ row.errorRate }}%
              </span>
            </template>
          </el-table-column>
        </el-table>
      </template>
    </ReTable>

  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { Download } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import ReTable from "@/components/ReTable/index.vue";
import { exportSingleSheet } from "@/utils/exportUtils";

interface Props {
  modelResult: any;
  showErrorRate?: boolean;
  datasetConfig?: {
    order: readonly string[];
    titles: Record<string, string>;
  };
}

const props = withDefaults(defineProps<Props>(), {
  showErrorRate: true,
  datasetConfig: () => ({
    order: ["train", "test", "cv", "loocv"] as const,
    titles: {
      train: "训练集",
      test: "测试集",
      cv: "交叉验证", 
      cv: "留一法交叉验证", 
    }
  })
});

// 可用数据集（根据 eval 对象中存在的键和数据来确定）
const availableDatasets = computed(() => {
  const result = props.modelResult;
  if (!result || !result.eval) return [];
  return props.datasetConfig.order.filter(key => {
    const dataset = result.eval[key];
    return dataset && Array.isArray(dataset.yTrue) && Array.isArray(dataset.yPredict);
  });
});

const selectedDataset = ref(
  availableDatasets.value.length > 0 ? availableDatasets.value[0] : "train"
);


// 当前预测数据
const currentPredictData = computed(() => {
  const ds = props.modelResult?.eval?.[selectedDataset.value];
  if (!ds || !ds.yTrue || !ds.yPredict) return [];
  
  return ds.yTrue.map((val: number, i: number) => {
    const trueVal = Number(val);
    const predictVal = Number(ds.yPredict[i]);
    const error = Math.abs(trueVal - predictVal);
    const errorRate = trueVal !== 0 ? (error / Math.abs(trueVal)) * 100 : 0;
    
    return {
      true: Number(trueVal.toFixed(2)),
      predict: Number(predictVal.toFixed(2)),
      error: Number(error.toFixed(4)),
      errorRate: Number(errorRate.toFixed(2))
    };
  });
});

// 统计信息
const averageError = computed(() => {
  if (currentPredictData.value.length === 0) return 0;
  const sum = currentPredictData.value.reduce((acc, item) => acc + item.error, 0);
  return sum / currentPredictData.value.length;
});

const maxError = computed(() => {
  if (currentPredictData.value.length === 0) return 0;
  return Math.max(...currentPredictData.value.map(item => item.error));
});

const minError = computed(() => {
  if (currentPredictData.value.length === 0) return 0;
  return Math.min(...currentPredictData.value.map(item => item.error));
});

// 根据误差大小设置样式类
const getErrorClass = (error: number) => {
  if (error < 0.1) return 'error-low';
  if (error < 0.5) return 'error-medium';
  return 'error-high';
};

// 导出预测数据
const exportPredictData = async () => {
  if (currentPredictData.value.length === 0) {
    ElMessage.warning('没有可导出的数据');
    return;
  }

  const headers = ['序号', '真实值', '预测值', '误差'];
  if (props.showErrorRate) {
    headers.push('误差率(%)');
  }

  const content = currentPredictData.value.map((item, index) => {
    const row = [index + 1, item.true, item.predict, item.error];
    if (props.showErrorRate) {
      row.push(item.errorRate);
    }
    return row;
  });

  await exportSingleSheet(
    { headers, content },
    {
      suggestedName: `预测数据_${props.datasetConfig.titles[selectedDataset.value]}`,
      sheetName: "预测数据",
      exportType: "auto"
    }
  );
};

// 监听可用数据集变化，自动选择第一个
watch(availableDatasets, (newDatasets) => {
  if (newDatasets.length > 0 && !newDatasets.includes(selectedDataset.value)) {
    selectedDataset.value = newDatasets[0];
  }
}, { immediate: true });
</script>

<style lang="scss" scoped>
.prediction-table {
  .model-controls {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;
  }
}

.selection-controls {
  display: flex;
  gap: 12px;
}

.selection-button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 16px;
  height: 36px;
  border-radius: 24px;
  border: 1px solid transparent;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
  background: rgba(206, 206, 206, 0.1);
  color: #666;
}

.selection-button.is-active {
  background: rgba(0, 93, 255, 0.1);
  border: 1px solid #005dff;
  color: #005dff;
  font-weight: 500;
}

/* 误差颜色样式 */
:deep(.error-low) {
  color: #67c23a;
  font-weight: 500;
}

:deep(.error-medium) {
  color: #e6a23c;
  font-weight: 500;
}

:deep(.error-high) {
  color: #f56c6c;
  font-weight: 500;
}
</style>
