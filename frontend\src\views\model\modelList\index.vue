<template>
  <div class="main">
    <div class="card-header">
      <span>模型列表</span>
    </div>

    <!-- 工具栏 -->
    <div class="toolbar content-card">
      <div class="left">
        <el-button type="primary" @click="fetchModelList" :loading="loading" :icon="Refresh">
          刷新
        </el-button>
        <el-button type="success" @click="handleImportModel" :icon="Upload">
          导入模型
        </el-button>
        <input
          ref="fileInputRef"
          type="file"
          accept=".joblib"
          style="display: none"
          @change="onFileSelected"
        />
        <el-switch v-model="autoRefresh" active-text="自动刷新" inactive-text="手动刷新" @change="handleAutoRefreshChange" />
        <el-select v-if="autoRefresh" v-model="refreshInterval" style="width: 120px" size="small">
          <el-option label="5秒" :value="5000" />
          <el-option label="10秒" :value="10000" />
          <el-option label="30秒" :value="30000" />
          <el-option label="1分钟" :value="60000" />
        </el-select>
        <el-button v-if="sortBy && sortOrder" type="info" size="small" @click="clearSort" plain>
          <el-icon>
            <Close />
          </el-icon>
          清除排序
        </el-button>
        <!-- 批量删除按钮 -->
        <el-button v-if="selectedModels.length > 0" type="danger" @click="handleBatchDelete" :icon="Delete">
          批量删除 ({{ selectedModels.length }})
        </el-button>
        <!-- 批量导出按钮 -->
        <el-button v-if="selectedModels.length > 0" type="success" @click="handleBatchExport" :icon="Download">
          批量导出 ({{ selectedModels.length }})
        </el-button>
      </div>
      <div class="center">
        <div class="stats">
          <span class="stat-item">
            <span class="label">总数:</span>
            <span class="value">{{ modelList.length }}</span>
          </span>
          <span class="stat-item">
            <span class="label">已完成:</span>
            <span class="value success">{{ getStatusCount('completed') }}</span>
          </span>
          <span class="stat-item">
            <span class="label">运行中:</span>
            <span class="value warning">{{ getStatusCount('running') }}</span>
          </span>
          <span class="stat-item">
            <span class="label">等待中:</span>
            <span class="value info">{{ getStatusCount('pending') }}</span>
          </span>
          <span class="stat-item">
            <span class="label">失败:</span>
            <span class="value danger">{{ getStatusCount('failed') }}</span>
          </span>
        </div>
      </div>
      <div class="right">
        <el-input v-model="searchKeyword" placeholder="搜索模型名称或目标变量" style="width: 300px" clearable
          @input="handleSearch">
          <template #prefix>
            <el-icon>
              <Search />
            </el-icon>
          </template>
        </el-input>
      </div>
    </div>

    <div class="table-container content-card">
      <el-scrollbar>
        <ReTable :data="filteredModelList">
          <template #default="{ data }">
            <el-table ref="tableRef" :data="data" v-loading="loading" style="width: 100%" empty-text="暂无模型数据"
              @sort-change="handleSortChange" @selection-change="handleSelectionChange" @row-click="handleRowClick"
              :row-key="(row) => row.uid" :default-sort="{ prop: 'createdAt', order: 'descending' }">
              <!-- 多选列 -->
              <el-table-column type="selection" width="55" :reserve-selection="false" />
              <el-table-column prop="id" label="ID" width="80" sortable="custom" />
              <el-table-column prop="name" label="模型名称" min-width="150" sortable="custom" />
              <el-table-column prop="targetName" label="目标变量" min-width="120" sortable="custom" />
              <el-table-column prop="modelType" label="类型" width="100" sortable="custom">
                <template #default="{ row }">
                  <el-tag :type="getModelTypeTagType(row.modelType)" size="small">
                    {{ getModelTypeLabel(row.modelType) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="status" label="状态" width="120" sortable="custom">
                <template #default="{ row }">
                  <el-tag :type="getStatusTagType(row.status)" size="small">
                    {{ getStatusLabel(row.status) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="progress" label="进度" width="120" sortable="custom">
                <template #default="{ row }">
                  <el-progress :percentage="getProgressPercentage(row.progress)" :status="getProgressStatus(row.status)"
                    :stroke-width="8" />
                </template>
              </el-table-column>
              <el-table-column prop="createdAt" label="创建时间" width="180" sortable="custom">
                <template #default="{ row }">
                  {{ formatDate(row.createdAt) }}
                </template>
              </el-table-column>
              <el-table-column label="操作" width="240" fixed="right">
                <template #default="{ row }">
                  <el-button type="success" size="small" :disabled="row.status !== 'completed'" @click="handleSingleExport(row)"
                    :loading="viewLoadingUids.includes(row.uid)" :icon="Download">
                    下载
                  </el-button>
                  <el-button type="primary" size="small" @click="viewModelDetails(row)">
                    详情
                  </el-button>
                  <el-button type="danger" size="small" @click="handleDeleteModels([row.uid])">
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </template>
        </ReTable>
      </el-scrollbar>
    </div>

    <!-- 模型详情对话框 -->
    <el-dialog v-model="detailDialogVisible" title="模型详情" width="80%" :before-close="handleCloseDetailDialog"
      class="model-detail-dialog" top="8vh">
      <div v-if="selectedModel" class="model-detail">
        <!-- 模型基本信息卡片 -->
        <div class="model-info-card">
          <div class="model-header">
            <div class="model-icon">
              <el-icon size="24" :color="getModelTypeColor(selectedModel.modelType)">
                <Histogram v-if="selectedModel.modelType === 'reg'" />
                <Grid v-else-if="selectedModel.modelType === 'cls'" />
                <Collection v-else />
              </el-icon>
            </div>
            <div class="model-title">
              <h3>{{ selectedModel.name }}</h3>
              <p class="model-subtitle">{{ selectedModel.targetName }}</p>
            </div>
            <div class="model-status">
              <el-tag :type="getStatusTagType(selectedModel.status)" size="large" effect="dark">
                {{ getStatusLabel(selectedModel.status) }}
              </el-tag>
            </div>
          </div>

          <!-- 进度条 -->
          <div class="progress-section">
            <div class="progress-info">
              <span class="progress-label">训练进度</span>
              <span class="progress-value">{{ getProgressPercentage(selectedModel.progress) }}%</span>
            </div>
            <el-progress :percentage="getProgressPercentage(selectedModel.progress)"
              :status="getProgressStatus(selectedModel.status)" :stroke-width="12" :show-text="false"
              class="custom-progress" />
          </div>
        </div>

        <!-- 详细信息网格 -->
        <div class="details-grid">
          <div class="detail-item">
            <div class="detail-label">
              <el-icon>
                <Document />
              </el-icon>
              <span>模型ID</span>
            </div>
            <div class="detail-value">{{ selectedModel.id }}</div>
          </div>

          <div class="detail-item">
            <div class="detail-label">
              <el-icon>
                <Key />
              </el-icon>
              <span>唯一标识</span>
            </div>
            <div class="detail-value code-text">{{ selectedModel.uid }}</div>
          </div>

          <div class="detail-item">
            <div class="detail-label">
              <el-icon>
                <Collection />
              </el-icon>
              <span>模型类型</span>
            </div>
            <div class="detail-value">
              <el-tag :type="getModelTypeTagType(selectedModel.modelType)" size="small" effect="light">
                {{ getModelTypeLabel(selectedModel.modelType) }}
              </el-tag>
            </div>
          </div>

          <div class="detail-item">
            <div class="detail-label">
              <el-icon>
                <Timer />
              </el-icon>
              <span>异步执行</span>
            </div>
            <div class="detail-value">
              <el-tag :type="selectedModel.asynchronous ? 'success' : 'info'" size="small" effect="light">
                {{ selectedModel.asynchronous ? '是' : '否' }}
              </el-tag>
            </div>
          </div>

          <div class="detail-item">
            <div class="detail-label">
              <el-icon>
                <Connection />
              </el-icon>
              <span>RabbitMQ就绪</span>
            </div>
            <div class="detail-value">
              <el-tag :type="selectedModel.isRabbitmqReady ? 'success' : 'warning'" size="small" effect="light">
                {{ selectedModel.isRabbitmqReady ? '是' : '否' }}
              </el-tag>
            </div>
          </div>

          <div class="detail-item">
            <div class="detail-label">
              <el-icon>
                <DataAnalysis />
              </el-icon>
              <span>目标值数量</span>
            </div>
            <div class="detail-value">
              <el-tag type="primary" size="small" effect="light">
                {{ selectedModel.targetValues?.length || 0 }} 个
              </el-tag>
            </div>
          </div>

          <div class="detail-item">
            <div class="detail-label">
              <el-icon>
                <Calendar />
              </el-icon>
              <span>创建时间</span>
            </div>
            <div class="detail-value">{{ formatDate(selectedModel.createdAt) }}</div>
          </div>

          <div class="detail-item">
            <div class="detail-label">
              <el-icon>
                <RefreshRight />
              </el-icon>
              <span>更新时间</span>
            </div>
            <div class="detail-value">{{ formatDate(selectedModel.updatedAt) }}</div>
          </div>
        </div>

        <!-- 错误信息 -->
        <div v-if="selectedModel.error" class="error-section">
          <div class="error-header">
            <el-icon color="#f56c6c">
              <Warning />
            </el-icon>
            <span>错误信息</span>
          </div>
          <div class="error-content">
            <el-tag type="danger" effect="dark" size="large">{{ selectedModel.error }}</el-tag>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="detailDialogVisible = false">关闭</el-button>
          <el-button v-if="selectedModel?.status === 'completed'" type="primary"
            @click="handleDetailViewModel" :loading="detailViewLoading" :icon="View">
            查看结果
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { getModelList } from "@/api/models/model";
import { deleteModels, getModelInfo, downloadModelFiles, uploadModel } from "@/api/models/model";
import { onMounted, onUnmounted, ref, computed } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { Refresh, Search, Close, Document, Key, Collection, Timer, Connection, DataAnalysis, Calendar, RefreshRight, Warning, View, Histogram, Grid, Delete, Download, Upload } from "@element-plus/icons-vue";
import type { Model } from "@/types/models";
import ReTable from "@/components/ReTable/index.vue";
import { useRouter } from "vue-router";
import { exportModelFile } from "@/utils/exportUtils";
import { useWorkspaceStoreHook } from "@/store/modules/workspace";

defineOptions({
  name: "ModelList"
});

const router = useRouter();
const viewLoadingUids = ref<string[]>([]);
const workspaceStore = useWorkspaceStoreHook();

// 响应式数据
const modelList = ref<Model[]>([]);
const loading = ref(false);
const searchKeyword = ref('');
const autoRefresh = ref(false);
const refreshInterval = ref(10000);
const sortBy = ref<string>('');
const sortOrder = ref<'ascending' | 'descending' | null>(null);
const tableRef = ref();
const fileInputRef = ref<HTMLInputElement>();
let refreshTimer: NodeJS.Timeout | null = null;

// 多选相关数据
const selectedModels = ref<Model[]>([]);

// 详情对话框中的查看结果按钮loading状态
const detailViewLoading = ref(false);

// 过滤后的模型列表
const filteredModelList = computed(() => {
  let result = modelList.value;

  // 搜索过滤
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase();
    result = result.filter(model =>
      model.name.toLowerCase().includes(keyword) ||
      model.targetName.toLowerCase().includes(keyword)
    );
  }

  // 排序
  if (sortBy.value && sortOrder.value) {
    result = [...result].sort((a, b) => {
      let aValue: any = a[sortBy.value as keyof Model];
      let bValue: any = b[sortBy.value as keyof Model];

      // 特殊字段处理
      if (sortBy.value === 'progress') {
        aValue = getProgressPercentage(aValue);
        bValue = getProgressPercentage(bValue);
      } else if (sortBy.value === 'createdAt' || sortBy.value === 'updatedAt') {
        aValue = new Date(aValue).getTime();
        bValue = new Date(bValue).getTime();
      } else if (sortBy.value === 'targetValues') {
        aValue = aValue?.length || 0;
        bValue = bValue?.length || 0;
      }

      // 处理null/undefined值
      if (aValue == null && bValue == null) return 0;
      if (aValue == null) return sortOrder.value === 'ascending' ? -1 : 1;
      if (bValue == null) return sortOrder.value === 'ascending' ? 1 : -1;

      // 字符串比较
      if (typeof aValue === 'string' && typeof bValue === 'string') {
        aValue = aValue.toLowerCase();
        bValue = bValue.toLowerCase();
      }

      // 数值比较
      if (aValue < bValue) {
        return sortOrder.value === 'ascending' ? -1 : 1;
      } else if (aValue > bValue) {
        return sortOrder.value === 'ascending' ? 1 : -1;
      } else {
        return 0;
      }
    });
  }

  return result;
});

// 获取模型列表
const fetchModelList = async () => {
  try {
    loading.value = true;
    const workspacePath = workspaceStore.getCurrentWorkspacePath || "";
    const workspaceName = workspacePath
      ? workspacePath
          .replace(/[\\/]+$/, "")
          .split(/[\\/]/)
          .pop() || ""
      : "";
    const params = { path: workspaceName };
    const res = await getModelList(params);
    if (res.code === 200) {
      modelList.value = res.data;
    } else {
      ElMessage.error(res.msg || '获取模型列表失败');
    }
  } catch (error) {
    console.error('获取模型列表失败:', error);
    ElMessage.error('获取模型列表失败');
  } finally {
    loading.value = false;
  }
};

// 处理自动刷新开关变化
const handleAutoRefreshChange = (value: boolean) => {
  if (value) {
    startAutoRefresh();
  } else {
    stopAutoRefresh();
  }
};

// 开始自动刷新
const startAutoRefresh = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer);
  }
  refreshTimer = setInterval(() => {
    fetchModelList();
  }, refreshInterval.value);
};

// 停止自动刷新
const stopAutoRefresh = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer);
    refreshTimer = null;
  }
};

// 处理搜索
const handleSearch = () => {
  // 搜索逻辑已通过计算属性实现
};

// 处理表格排序
const handleSortChange = ({ column, prop, order }: { column: any; prop: string; order: string }) => {
  if (prop && order) {
    sortBy.value = prop;
    sortOrder.value = order as 'ascending' | 'descending';
  } else {
    sortBy.value = '';
    sortOrder.value = null;
  }
};

// 获取排序字段的显示标签
const getSortFieldLabel = (field: string) => {
  const fieldLabels: Record<string, string> = {
    id: 'ID',
    name: '模型名称',
    targetName: '目标变量',
    modelType: '模型类型',
    status: '状态',
    progress: '进度',
    createdAt: '创建时间',
    updatedAt: '更新时间'
  };
  return fieldLabels[field] || field;
};

// 清除排序
const clearSort = () => {
  sortBy.value = '';
  sortOrder.value = null;

  // 清除表格的排序状态
  if (tableRef.value) {
    // 使用 Element Plus 的 clearSort 方法
    tableRef.value.clearSort();
  }
};

// 获取指定状态的模型数量
const getStatusCount = (status: string) => {
  return modelList.value.filter(model => model.status === status).length;
};

// 获取状态标签类型
const getStatusTagType = (status: string) => {
  switch (status) {
    case 'completed':
      return 'success';
    case 'running':
      return 'warning';
    case 'pending':
      return 'info';
    case 'upload':
      return 'info';
    case 'failed':
      return 'danger';
    default:
      return 'info';
  }
};

// 获取状态标签文本
const getStatusLabel = (status: string) => {
  switch (status) {
    case 'completed':
      return '已完成';
    case 'running':
      return '运行中';
    case 'pending':
      return '等待中';
    case 'upload':
      return '导入中';
    case 'failed':
      return '失败';
    default:
      return status;
  }
};

// 获取模型类型标签类型
const getModelTypeTagType = (modelType: string) => {
  switch (modelType) {
    case 'reg':
      return 'primary';
    case 'cls':
      return 'success';
    default:
      return 'info';
  }
};

// 获取模型类型标签文本
const getModelTypeLabel = (modelType: string) => {
  switch (modelType) {
    case 'reg':
      return '回归';
    case 'cls':
      return '分类';
    default:
      return modelType;
  }
};

// 获取进度百分比
const getProgressPercentage = (progress: string) => {
  if (progress === 'completed') return 100;
  if (progress === 'running') return 50;
  if (progress === 'pending') return 0;

  const num = parseInt(progress);
  if (!isNaN(num)) return num;
  return 0;
};

// 获取进度状态
const getProgressStatus = (status: string) => {
  if (status === 'completed') return 'success';
  if (status === 'failed') return 'exception';
  return undefined;
};

// 获取模型类型颜色
const getModelTypeColor = (modelType: string) => {
  switch (modelType) {
    case 'reg':
      return '#409eff';
    case 'cls':
      return '#67c23a';
    default:
      return '#909399';
  }
};

// 格式化日期
const formatDate = (dateString: string) => {
  if (!dateString) return '-';
  try {
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  } catch {
    return dateString;
  }
};

// 查看模型结果
const viewModel = async (model: Model) => {
  if (model.status !== 'completed') {
    ElMessage.warning('模型尚未完成，无法查看结果');
    return;
  }

  viewLoadingUids.value.push(model.uid);
  try {
    if (window.ipcRenderer) {
      // 1. 预取数据
      const res = await getModelInfo(model.uid);
      if (res.code === 200) {
        const correctModelType = res.data.alg; // 从API响应获取正确的模型类型
        // 2. 将结果发送到主进程进行暂存，并等待其完成
        await window.ipcRenderer.send("cache_model_result", {
          uid: model.uid,
          result: res.data
        });

        // 3. 使用 router.resolve 生成正确的 URL
        const linearModels = ["LinearRegression", "Ridge", "Lasso", "ElasticNet"];
        const modelCategory = linearModels.includes(correctModelType) ? 'linear' : 'ml';
        const routeName = modelCategory === 'linear' ? 'LMModelResult' : 'MLModelResult';

        const { href } = router.resolve({
          name: routeName,
          query: {
            uid: model.uid,
            modelType: correctModelType, // 使用正确的模型类型
            modelName: model.name,
            targetName: model.targetName,
          },
        });
        window.open(href);

      } else {
        ElMessage.error(res.msg || '获取模型详情失败');
      }
    } else {
      ElMessage.error('非Electron环境，无法打开独立窗口');
    }
  } catch (error) {
    console.error("获取模型详情失败:", error);
    ElMessage.error('获取模型详情失败');
  } finally {
    // 确保无论成功与否都移除加载状态
    viewLoadingUids.value = viewLoadingUids.value.filter(uid => uid !== model.uid);
  }
};

// 处理详情对话框中的查看结果按钮点击
const handleDetailViewModel = async () => {
  if (!selectedModel.value) return;
  
  detailViewLoading.value = true;
  try {
    await viewModel(selectedModel.value);
    // 成功打开新页面后关闭弹窗
    detailDialogVisible.value = false;
  } catch (error) {
    console.error('查看模型结果失败:', error);
  } finally {
    detailViewLoading.value = false;
  }
};

// 查看模型详情
const viewModelDetails = (model: Model) => {
  selectedModel.value = model; // 设置选中的模型
  detailDialogVisible.value = true; // 打开详情对话框
 };

const handleDeleteModels = (uids: string[]) => {
  deleteModels(uids).then((res) => {
    if (res.code === 200) {
      ElMessage.success("删除成功");
      fetchModelList();
    } else {
      ElMessage.error(res.msg || "删除失败");
    }
  });
};

// 处理表格选择变化
const handleSelectionChange = (selection: Model[]) => {
  selectedModels.value = selection;
};

// 处理行点击事件
const handleRowClick = (row: Model, column: any) => {
  // 如果点击的是操作列，不处理行选中
  if (column && column.label === '操作') return;

  if (tableRef.value) {
    tableRef.value.toggleRowSelection(row, !selectedModels.value.some(model => model.uid === row.uid));
  }
};

// 批量删除
const handleBatchDelete = () => {
  if (selectedModels.value.length === 0) {
    ElMessage.warning('请先选择要删除的模型');
    return;
  }

  const uids = selectedModels.value.map(model => model.uid);
  ElMessageBox.confirm(
    `确定要删除选中的 ${selectedModels.value.length} 个模型吗？此操作不可恢复。`,
    '确认删除',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    handleDeleteModels(uids);
    // 清空选择
    selectedModels.value = [];
    if (tableRef.value) {
      tableRef.value.clearSelection();
    }
  }).catch(() => {
    // 用户取消删除
  });
};

// 批量导出
const handleBatchExport = async () => {
  if (selectedModels.value.length === 0) {
    ElMessage.warning('请先选择要导出的模型');
    return;
  }

  const uids = selectedModels.value.map(model => model.uid);
  ElMessageBox.confirm(
    `确定要导出选中的 ${selectedModels.value.length} 个模型吗？`,
    '确认导出',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'info',
    }
  ).then(async () => {
    try {
      loading.value = true;
      const response = await downloadModelFiles(uids);
      
      // 使用 exportUtils 导出模型文件，传入多个UID
      await exportModelFile(response, `models_${new Date().getTime()}`, uids);
      
      ElMessage.success('模型导出成功！');
    } catch (error) {
      console.error('批量导出失败:', error);
      ElMessage.error('批量导出失败');
    } finally {
      loading.value = false;
    }
  }).catch(() => {
    // 用户取消导出
  });
};

// 处理导入模型
const handleImportModel = () => {
  if (fileInputRef.value) {
    fileInputRef.value.click();
  }
};

// 处理文件选择
const onFileSelected = async (event: Event) => {
  const target = event.target as HTMLInputElement;
  const file = target.files?.[0];
  
  if (!file) return;
  
  // 检查文件类型
  if (!file.name.endsWith('.joblib')) {
    ElMessage.error('请选择.joblib格式的文件');
    return;
  }
  
  try {
    loading.value = true;
    const response = await uploadModel(file);
    
    if (response.code === 200) {
      ElMessage.success('模型导入成功！');
      // 刷新模型列表
      await fetchModelList();
    } else {
      ElMessage.error(response.msg || '模型导入失败');
    }
  } catch (error) {
    console.error('模型导入失败:', error);
    ElMessage.error('模型导入失败');
  } finally {
    loading.value = false;
    // 清空文件输入框
    if (fileInputRef.value) {
      fileInputRef.value.value = '';
    }
  }
};

// 单个模型导出
const handleSingleExport = async (model: Model) => {
  if (model.status !== 'completed') {
    ElMessage.warning('只能下载已完成的模型');
    return;
  }

  try {
    // 将单个模型的uid添加到加载状态中
    viewLoadingUids.value.push(model.uid);
    
    const response = await downloadModelFiles([model.uid]);
    
    // 使用 exportUtils 导出模型文件，传入单个UID
    await exportModelFile(response, `${model.name}_${model.uid}`, [model.uid]);
    
    ElMessage.success('模型下载成功！');
  } catch (error) {
    console.error('单个模型下载失败:', error);
    ElMessage.error('模型下载失败');
  } finally {
    // 从加载状态中移除
    viewLoadingUids.value = viewLoadingUids.value.filter(uid => uid !== model.uid);
  }
};

// 关闭详情对话框
const handleCloseDetailDialog = () => {
  detailDialogVisible.value = false;
  selectedModel.value = null; // 清空选中的模型
};

const detailDialogVisible = ref(false);
const selectedModel = ref<Model | null>(null);

onMounted(() => {
  fetchModelList();
});

onUnmounted(() => {
  stopAutoRefresh();
});
</script>

<style scoped>
.main {
  padding: 25px;
  height: calc(100% - 40px);
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  gap: 20px;
  overflow: hidden;
  background-color: transparent;

  .card-header {
    font-size: 18px;
    font-weight: 600;
    color: var(--el-text-color-primary);
  }

  .toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 16px;
    background: var(--el-bg-color);

    .left {
      display: flex;
      gap: 12px;
      align-items: center;

      .el-divider--vertical {
        margin: 0 8px;
      }
    }

    .center {
      display: flex;
      align-items: center;
      gap: 20px;
      flex-direction: column;

      .sort-indicator {
        margin-top: 8px;
      }
    }

    .stats {
      display: flex;
      gap: 20px;
      font-size: 14px;
      color: var(--el-text-color-regular);

      .stat-item {
        display: flex;
        align-items: center;
        gap: 8px;

        .label {
          font-weight: 500;
          color: var(--el-text-color-secondary);
        }

        .value {
          font-weight: 600;
          color: var(--el-text-color-primary);
        }

        .value.success {
          color: var(--el-color-success);
        }

        .value.warning {
          color: var(--el-color-warning);
        }

        .value.info {
          color: var(--el-color-info);
        }

        .value.danger {
          color: var(--el-color-danger);
        }
      }
    }

    .right {
      display: flex;
      gap: 12px;
    }
  }
}

.content-card {
  background-color: var(--el-bg-color, #ffffff);
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0px 0px 12px rgba(0, 0, 0, 0.05);
}

.table-container {
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;
}

:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
}

/* 多选模式下的行样式 */
:deep(.el-table__row) {
  transition: all 0.2s ease;
}

:deep(.el-table__row:hover) {
  background-color: var(--el-fill-color-light);
}

/* 多选模式下选中行的样式 */
:deep(.el-table__row.current-row) {
  background-color: var(--el-color-primary-light-9);
}

:deep(.el-table__row.current-row:hover) {
  background-color: var(--el-color-primary-light-8);
}

:deep(.el-table th) {
  font-weight: 600;
}

:deep(.el-progress) {
  margin: 0;
}

:deep(.el-tag) {
  font-weight: 500;
}

.model-detail {
  .el-descriptions {
    margin: 0;
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 模型详情对话框样式 */
.model-detail-dialog {
  :deep(.el-dialog__header) {
    background: linear-gradient(135deg, var(--el-color-primary-light-9) 0%, var(--el-color-primary-light-8) 100%);
    border-radius: 8px 8px 0 0;
    padding: 20px 24px;

    .el-dialog__title {
      font-size: 18px;
      font-weight: 600;
      color: var(--el-color-primary-dark-2);
    }
  }

  :deep(.el-dialog__body) {
    padding: 24px;
  }

  :deep(.el-dialog__footer) {
    padding: 16px 24px;
    border-top: 1px solid var(--el-border-color-lighter);
    background: var(--el-fill-color-extra-light);
  }
}

.model-detail {
  .model-info-card {
    background: linear-gradient(135deg, var(--el-bg-color) 0%, var(--el-fill-color-extra-light) 100%);
    border: 1px solid var(--el-border-color-light);
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 24px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);

    .model-header {
      display: flex;
      align-items: center;
      gap: 16px;
      margin-bottom: 20px;

      .model-icon {
        width: 56px;
        height: 56px;
        background: var(--el-fill-color-light);
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        border: 2px solid var(--el-border-color-lighter);
      }

      .model-title {
        flex: 1;

        h3 {
          margin: 0 0 8px 0;
          font-size: 20px;
          font-weight: 600;
          color: var(--el-text-color-primary);
        }

        .model-subtitle {
          margin: 0;
          font-size: 14px;
          color: var(--el-text-color-secondary);
          font-weight: 500;
        }
      }

      .model-status {
        .el-tag {
          font-weight: 600;
          padding: 8px 16px;
          border-radius: 20px;
        }
      }
    }

    .progress-section {
      .progress-info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;

        .progress-label {
          font-size: 14px;
          font-weight: 500;
          color: var(--el-text-color-regular);
        }

        .progress-value {
          font-size: 16px;
          font-weight: 600;
          color: var(--el-color-primary);
        }
      }

      .custom-progress {
        :deep(.el-progress-bar__outer) {
          border-radius: 8px;
          background: var(--el-fill-color-light);
        }

        :deep(.el-progress-bar__inner) {
          border-radius: 8px;
        }
      }
    }
  }

  .details-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
    margin-bottom: 24px;

    .detail-item {
      background: var(--el-bg-color);
      border: 1px solid var(--el-border-color-lighter);
      border-radius: 8px;
      padding: 16px;
      transition: all 0.2s ease;

      &:hover {
        border-color: var(--el-color-primary-light-7);
        box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
        transform: translateY(-1px);
      }

      .detail-label {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 8px;
        font-size: 13px;
        font-weight: 500;
        color: var(--el-text-color-secondary);

        .el-icon {
          color: var(--el-color-primary);
        }
      }

      .detail-value {
        font-size: 14px;
        font-weight: 500;
        color: var(--el-text-color-primary);
        word-break: break-all;

        &.code-text {
          font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
          background: var(--el-fill-color-light);
          padding: 4px 8px;
          border-radius: 4px;
          font-size: 12px;
        }
      }
    }
  }

  .error-section {
    background: linear-gradient(135deg, #fef0f0 0%, #fde2e2 100%);
    border: 1px solid #fecaca;
    border-radius: 8px;
    padding: 16px;

    .error-header {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 12px;
      font-size: 14px;
      font-weight: 600;
      color: #dc2626;
    }

    .error-content {
      .el-tag {
        border-radius: 6px;
        font-weight: 500;
      }
    }
  }
}
</style>