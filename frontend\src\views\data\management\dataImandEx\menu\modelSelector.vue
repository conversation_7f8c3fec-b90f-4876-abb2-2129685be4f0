<template>
  <div class="model-selector" :class="{ disabled: props.disabled }">
    <el-tooltip content="回归模型构建" placement="bottom">
      <el-button :disabled="props.disabled" @click="openModal" class="menu-button" circle>
        <ModelIcon class="menu-icon normal" />
        <ModelSelectedIcon class="menu-icon hover" />
      </el-button>
    </el-tooltip>

    <el-dialog v-model="isModalVisible" title="模型中心" width="1200px" class="model-center-dialog" :append-to-body="true">
      <ModelSelect
        :modelCategories="modelCategories"
        :getCategoryIcon="getCategoryIcon"
        :getLargeIcon="getLargeIcon"
        :rightArrowIcon="rightArrowIcon"
        :isLoading="isLoading"
        @selectModel="selectModel"
      />
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from "vue";
import ModelIcon from "@/assets/svg/model.svg?component";
import ModelSelectedIcon from "@/assets/svg/model_selected_icon.svg?component";
import ModelSelect from "@/components/modelManagement/src/modelSelect/index.vue";
import {
  dynamicModelManager,
  getCategories,
  type ModelCategory,
  type ModelMetadata
} from "@/utils/dynamicModelLoader";

const props = defineProps<{
  disabled?: boolean;
  isFileModified?: boolean; // 新增：文件是否已修改
}>();

defineOptions({
  name: "ModelSelector"
});

const emit = defineEmits<{
  (e: "buildModel", model: { id: string, category: string }): void;
  (e: "checkFileSave"): void; // 新增：检查文件保存状态的事件
}>();

// 响应式数据
const isModalVisible = ref(false);
const modelCategories = ref<ModelCategory[]>([]);
const isLoading = ref(false);

// 获取分类图标
const getCategoryIcon = (categoryKey: string) => {
  switch (categoryKey) {
    case 'linear':
      return new URL('@/assets/svg/linear.svg', import.meta.url).href;
    case 'tree':
      return new URL('@/assets/svg/tree.svg', import.meta.url).href;
    case 'ml':
    default:
      return new URL('@/assets/svg/other.svg', import.meta.url).href;
  }
};

// 获取大图标
const getLargeIcon = (categoryKey: string) => {
  switch (categoryKey) {
    case 'linear':
      return new URL('@/assets/svg/linear_lg.svg', import.meta.url).href;
    case 'tree':
      return new URL('@/assets/svg/tree_lg.svg', import.meta.url).href;
    case 'ml':
    default:
      return new URL('@/assets/svg/other_lg.svg', import.meta.url).href;
  }
};

// 右箭头图标
const rightArrowIcon = new URL('@/assets/svg/right_arrow.svg', import.meta.url).href;

// 初始化动态模型管理器
const initializeModels = async () => {
  // Only run if models are not already loaded
  if (modelCategories.value.length > 0) return;
  try {
    isLoading.value = true;
    await dynamicModelManager.initialize();
    modelCategories.value = getCategories();
    console.log("Dynamic models loaded:", modelCategories.value);
  } catch (error) {
    console.error("Failed to initialize dynamic models:", error);
  } finally {
    isLoading.value = false;
  }
};

// 方法
const openModal = () => {
  if (props.disabled) return;
  
  // 检查文件是否已保存
  if (props.isFileModified) {
    // 触发父组件的文件保存检查
    emit("checkFileSave");
    return;
  }
  
  isModalVisible.value = true;
  // Load models when the dialog is opened, not on page load
  initializeModels();
};

// 暴露方法给父组件调用
defineExpose({
  openModal
});

const selectModel = (model: ModelMetadata, categoryKey: string) => {
  emit("buildModel", { id: model.id, category: categoryKey });
  // 选择后自动关闭
  isModalVisible.value = false;
};

</script>

<style scoped>
.model-selector {
  position: relative;
  display: inline-block;
}

.model-selector.disabled {
  cursor: not-allowed;
}

/* 调整为图标模式 */
.selector-button.icon-only {
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 旧样式保留以兼容 */
.selector-button {
  transition: all 0.3s ease;
}

/* 对话框样式覆盖 */
:deep(.model-center-dialog .el-dialog__header) {
  border-bottom: none;
  margin-right: 0;
  padding: 20px;
}

:deep(.model-center-dialog .el-dialog__body) {
  padding: 0 0 20px 0;
  background: #F9FBFF;
}

:deep(.model-center-dialog .el-dialog__title) {
  font-weight: bold;
  font-size: 18px;
}

/* 加载状态 */
.selector-button.loading {
  pointer-events: none;
  opacity: 0.7;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

.menu-button {
  width: 36px;
  height: 36px;
  border-radius: 2px;
  background: #FFFFFF;
  box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.08);

  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {

    .menu-icon.normal {
      opacity: 0;
    }

    .menu-icon.hover {
      opacity: 1;
    }
  }

  .menu-icon {
    width: 22px;
    height: 22px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    transition: opacity 0.2s ease;
  }

  .normal {
    opacity: 1;
  }

  .hover,
  .selected {
    opacity: 0;
  }

}
</style>
