// 数据处理模块组件导出
import DataTable from "./src/dataTable";
import DataPreprocess from "./src/dataPreprocess";
import DataAnalyze from "./src/dataAnalyze";
import DataVisualize from "./src/dataVisualize";
import VirtualSampleGeneration from "./src/dataGeneration";

// 默认导出
export default {
  DataTable,
  DataPreprocess,
  DataAnalyze,
  DataVisualize,
  VirtualSampleGeneration
};

// 命名导出
export { 
  DataTable,
  DataPreprocess,
  DataAnalyze,
  DataVisualize,
  VirtualSampleGeneration
};

// 导出数据表格相关的类型和组合式函数
export * from "./src/dataTable/types";
export * from "./src/dataTable/composables";

// 导出其他模块的类型定义
export * from "./src/dataAnalyze/types/index";
export * from "./src/dataVisualize/types/index";
export * from "./src/dataPreprocess/types/index";
export * from "./src/dataGeneration/types/index";
