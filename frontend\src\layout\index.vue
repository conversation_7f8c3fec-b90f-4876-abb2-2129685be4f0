<script setup lang="ts">
import "animate.css";
// 引入 src/components/ReIcon/src/offlineIcon.ts 文件中所有使用addIcon添加过的本地图标
import "@/components/ReIcon/src/offlineIcon";
import { setType } from "./types";
import { useLayout } from "./hooks/useLayout";
import { getConfig } from "@/config";
import { useAppStoreHook } from "@/store/modules/app";
import { useSettingStoreHook } from "@/store/modules/settings";
import { useDataThemeChange } from "@/layout/hooks/useDataThemeChange";
import { useWorkspaceStoreHook } from "@/store/modules/workspace";
import { usePermissionStoreHook } from "@/store/modules/permission";
import { useRouter, useRoute } from "vue-router";
import { ElLoading } from "element-plus";
import {
  h,
  ref,
  reactive,
  computed,
  onMounted,
  onBeforeMount,
  onBeforeUnmount,
  defineComponent
} from "vue";
import {
  useDark,
  useGlobal,
  deviceDetection,
  useResizeObserver,
  isAllEmpty
} from "@pureadmin/utils";
import { initializeSocket } from "@/utils/socket";

import LayTag from "./components/lay-tag/index.vue";
import LayNavbar from "./components/lay-navbar/index.vue";
import LayContent from "./components/lay-content/index.vue";
import LaySetting from "./components/lay-setting/index.vue";
import NavVertical from "./components/lay-sidebar/NavVertical.vue";
import LaySidebarItem from "./components/lay-sidebar/components/SidebarItem.vue";
import WorkspaceFileTree from "@/components/fileManagement/src/WorkspaceFileTree/index.vue";
import BackTopIcon from "@/assets/svg/back_top.svg?component";
import ModelView from "@/views/model/modelCenter/index.vue";

const appWrapperRef = ref();
const workspaceFileTreeRef = ref(); // 新增ref
const { isDark } = useDark();
// 全局 Tooltip 效果配置，默认 light
const tooltipEffect = getConfig()?.TooltipEffect ?? 'light';
const { layout } = useLayout();
const isMobile = deviceDetection();
const pureSetting = useSettingStoreHook();
const { $storage } = useGlobal<GlobalPropertiesApi>();
const workspaceStore = useWorkspaceStoreHook();
const router = useRouter();
const route = useRoute();

// 获取菜单数据
const menuData = computed(() => usePermissionStoreHook().wholeMenus);
const defaultActive = computed(() => !isAllEmpty(route.meta?.activePath) ? route.meta.activePath : route.path);

// 获取当前视图
const currentView = computed(() => {
  try {
    return workspaceStore.getCurrentView || 'workspace';
  } catch (error) {
    console.warn('Error accessing currentView:', error);
    return 'workspace';
  }
});

// 工作区事件处理函数
const handleFileSelected = async (filePath) => {
  console.log('File selected:', filePath);
  if (workspaceStore.isExcelFile(filePath)) {
    try {
      // 添加到工作区
      workspaceStore.addFileToWorkspace(filePath);
      workspaceStore.setCurrentFile(filePath);
      workspaceStore.markDataAsSaved(filePath);
      // 导航到带文件路径的Excel文件路由
      await router.push(`/dataManagement/imandex/${encodeURIComponent(filePath)}`);
    } catch (error) {
      console.error('Error navigating to dataImandEx:', error);
    }
  } else {
    // 添加到工作区
    workspaceStore.addFileToWorkspace(filePath);
    workspaceStore.setCurrentFile(filePath);

    try {
      // 导航到文件特定路由
      await router.push(`/workspace/file/${encodeURIComponent(filePath)}`);
      // 通知文件已选中
      window.ipcRenderer.send('workspace-file-selected', filePath);
    } catch (error) {
      console.error('Navigation error:', error);
    }
  }
};

const handleDirectorySelected = (dirPath) => {
  console.log('Directory selected:', dirPath);
};

const handleWorkspaceChanged = (workspacePath) => {
  console.log('Workspace changed:', workspacePath);
  if (workspacePath) {
    router.push(`/workspace/${encodeURIComponent(workspacePath)}`);
  }
};

const set: setType = reactive({
  sidebar: computed(() => {
    return useAppStoreHook().sidebar;
  }),

  device: computed(() => {
    return useAppStoreHook().device;
  }),

  fixedHeader: computed(() => {
    return pureSetting.fixedHeader;
  }),

  classes: computed(() => {
    return {
      hideSidebar: !set.sidebar.opened,
      openSidebar: set.sidebar.opened,
      withoutAnimation: set.sidebar.withoutAnimation,
      mobile: set.device === "mobile"
    };
  }),

  hideTabs: computed(() => {
    return $storage?.configure.hideTabs;
  })
});

function setTheme(layoutModel: string) {
  window.document.body.setAttribute("layout", layoutModel);
  $storage.layout = {
    layout: `${layoutModel}`,
    theme: $storage.layout?.theme,
    darkMode: $storage.layout?.darkMode,
    sidebarStatus: $storage.layout?.sidebarStatus,
    epThemeColor: $storage.layout?.epThemeColor,
    themeColor: $storage.layout?.themeColor,
    overallStyle: $storage.layout?.overallStyle
  };
}

function toggle(device: string, bool: boolean) {
  useAppStoreHook().toggleDevice(device);
  useAppStoreHook().toggleSideBar(bool, "resize");
}

// Handle menu-triggered file opening
const handleMenuOpenFile = async () => {
  try {
    const filePath = await window.ipcRenderer.invoke('dialog:openFile');
    if (filePath) {
      console.log('Menu triggered file selection:', filePath);

      const loadingInstance = ElLoading.service({
        text: '正在加载文件...',
        background: 'rgba(0, 0, 0, 0.7)'
      });

      try {
        // Check if it's an Excel file
        const isExcelFile = /\.(xlsx|xls|csv)$/i.test(filePath);

        if (isExcelFile) {
          // For Excel files, navigate to dataImandEx page
          if (workspaceStore.isExcelFile(filePath)) {
            await router.push(`/dataManagement/imandex/${encodeURIComponent(filePath)}`);
          }
          workspaceStore.setCurrentFile(filePath);
          workspaceStore.markDataAsSaved(filePath);
        } else {
          // For other files, navigate to file-specific route
          console.log('Opening file:', filePath);

          // 添加到工作区
          workspaceStore.addFileToWorkspace(filePath);
          workspaceStore.setCurrentFile(filePath);

          // 使用文件特定路由以支持单独的标签页
          await router.push(`/workspace/file/${encodeURIComponent(filePath)}`);

          // Send the file path to be opened
          setTimeout(() => {
            console.log('Sending file selection event:', filePath);
            window.ipcRenderer.send('workspace-file-selected', filePath);
          }, 300);
        }
      } finally {
        // 延迟关闭加载动画，确保用户能看到切换效果
        setTimeout(() => {
          loadingInstance.close();
        }, 500);
      }
    }
  } catch (error) {
    console.error('Error handling menu file open:', error);
  }
};

useResizeObserver(appWrapperRef, entries => {
  const entry = entries[0];
  const [{ inlineSize: width, blockSize: height }] = entry.borderBoxSize;

  // 更新视口大小
  useAppStoreHook().setViewportSize({ width, height });

  // 只处理移动设备的自适应
  if (width > 0 && width <= 760) {
    // 只在真正的移动设备上才应用移动模式
    if (isMobile) {
      toggle("mobile", false);
      setTheme("vertical");
    }
  } else {
    // 大屏幕使用正常布局，但不自动折叠侧边栏
    setTheme(useAppStoreHook().layout);
  }
});

onMounted(() => {
  if (isMobile) {
    toggle("mobile", false);
  }

  // Initialize workspace store to handle cache migration
  workspaceStore.initializeStore();

  // 监听tab关闭时清除文件树选中状态
  window.addEventListener('clear-filetree-selected-file', () => {
    workspaceFileTreeRef.value?.clearSelectedFile && workspaceFileTreeRef.value.clearSelectedFile();
  });

  // Initialize Socket.IO connection when the main layout loads
  const socketEnabled = import.meta.env.VITE_SOCKETIO_ENABLED === "true";
  if (socketEnabled) {
    try {
      initializeSocket().then(() => {
        console.log('Socket.IO connection initialized successfully in layout');
      }).catch(error => {
        console.error('Failed to initialize Socket.IO connection in layout:', error);
      });
    } catch (error) {
      console.error('Error initializing Socket.IO:', error);
    }
  } else {
    console.log('Socket.IO is disabled');
  }

  // 通过路由判断，仅在非数据导入导出页面添加事件监听，避免重复处理
  const setupMenuListener = () => {
    // 检查当前路径是否是数据导入导出页面
    const isDataImportPage = route.path.includes('/dataManagement/imandex');
    if (!isDataImportPage) {
      // 只在非数据导入导出页面添加事件监听
      window.ipcRenderer.on('menu-triggered-open-file', handleMenuOpenFile);
      console.log('Layout: Registered menu-triggered-open-file listener');
    } else {
      console.log('Layout: Skipped registering menu listener as we are on dataImandEx page');
    }
  };

  // 初始设置
  setupMenuListener();

  // 监听路由变化，动态管理事件监听器
  const unregisterRouteWatcher = router.afterEach((to) => {
    // 先移除所有监听器
    window.ipcRenderer.removeAllListeners('menu-triggered-open-file');

    // 检查新路径是否是数据导入导出页面
    const isDataImportPage = to.path.includes('/dataManagement/imandex');
    if (!isDataImportPage) {
      // 只在非数据导入导出页面添加事件监听
      window.ipcRenderer.on('menu-triggered-open-file', handleMenuOpenFile);
      console.log('Layout: Re-registered menu-triggered-open-file listener after route change');
    } else {
      console.log('Layout: Removed menu listener as we navigated to dataImandEx page');
    }
  });

  // Note: set-single-file-mode event listener is now set up globally in main.ts
});

onBeforeUnmount(() => {
  // Clean up IPC listeners
  window.ipcRenderer.removeAllListeners('menu-triggered-open-file');
  // Note: Global listeners are cleaned up in main.ts
});

onBeforeMount(() => {
  useDataThemeChange().dataThemeChange($storage.layout?.overallStyle);
});

const LayHeader = defineComponent({
  name: "LayHeader",
  render() {
    return h("div", null, h(LayNavbar));
  }
});
</script>

<template>
  <div ref="appWrapperRef" :class="['app-wrapper', set.classes]">
    <div v-show="set.device === 'mobile' && set.sidebar.opened && layout.includes('vertical')" class="app-mask"
      @click="useAppStoreHook().toggleSideBar()" />

      <!-- 顶部菜单布局 -->
      <div class="layout-container">
        <!-- 顶部导航 -->
        <div class="layout-top">
          <LayHeader style="height:100%" />
        </div>

        <!-- 内容区域 -->
        <div class="layout-content">
          <!-- 侧边栏 - 白色背景，竖直内容 -->
          <NavVertical v-show="!pureSetting.hiddenSideBar" class="sidebar-container" />

          <!-- 右侧内容区域 - 有内边距 -->
          <div :class="['right-content', { 'full-width-right-content': currentView === 'model' }]">
            <!-- 文件树/功能区 - 透明背景，竖直内容 -->
            <div v-show="currentView === 'workspace'" class="workspace-panel">
              <WorkspaceFileTree ref="workspaceFileTreeRef" @file-selected="handleFileSelected"
                @directory-selected="handleDirectorySelected" @workspace-changed="handleWorkspaceChanged" />
            </div>

            <div v-show="currentView === 'navigation'" class="navigation-panel">
              <el-scrollbar wrap-class="scrollbar-wrapper" :class="[set.device === 'mobile' ? 'mobile' : 'pc']">
                <el-menu unique-opened mode="vertical" popper-class="pure-scrollbar" class="outer-most select-none"
                  :collapse-transition="false" :popper-effect="tooltipEffect" :default-active="defaultActive">
                  <LaySidebarItem v-for="routesItem in menuData" :key="routesItem.path" :item="routesItem"
                    :base-path="routesItem.path" class="outer-most select-none" />
                </el-menu>
              </el-scrollbar>
            </div>

            <!-- 主内容区 - 卡片，白色背景 -->
            <div :class="['main-container', { 'main-container-model-view': currentView === 'model' }]">
              <div class="main-card">
                <LayTag v-show="currentView !== 'model'" /> <!-- Conditionally hide LayTag -->
                <div class="main-content" :style="currentView === 'model' ? { padding: '0px' } : {}">
                  <el-scrollbar :wrap-style="{
                    display: 'flex',
                    flex: '1 1 auto',
                    'flex-direction': 'column',
                    height: '100%'
                  }" :view-style="{
                      display: 'flex',
                      flex: 'auto',
                      'flex-direction': 'column',
                      height: '100%'
                    }" class="main-el-scrollbar">
                    <el-backtop title="回到顶部" target=".main-content .el-scrollbar__wrap">
                      <BackTopIcon />
                    </el-backtop>
                    <LayContent v-show="currentView !== 'model'" :fixed-header="false" />
                    <ModelView v-if="currentView === 'model'" /> <!-- New: Render ModelView -->
                  </el-scrollbar>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 系统设置 -->
    <LaySetting />
  </div>
</template>

<style lang="scss" scoped>
.app-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);

  &::after {
    display: table;
    clear: both;
    content: "";
  }

  &.mobile.openSidebar {
    position: fixed;
    top: 0;
  }
}

.layout-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
}

.layout-top {
  height: 70px;
  width: 100%;
  z-index: 999;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
}

.layout-content {
  display: flex;
  flex: 1;
  background-color: #fafafa;
  /* 灰色背景 */
  padding: 2px;
  overflow: hidden;
}

/* 深色模式适配 */
html.dark .layout-content {
  background-color: #1e1e1e;
}

.app-mask {
  position: absolute;
  top: 0;
  z-index: 2001;
  width: 100%;
  height: 100%;
  background: #000;
  opacity: 0.3;
}

.sidebar-container {
  width: 56px !important;
  height: 100%;
  background: white !important;
  border-radius: 0;
  overflow: hidden;
  margin-right: 0;
  box-shadow: none;
}

/* 新增右侧内容区域样式 */
.right-content {
  flex: 1;
  display: flex;
  padding: 12px;
  height: 100%;
  overflow: hidden;
  box-sizing: border-box;
}

.full-width-right-content {
  padding: 0; /* Remove padding for model view */
}

/* 深色模式适配 */
html.dark .sidebar-container {
  background: var(--el-bg-color) !important;
}

.workspace-panel,
.navigation-panel {
  width: 220px;
  background: transparent;
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  margin-right: 16px;
}

.navigation-panel :deep(.el-menu) {
  border-right: none;
  background-color: transparent;
}

.navigation-panel :deep(.el-menu-item),
.navigation-panel :deep(.el-sub-menu__title) {
  background-color: transparent;
}

.navigation-panel :deep(.el-menu-item:hover),
.navigation-panel :deep(.el-sub-menu__title:hover) {
  background-color: var(--el-menu-hover-bg-color);
}

.main-container {
  flex: 1;
  height: 100%;
  overflow: hidden;
  /* No default margin-left, it will take up space naturally */
}

.main-container-model-view {
  margin-left: 0px; /* Remove margin when it's the only content and full-width-right-content is applied */
}

.main-card {
  border-radius: 4px;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #FFFFFF;
  box-shadow: 0px 2px 8px 0px rgba(153, 153, 153, 0.2);
  overflow: hidden;
  padding: 0; /* Remove padding from main-card for model view */
}

/* 深色模式适配 */
html.dark .main-card {
  background: var(--el-bg-color);
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.main-content {
  flex: 1 1 auto;
  padding: 8px;
  overflow: hidden;
  display: flex;
  min-height: 0;
  /* Allow children flex items to take full height without overflow */
}

.main-content .el-scrollbar {
  height: 100%;
  width: 100%;
  /* Ensure scrollbar takes full width */
  flex: 1 1 auto;
  min-height: 0;
}

/* Ensure the internal scrollbar structure fills the available height */
.main-el-scrollbar .el-scrollbar__wrap {
  height: 100% !important;
  flex: 1 1 auto;
  min-height: 0;
  /* Allow flex children to shrink */
}

.main-el-scrollbar .el-scrollbar__view {
  display: flex;
  flex-direction: column;
  flex: 1 0 auto;
  height: 100%;
  min-height: 0;
}
</style>
