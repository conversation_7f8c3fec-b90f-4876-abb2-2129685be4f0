from dataclasses import dataclass
import json
from typing import Literal, TypedDict, Union, List, Any, Dict

class GAParams(TypedDict):
    '''
    遗传算法参数
    '''
    population_size: int
    generations: int
    crossover: float
    mutation: float
    max_iter: int
    max_time: int
    criterion: float

class SearchTarget(TypedDict):
    '''
    搜索目标
    '''
    name: str
    value: float

class SearchAlgorithm(TypedDict):
    '''
    搜索算法
    '''
    algorithm: Literal["GeneticAlgorithm"]
    params: Union[GAParams]

class SearchRequest(TypedDict):
    '''
    SearchRequest = {
        uid: str
        target: SearchTarget = {
            name: str
            value: float
        }
        search: SearchAlgorithm = {
            algorithm: Literal["GeneticAlgorithm"]
            params: Union[GAParams] = {
                # 遗传算法参数
                population_size: int
                generations: int
                crossover: float
                mutation: float
                max_iter: int
                max_time: int
                criterion: float
            }
        }
    }
    '''
    uid: str
    target: SearchTarget
    search: SearchAlgorithm

class SearchRequestMultiple(TypedDict):
    '''
    SearchRequestMultiple = {
        targets: List[Any] : [
            {
                model_uid: str
                target: {
                    name: str
                    value: float
                },
                criterion: float
                weight: float
            }
        ]
        feature_ranges: Dict[str, Any] : {
            name: str
            value: float
        }
        search: SearchAlgorithm
    }
    '''
    targets: List[Any]
    feature_ranges: Dict[str, Any]
    search: SearchAlgorithm

@dataclass
class SearchConfig(object):
    '''
    SearchConfig = {
        uid: str
        model_uid: Union[str, List[str]]
        target_name: Union[str, List[str]]
        target_value: Union[float, List[dict]]
        feature_ranges: Union[dict, List[dict]]
        alg_name: str
        alg_param: dict
        asynchronous: bool
        is_rabbitmq_ready: bool
    }
    '''
    uid: Union[int, None]
    model_uid: Union[str, List[str]]
    target_name: Union[str, List[str]]
    target_value: Union[float, List[dict]]
    feature_ranges: Union[dict, List[dict]]
    alg_name: str
    alg_param: dict
    asynchronous: bool
    is_rabbitmq_ready: bool

    def to_dict(self) -> dict:
        return {
            "uid": self.uid,
            "model_uid": self.model_uid,
            "target_name": self.target_name,
            "target_value": self.target_value,
            "feature_ranges": self.feature_ranges,
            "alg_name": self.alg_name,
            "alg_param": self.alg_param,
            "asynchronous": self.asynchronous,
            "is_rabbitmq_ready": self.is_rabbitmq_ready
        }
    
    def to_json(self) -> str:
        return json.dumps(self.to_dict(), ensure_ascii=False)