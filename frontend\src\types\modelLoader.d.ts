/**
 * 模型配置接口
 */
interface ModelConfig {
  name: string;
  displayName: string;
  description: string;
  category: string;
  type: string;
  introduction?: {
    detailedDescription?: string;
    usageTips?: string[];
    scenarios?: string;
    mainParams?: Array<{
      name: string;
      displayName: string;
      description: string;
      value: any;
      type: string;
    }>;
  };
  defaultParams?: Record<string, any>;
  evaluation?: Record<string, any>;
}

/**
 * 模型元数据接口
 */
interface ModelMetadata {
  id: string; // 配置文件名（不含扩展名）
  fileName: string; // 完整文件名
  config: ModelConfig;
  sourcePath: string; // 记录模型配置文件来源路径 (e.g., 'modelParams', 'searchParams')
}

/**
 * 模型分类配置
 */
interface ModelCategory {
  key: string;
  label: string;
  description?: string;
  models: ModelMetadata[];
}

export { ModelConfig, ModelMetadata, ModelCategory };