import { contextBridge, ipc<PERSON><PERSON>er, IpcRendererEvent } from 'electron';

contextBridge.exposeInMainWorld('api', {
  onUpdateProgress: (callback: (event: IpcRendererEvent, progress: number, text: string) => void) => 
    ipcRenderer.on('update-progress', callback),
  onSetVersion: (callback: (event: IpcRendererEvent, version: string) => void) =>
    ipcRenderer.on('set-version', callback),
  quitApp: () => ipcRenderer.send('app-quit'), // 修改：使用现有的 'app-quit' 事件
});
