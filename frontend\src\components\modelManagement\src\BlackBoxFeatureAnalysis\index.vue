<template>
  <div class="black-box-feature-analysis">
    <div class="charts-container">
      <!-- PFI Chart Wrapper -->
      <div class="chart-wrapper">
        <div v-if="pfiData && pfiData.length > 0" class="chart-container">
          <div class="chart-header">
            <span class="chart-title">置换特征重要性 (PFI)</span>
            <div class="button-group">
              <el-button
                type="primary"
                size="small"
                @click="exportPfiData"
                :icon="Document"
              >
                导出数据
              </el-button>
              <el-button
                type="primary"
                size="small"
                @click="savePfiChart"
                :icon="Download"
              >
                保存图片
              </el-button>
            </div>
          </div>
          <div ref="pfiChartRef" class="chart-content" />
        </div>
        <el-empty
          v-else
          description="暂无置换特征重要性（PFI）数据"
          :image-size="100"
        />
      </div>

      <!-- SHAP Chart Wrapper -->
      <div class="chart-wrapper">
        <div v-if="shapData && shapData.length > 0" class="chart-container">
          <div class="chart-header">
            <span class="chart-title">SHAP 值 (对模型输出的影响)</span>
            <div class="button-group">
              <div class="chart-type-controls">
                <button
                  type="button"
                  class="chart-type-button"
                  :class="{ 'is-active': shapChartType === 'beeswarm' }"
                  @click="shapChartType = 'beeswarm'"
                >
                  蜂群图
                </button>
                <button
                  type="button"
                  class="chart-type-button"
                  :class="{ 'is-active': shapChartType === 'importance' }"
                  @click="shapChartType = 'importance'"
                >
                  重要性
                </button>
              </div>
              <el-button
                type="primary"
                size="small"
                @click="exportShapData"
                :icon="Document"
              >
                导出数据
              </el-button>
              <el-button
                type="primary"
                size="small"
                @click="saveShapChart"
                :icon="Download"
              >
                保存图片
              </el-button>
            </div>
          </div>
          <div ref="shapChartRef" class="chart-content" />
        </div>
        <el-empty
          v-else
          description="暂无SHAP分析数据，请启用SHAP特征分析"
          :image-size="100"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick, watch, computed } from "vue";
import { useECharts } from "@pureadmin/utils";
import { ElMessage, ElButton, ElEmpty } from "element-plus";
import * as echarts from "echarts/core";
import {
  TitleComponent,
  TooltipComponent,
  GridComponent,
  VisualMapComponent,
  MarkLineComponent,
  LegendComponent
} from "echarts/components";
import {
  BarChart,
  ScatterChart,
  LineChart,
  CustomChart
} from "echarts/charts";
import { CanvasRenderer } from "echarts/renderers";
import { Download, Document } from "@element-plus/icons-vue";
import {
  exportChartImage,
  exportSingleSheet,
  exportMultiSheet
} from "@/utils/exportUtils";

echarts.use([
  TitleComponent,
  TooltipComponent,
  GridComponent,
  VisualMapComponent,
  MarkLineComponent,
  LegendComponent,
  BarChart,
  ScatterChart,
  LineChart,
  CustomChart,
  CanvasRenderer
]);

const props = defineProps<{
  modelResult: any | null;
  taskUid: string | null;
}>();

const pfiAnalysisData = computed(
  () => props.modelResult?.featureAnalysis?.pfi ?? null
);

const shapAnalysisData = computed(
  () => props.modelResult?.featureAnalysis?.shapAnalysis ?? null
);

const shapData = ref<any>(null);
const pfiData =
  ref<{ feature: string; importance: number; std: number }[] | null>(null);

const shapChartType = ref<'beeswarm' | 'importance'>('beeswarm');

const shapChartRef = ref<HTMLDivElement>();
const pfiChartRef = ref<HTMLDivElement>();

const {
  setOptions: setShapOptions,
  getInstance: getShapInstance
} = useECharts(shapChartRef);
const {
  setOptions: setPfiOptions,
  getInstance: getPfiInstance
} = useECharts(pfiChartRef);

// Watch PFI data
watch(
  pfiAnalysisData,
  newVal => {
    if (newVal && newVal.length > 0) {
      pfiData.value = newVal;
      nextTick(() => {
        if (pfiChartRef.value) {
          renderPfiChart(pfiData.value);
        }
      });
    } else {
      pfiData.value = null;
    }
  },
  { immediate: true, deep: true }
);

// Watch SHAP data
watch(
  shapAnalysisData,
  newVal => {
    if (newVal && newVal.length > 0) {
      shapData.value = newVal;
      nextTick(() => {
        if (shapChartRef.value) {
          renderShapChart(shapData.value);
        }
      });
    } else {
      shapData.value = null;
    }
  },
  { immediate: true, deep: true }
);

// Watch SHAP chart type changes
watch(
  shapChartType,
  () => {
    if (shapData.value && shapChartRef.value) {
      nextTick(() => {
        renderShapChart(shapData.value);
      });
    }
  }
);

const savePfiChart = async () => {
  const chartInstance = getPfiInstance();
  if (!chartInstance) {
    ElMessage.error("图表未初始化");
    return;
  }
  const dataURL = chartInstance.getDataURL({
    type: "png",
    pixelRatio: 2,
    backgroundColor: "#F9FBFF"
  });
  await exportChartImage(dataURL, "PFI.png");
};

const exportPfiData = async () => {
  if (!pfiData.value || pfiData.value.length === 0) {
    ElMessage.warning("没有可导出的PFI数据");
    return;
  }
  const headers = ["feature", "importance", "std"];
  const content = pfiData.value.map(item => [
    item.feature,
    item.importance,
    item.std
  ]);
  await exportSingleSheet(
    { headers, content },
    { suggestedName: "PFI数据", sheetName: "PFI" }
  );
  ElMessage.success("数据已开始导出");
};

const saveShapChart = async () => {
  const chartInstance = getShapInstance();
  if (!chartInstance) {
    ElMessage.error("图表未初始化");
    return;
  }
  const dataURL = chartInstance.getDataURL({
    type: "png",
    pixelRatio: 2,
    backgroundColor: "#ffffff"
  });
  await exportChartImage(dataURL, "SHAP.png");
};

const exportShapData = async () => {
  if (!shapData.value || shapData.value.length === 0) {
    ElMessage.warning("没有可导出的SHAP数据");
    return;
  }

  const sheetsData = shapData.value.reduce((acc, featureData) => {
    const headers = ["featureValue", "shapValue", "yOffset"];
    const content = featureData.points.map(point => [
      point.featureValue,
      point.shapValue,
      point.yOffset
    ]);
    const sheetName = featureData.featureName.replace(/[\\/*?:"<>|]/g, "_");
    acc[`SHAP_${sheetName}`] = { headers, content };
    return acc;
  }, {});

  await exportMultiSheet(sheetsData, { suggestedName: "SHAP数据" });
  ElMessage.success("数据已开始导出");
};

const renderShapChart = (data: any[]) => {
  console.log('SHAP数据:', data);
  
  // 按重要性排序特征（重要性高的在上面）
  const sortedData = [...data].sort((a, b) => (b.importance || 0) - (a.importance || 0));
  const features = sortedData.map(item => item.featureName);
  
  console.log('排序后的特征列表:', features);
  console.log('特征数量:', features.length);

  if (shapChartType.value === 'importance') {
    // SHAP重要性图 - 类似PFI图的柱状图
    const importanceData = sortedData.map(item => ({
      feature: item.featureName,
      importance: item.importance || 0,
      std: 0 // SHAP重要性通常没有标准差，设为0
    }));

    const sortedImportanceData = [...importanceData].sort((a, b) => a.importance - b.importance);
    const importanceFeatures = sortedImportanceData.map(item => item.feature);
    const importanceSeriesData = sortedImportanceData.map(item => item.importance);

    setShapOptions({
      backgroundColor: "#F9FBFF",
      tooltip: {
        trigger: "axis",
        axisPointer: { type: "shadow" },
        formatter: params => {
          const barParam = params.find(p => p.seriesType === "bar");
          if (!barParam) return "";
          const featureName = barParam.name;
          const originalItem = sortedImportanceData.find(d => d.feature === featureName);
          if (!originalItem) return "";
          const importance = originalItem.importance.toFixed(4);
          return `${featureName}<br/>SHAP重要性: ${importance}`;
        }
      },
      grid: { top: "10%", left: "3%", right: "12%", bottom: "3%", containLabel: true },
      xAxis: {
        type: "value",
        name: "SHAP重要性",
        nameLocation: "end",
        nameTextStyle: { color: "#005DFF", opacity: 0.6 },
        axisLabel: { color: "#999999" },
        axisLine: { show: true, lineStyle: { color: "rgba(0, 93, 255, 0.3)" } },
        splitLine: { show: true, lineStyle: { type: "dashed", color: "#E0E6F1" } }
      },
      yAxis: {
        type: "category",
        name: "特征",
        data: importanceFeatures,
        axisLabel: { color: "#999999" },
        axisLine: { show: true, lineStyle: { color: "rgba(0, 93, 255, 0.3)" } },
        splitLine: { show: false }
      },
      series: [
        {
          name: "SHAP重要性",
          type: "bar",
          data: importanceSeriesData,
          itemStyle: {
            color: "#005DFF"
          }
        }
      ]
    });
    return;
  }

  // Beeswarm图的原有逻辑
  const chartData: any[] = [];
  let minFeatureVal = Infinity;
  let maxFeatureVal = -Infinity;
  let minShap = Infinity;
  let maxShap = -Infinity;

  // 先分析所有y_offset的范围，用于确定合适的缩放因子
  let allOffsets: number[] = [];
  sortedData.forEach(featureData => {
    const offsets = featureData.points.map(p => p.y_offset || 0);
    allOffsets = allOffsets.concat(offsets);
  });
  
  const offsetRange = {
    min: Math.min(...allOffsets),
    max: Math.max(...allOffsets)
  };
  console.log('全局offset范围:', offsetRange);
  
  // 计算缩放因子，确保抖动不超过±0.3的范围
  const maxAbsOffset = Math.max(Math.abs(offsetRange.min), Math.abs(offsetRange.max));
  const jitterTarget = 0.3; // 减小目标抖动幅度从±0.6到±0.3
  const scaleFactor = maxAbsOffset > jitterTarget ? jitterTarget / maxAbsOffset : 1;
  console.log('缩放因子:', scaleFactor, '目标抖动:', jitterTarget);

  // 处理数据，为每个特征分配正确的Y轴位置
  sortedData.forEach((featureData, featureIndex) => {
    console.log(`处理特征 ${featureIndex}: ${featureData.featureName}, 点数: ${featureData.points.length}`);
    
    featureData.points.forEach((point, pointIndex) => {
      const { featureValue, shapValue, yOffset } = point;
      
      // 更新特征值范围用于颜色映射
      minFeatureVal = Math.min(minFeatureVal, featureValue);
      maxFeatureVal = Math.max(maxFeatureVal, featureValue);
      // 更新SHAP值范围用于基线横线的跨度
      minShap = Math.min(minShap, shapValue);
      maxShap = Math.max(maxShap, shapValue);

      // 使用缩放后的yOffset作为垂直抖动，并进一步限制范围
      const scaledOffset = (yOffset || 0) * scaleFactor;
      // 确保抖动不会超出特征自己的区域（±0.25）
      const clampedOffset = Math.max(-0.25, Math.min(0.25, scaledOffset));
      const yPosition = featureIndex + clampedOffset;
      
      // 打印前几个点的详细信息
      if (pointIndex < 3) {
        console.log(`特征${featureIndex}-点${pointIndex}: featureValue=${featureValue}, shap=${shapValue.toFixed(3)}, raw_offset=${yOffset}, scaled_offset=${scaledOffset.toFixed(3)}, clamped_offset=${clampedOffset.toFixed(3)}, Y位置=${yPosition.toFixed(3)}`);
      }

      chartData.push({
        value: [
          shapValue,           // x轴：SHAP值
          yPosition,            // y轴：特征索引 + 缩放抖动（用于数值轴）
          featureValue,        // 颜色映射：特征值
          featureData.featureName, // 工具提示用
          featureIndex          // 原始特征索引
        ]
      });
    });
  });

  console.log('图表数据前10个点:', chartData.slice(0, 10));
  console.log('特征值范围:', minFeatureVal, '到', maxFeatureVal);
  console.log('SHAP值范围:', minShap, '到', maxShap);

  // 基线数据：每个特征的零抖动位置（整数索引）
  const baselineData = features.map((_, idx) => [idx]);

  // 根据最长特征名动态调整左侧留白，避免文字被裁切
  const maxFeatureNameLength = features.reduce((m, n) => Math.max(m, (n || '').length), 0);
  const leftPaddingPx = Math.min(320, Math.max(120, maxFeatureNameLength * 8 + 40));

  const option: echarts.EChartsOption = {
    backgroundColor: "#F9FBFF", // 1. 设置背景色
    grid: { 
      left: `${leftPaddingPx}px`,
      right: "10%", 
      bottom: "10%", 
      top: "5%", 
      containLabel: true 
    },
    // 移除legend配置
    tooltip: {
      trigger: "item",
      formatter: (params: any) => {
        const [shap, yPos, featVal, featName] = params.data.value;
        return `
          <div style="font-weight: bold; margin-bottom: 4px;">${featName}</div>
          <div>特征值: <span style="font-weight: bold;">${(+featVal).toFixed(3)}</span></div>
          <div>SHAP值: <span style="font-weight: bold;">${(+shap).toFixed(3)}</span></div>
        `;
      }
    },
    xAxis: {
      type: "value",
      name: "SHAP value (impact on model output)",
      nameLocation: "center",
      nameGap: 30,
      nameTextStyle: {
        fontSize: 14,
        color: "#005DFF", // 2. x轴名称颜色
        fontWeight: "normal"
      },
      position: 'bottom',
      splitLine: { 
        show: false
      },
      axisLine: { 
        show: true,
        onZero: false,
        lineStyle: { color: "rgba(0, 93, 255, 0.3)", width: 1 } // 3. x轴线颜色
      },
      axisLabel: { 
        show: true,
        color: "#999999", // 4. x轴刻度文字颜色
        fontSize: 12,
        fontWeight: "normal"
      },
      axisTick: {
        show: true,
        lineStyle: { color: "rgba(0, 93, 255, 0.3)", width: 1 } // 5. x轴刻度线颜色
      }
    },
    // 双Y轴：
    // - 第一个为数值轴（隐藏标签与分割线），用于承载带抖动的散点坐标
    // - 第二个为类目轴（显示标签与浅灰色横向分割线），用于展示特征名称并保证与坐标线对齐
    yAxis: [
      {
        type: "value",
        // 扩大数值轴范围以容纳更大的抖动
        min: -0.5, // 从-(jitterTarget + 0.1)改为-0.5
        max: (features.length - 1) + 0.5, // 从+(jitterTarget + 0.1)改为+0.5
        inverse: true,
        axisLine: { show: false },
        axisTick: { show: false },
        splitLine: { show: false },
        axisLabel: { show: false }
      },
      {
        type: "category",
        inverse: true,
        data: features,
        axisLine: { show: false },
        axisTick: { show: false },
        splitLine: {
          show: false
        },
        axisLabel: {
          color: "#999999", // 6. y轴特征名称颜色
          fontSize: 13,
          margin: 10,
          align: 'right',
          formatter: (value: string) => {
            if (!value) return '';
            return value.length > 20 ? value.substring(0, 20) + '...' : value;
          }
        }
      }
    ],
    visualMap: {
      type: "continuous",
      dimension: 2, // 使用特征值进行颜色映射
      min: minFeatureVal,
      max: maxFeatureVal,
      precision: 2,
      orient: "vertical",
      right: 10,
      top: "center",
      height: "70%",
      text: ["High", "Low"],
      textStyle: {
        fontSize: 12,
        color: "#333"
      },
      calculable: true,
      inRange: {
        // 标准的蓝-红色谱，类似matplotlib的coolwarm
        color: ["#3A4CC0", "#6788EE", "#94CAEF", "#B7E6F7", "#DDEEF9", "#F7F7F7", "#FADDD9", "#F2A3A7", "#E26B73", "#D13644", "#B40426"]
      },
      formatter: (value: number) => value.toFixed(2),
      itemWidth: 15,
      itemHeight: 120
    },
    series: [
      // 将ZeroLine系列放在前面并降低z，使其位于底部
      {
        type: "line",
        name: "ZeroLine",
        z: 0,
        silent: true, // 1. 设置为不可交互
        markLine: {
          symbol: "none",
          data: [
            {
              xAxis: 0,
              lineStyle: {
                color: "rgba(0, 93, 255, 0.3)", // 2. 与x轴线颜色一致
                width: 1,                       // 3. 与x轴线宽度一致
                type: "solid"
              },
              label: {
                show: false
              }
            }
          ],
          tooltip: { show: false },
          z: 0
        },
        // 不实际绘制折线
        data: [],
        showSymbol: false
      },
      // 自定义每个特征的零偏移横线，位于散点下方
      {
        type: 'custom',
        name: 'FeatureBaselines',
        yAxisIndex: 0,
        z: 1,
        silent: true,
        renderItem: (params, api) => {
          const y = api.value(0) as number;
          const p0 = api.coord([minShap, y]);
          const p1 = api.coord([maxShap, y]);
          return {
            type: 'line',
            shape: {
              x1: p0[0], y1: p0[1], x2: p1[0], y2: p1[1]
            },
            style: {
              stroke: '#E0E6F1',
              lineWidth: 1
            }
          };
        },
        data: baselineData,
        encode: { y: 0 }
      },
      {
        name: "SHAP",
        type: "scatter",
        data: chartData,
        yAxisIndex: 0, // 使用数值轴承载抖动
        symbolSize: 6,
        itemStyle: { 
          opacity: 0.8,
          borderWidth: 0
        },
        emphasis: {
          itemStyle: {
            opacity: 1,
            borderWidth: 1,
            borderColor: "#000",
            symbolSize: 8
          }
        }
      }
    ]
  };

  setShapOptions(option);
};

const renderPfiChart = (
  data: { feature: string; importance: number; std: number }[]
) => {
  const sortedData = [...data].sort((a, b) => a.importance - b.importance);

  const features = sortedData.map(item => item.feature);
  const seriesData = sortedData.map(item => item.importance);
  const errorData = sortedData.map((item, index) => [
    item.importance,
    index,
    item.std
  ]);

  setPfiOptions({
    backgroundColor: "#F9FBFF",
    tooltip: {
      trigger: "axis",
      axisPointer: { type: "shadow" },
      formatter: params => {
        const barParam = params.find(
          p => p.seriesType === "bar" || p.seriesType === "custom"
        );
        if (!barParam) return "";

        const featureName = barParam.name;
        const originalItem = sortedData.find(d => d.feature === featureName);
        if (!originalItem) return "";

        const importance = originalItem.importance.toFixed(4);
        const std = originalItem.std.toFixed(4);

        return `${featureName}<br/>Importance: ${importance}<br/>Std Dev: ${std}`;
      }
    },
    grid: { top: "10%", left: "3%", right: "12%", bottom: "3%", containLabel: true },
    xAxis: {
      type: "value",
      name: "重要性",
      nameLocation: "end",
      nameTextStyle: { color: "#005DFF", opacity: 0.6 },
      axisLabel: { color: "#999999" },
      axisLine: { show: true, lineStyle: { color: "rgba(0, 93, 255, 0.3)" } },
      splitLine: { show: true, lineStyle: { type: "dashed", color: "#E0E6F1" } }
    },
    yAxis: {
      type: "category",
      name: "特征",
      data: features,
      axisLabel: { color: "#999999" },
      axisLine: { show: true, lineStyle: { color: "rgba(0, 93, 255, 0.3)" } },
      splitLine: { show: false }
    },
        series: [
      {
        name: "PFI",
        type: "bar",
        data: seriesData,
        itemStyle: {
          color: "#005DFF"
        }
      },
      {
        name: "Error",
        type: "custom",
        itemStyle: {
          borderWidth: 1.5,
          borderColor: "black"
        },
        renderItem: (params, api) => {
          const yValue = api.value(1);
          const xValue = api.value(0) as number;
          const std = api.value(2) as number;

          const highPoint = api.coord([xValue + std, yValue]);
          const lowPoint = api.coord([xValue - std, yValue]);
          const halfWidth = api.size([0, 1])[1] * 0.1;

          if (isNaN(highPoint[0]) || isNaN(lowPoint[0])) {
            return null;
          }

          const style = {
            stroke: "black",
            lineWidth: 1.5
          };

          return {
            type: "group",
            children: [
              {
                type: "line",
                shape: {
                  x1: lowPoint[0],
                  y1: highPoint[1],
                  x2: highPoint[0],
                  y2: highPoint[1]
                },
                style: style
              },
              {
                type: "line",
                shape: {
                  x1: highPoint[0],
                  y1: highPoint[1] - halfWidth,
                  x2: highPoint[0],
                  y2: highPoint[1] + halfWidth
                },
                style: style
              },
              {
                type: "line",
                shape: {
                  x1: lowPoint[0],
                  y1: lowPoint[1] - halfWidth,
                  x2: lowPoint[0],
                  y2: lowPoint[1] + halfWidth
                },
                style: style
              }
            ]
          };
        },
        data: errorData,
        z: 100,
        encode: { x: 0, y: 1 }
      },
      {
        name: "ZeroLine",
        type: "line",
        markLine: {
          symbol: "none",
          data: [{ xAxis: 0 }],
          lineStyle: {
            type: "dashed",
            color: "black",
            width: 1.5
          },
          label: {
            show: false
          }
        }
      }
    ]
  });
};
</script>

<style scoped lang="scss">
.black-box-feature-analysis {
  position: relative;
  min-height: 400px;
}

.charts-container {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  gap: 30px;
  align-items: stretch;
}

.chart-wrapper {
  flex: 1 1 500px;
  min-width: 400px;
  display: flex;
  flex-direction: column;
}

.chart-container {
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  padding-top: 50px;
  background-color: #f9fbff;
  display: flex;
  flex-direction: column;
  flex-grow: 1;
}

.chart-wrapper .el-empty {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  flex-grow: 1;
  background-color: #f9fbff;
  border-radius: 8px;
  height: 100%;
}


.chart-header {
  position: absolute;
  top: 15px;
  left: 20px;
  right: 20px;
  z-index: 10;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .chart-title {
    font-size: 16px;
    font-weight: 500;
  }

  .button-group {
    display: flex;
    align-items: center;
    gap: 10px;
  }

  .chart-type-controls {
    display: flex;
    gap: 8px;
    margin-right: 10px;
  }

  .chart-type-button {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 12px;
    height: 28px;
    border-radius: 14px;
    border: 1px solid transparent;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.3s ease;
    background: rgba(206, 206, 206, 0.1);
    color: #666;

    &.is-active {
      background: rgba(0, 93, 255, 0.1);
      border-color: #005dff;
      color: #005dff;
      font-weight: 500;
    }

    &:hover {
      background: rgba(0, 93, 255, 0.05);
    }
  }
}

.chart-content {
  height: 500px;
  flex-grow: 1;
}
</style>