<template>
  <div class="re-table-wrapper">
    <slot :data="paginatedData" />
    <div v-if="showPagination" class="re-table-pagination">
      <el-pagination
        v-model:currentPage="currentPage"
        v-model:page-size="internalPageSize"
        :page-sizes="pageSizes"
        :total="data.length"
        layout="total, prev, pager, next, sizes"
        background
        @size-change="handleSizeChange"
      />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed } from "vue";
import { useEpThemeStoreHook } from "@/store/modules/epTheme";

const props = withDefaults(
  defineProps<{
    data: any[];
    pageSize?: number;
  }>(),
  {
    pageSize: 20,
  },
);

const epThemeColor = useEpThemeStoreHook().getEpThemeColor;
const currentPage = ref(1);
const internalPageSize = ref(props.pageSize);

const pageSizes = computed(() => {
  const baseSizes = [20, 50, 100, 200];
  const filteredSizes = baseSizes.filter(size => size < props.data.length);
  const sizes = new Set(filteredSizes);
  if (props.data.length > 0) {
    sizes.add(props.data.length);
  }
  return Array.from(sizes).sort((a, b) => a - b);
});

const showPagination = computed(() => {
  if (props.data.length === 0) return false;
  // If there's any page size option smaller than the total number of items,
  // then pagination should be displayed.
  return pageSizes.value.some(size => size < props.data.length);
});

const paginatedData = computed(() => {
  if (props.data.length <= internalPageSize.value) {
    return props.data;
  }
  const startIndex = (currentPage.value - 1) * internalPageSize.value;
  const endIndex = startIndex + internalPageSize.value;
  return props.data.slice(startIndex, endIndex);
});

const handleSizeChange = () => {
  currentPage.value = 1;
};
</script>

<style scoped>
.re-table-wrapper :deep(.el-table) {
  background-color: transparent;
}

/* Remove default top and bottom borders of el-table */
.re-table-wrapper :deep(.el-table::before),
.re-table-wrapper :deep(.el-table::after) {
  display: none;
}

.re-table-wrapper :deep(.el-table__header-wrapper th.el-table__cell) {
  background-color: transparent;
  color: #999999;
  font-weight: 400;
  border: none;
}

.re-table-wrapper :deep(.el-table__body) {
  border-collapse: separate;
  border-spacing: 0 8px; /* Vertical spacing between rows */
}

.re-table-wrapper :deep(.el-table__body tr) {
  border-radius: 80px;
  background: #f9fbff;
}

.re-table-wrapper :deep(.el-table__body td.el-table__cell) {
  border: none;
  background: transparent; /* Cells should be transparent to show tr background */
}

.re-table-wrapper :deep(.el-table__body tr:hover) {
  background-color: #f0f4ff; /* Hover effect on whole row */
}

.re-table-wrapper :deep(.el-table__body tr:hover > td.el-table__cell) {
  background-color: transparent;
}

.re-table-wrapper :deep(.el-table__body tr td:first-child) {
  border-top-left-radius: 80px;
  border-bottom-left-radius: 80px;
}

.re-table-wrapper :deep(.el-table__body tr td:last-child) {
  border-top-right-radius: 80px;
  border-bottom-right-radius: 80px;
}

.re-table-wrapper :deep(.el-table:not(.is-scrolling-none) .el-table-fixed-column--right) {
  background-color: var(--el-bg-color) !important;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

/* 修复固定列覆盖滚动条的问题 */
.re-table-wrapper :deep(.el-table__body-wrapper) {
  /* 确保滚动条在最上层 */
  position: relative;
  z-index: 1;
}

/* 调整固定列的样式 */
.re-table-wrapper :deep(.el-table__fixed-right) {
  /* 降低固定列的层级，使其不会覆盖滚动条 */
  z-index: auto !important;
}

/* 固定列的背景处理 */
.re-table-wrapper :deep(.el-table__fixed-body-wrapper) {
  /* 为固定列内容区域设置背景，而不是整个固定列容器 */
  background-color: transparent;
}

.re-table-wrapper :deep(.el-table__fixed-right .el-table__fixed-body-wrapper tbody tr) {
  /* 保持与主表格相同的行背景 */
  background: #f9fbff;
}

.re-table-wrapper :deep(.el-table__fixed-right .el-table__fixed-body-wrapper tbody tr:hover) {
  background-color: #f0f4ff;
}

/* 修复固定列单元格的圆角 */
.re-table-wrapper :deep(.el-table__fixed-right .el-table__fixed-body-wrapper tbody tr td:last-child) {
  border-top-right-radius: 80px;
  border-bottom-right-radius: 80px;
}

/* 确保滚动条轨道不被遮挡 */
.re-table-wrapper :deep(.el-table__body-wrapper::-webkit-scrollbar) {
  position: relative;
  z-index: 2;
}

/* 如果需要自定义滚动条样式 */
.re-table-wrapper :deep(.el-table__body-wrapper::-webkit-scrollbar) {
  width: 8px;
  height: 8px;
}

.re-table-wrapper :deep(.el-table__body-wrapper::-webkit-scrollbar-track) {
  background: #f1f1f1;
  border-radius: 4px;
}

.re-table-wrapper :deep(.el-table__body-wrapper::-webkit-scrollbar-thumb) {
  background: #c1c1c1;
  border-radius: 4px;
}

.re-table-wrapper :deep(.el-table__body-wrapper::-webkit-scrollbar-thumb:hover) {
  background: #a8a8a8;
}


.re-table-pagination {
  display: flex;
  justify-content: flex-end;
  margin-top: 16px;
}

/* Pagination Styles */
.re-table-pagination :deep(.el-pagination.is-background .btn-prev),
.re-table-pagination :deep(.el-pagination.is-background .btn-next) {
  border-radius: 50%; /* Circular prev/next buttons */
  background: #F9FBFF;
}

.re-table-pagination :deep(.el-pagination.is-background .el-pager li) {
  border-radius: 6px; /* Rounded corners for page numbers */
  background: #F9FBFF;
}

.re-table-pagination :deep(.el-pagination.is-background .el-pager li:not(.disabled).is-active) {
  background-color: v-bind(epThemeColor); /* Use theme color for active page */
}

</style> 