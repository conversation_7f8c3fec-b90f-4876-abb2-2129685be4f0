<template>
  <el-dialog
    :model-value="dialogStore.dialogs.preprocessData"
    title="数据预处理"
    width="80%"
    :destroy-on-close="true"
    @update:model-value="(val) => !val && handleCancel()"
    @close="handleCancel"
    class="preprocess-dialog"
  >
    <!-- 加载蒙版 -->
    <div v-if="isLoading" class="loading-overlay">
      <div class="loading-content">
        <img :src="loadingIcon" alt="loading" class="loading-icon" />
        <p class="loading-title">检测中</p>
        <el-progress
          :percentage="progress"
          :stroke-width="10"
          striped
          striped-flow
          :duration="20"
          class="loading-progress"
        />
        <p class="loading-text">正在处理...</p>
      </div>
    </div>

    <!-- 主内容 -->
    <div v-if="!isResultView">
      <div class="custom-tabs-container">
        <div
          class="custom-tab"
          :class="{ 'is-active': activeTab === 'missing' }"
          @click="activeTab = 'missing'"
        >
          <img
            :src="
              activeTab === 'missing'
                ? getAssetUrl('missingValue_selected.svg')
                : getAssetUrl('missingValue.svg')
            "
            alt="icon"
            class="tab-icon"
          />
          <span class="tab-label">缺失值填充</span>
        </div>
        <div
          class="custom-tab"
          :class="{ 'is-active': activeTab === 'abnormal' }"
          @click="activeTab = 'abnormal'"
        >
          <img
            :src="
              activeTab === 'abnormal'
                ? getAssetUrl('abnormalValue_selected.svg')
                : getAssetUrl('abnormalValue.svg')
            "
            alt="icon"
            class="tab-icon"
          />
          <span class="tab-label">异常值检测</span>
        </div>
      </div>

      <div class="tab-content">
        <div v-show="activeTab === 'missing'" class="preprocess-form">
          <div class="section-container">
            <el-radio-group
              v-model="metaConfig.algorithm.name"
              class="radio-options-container"
            >
              <div
                v-for="option in missingOptions"
                :key="option.value"
                class="radio-option-wrapper"
                :class="{
                  'is-checked': metaConfig.algorithm.name === option.value
                }"
                @click="handleRadioChange(option.value)"
              >
                <el-radio :value="option.value" class="radio-option">
                  <div class="radio-content">
                    <span class="radio-label">{{ option.label }}</span>
                    <span class="radio-description">{{
                      getOptionDescription(option.value)
                    }}</span>
                  </div>
                </el-radio>
              </div>
            </el-radio-group>
          </div>
          <el-form
            :model="metaConfig"
            label-position="left"
            label-width="80px"
            size="default"
            class="params-form"
          >
            <!-- 当选择统计值填充时 -->
            <el-form-item
              v-if="metaConfig.algorithm.name === 'SimpleImputer'"
              label="填充方式:"
            >
              <el-select
                v-model="metaConfig.algorithm.params!.strategy"
                placeholder="请选择填充方式"
                style="width: 230px"
              >
                <el-option
                  v-for="option in statisticOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
            <!-- 固定值参数 -->
            <el-form-item
              v-if="
                metaConfig.algorithm.name === 'SimpleImputer' &&
                metaConfig.algorithm.params?.strategy === 'constant'
              "
              label="固定值:"
            >
              <el-input
                v-model="metaConfig.algorithm.params!.fill_value"
                placeholder="请输入固定值"
                style="width: 230px"
              />
            </el-form-item>
          </el-form>
        </div>

        <div v-show="activeTab === 'abnormal'" class="preprocess-form">
          <div class="section-container">
            <el-radio-group
              v-model="metaConfig.algorithm.name"
              class="radio-options-container"
            >
              <div
                v-for="option in abnormalOptions"
                :key="option.value"
                class="radio-option-wrapper"
                :class="{
                  'is-checked': metaConfig.algorithm.name === option.value
                }"
                @click="handleRadioChange(option.value)"
              >
                <el-radio :value="option.value" class="radio-option">
                  <div class="radio-content">
                    <span class="radio-label">{{ option.label }}</span>
                    <span class="radio-description">{{
                      getOptionDescription(option.value)
                    }}</span>
                  </div>
                </el-radio>
              </div>
            </el-radio-group>
          </div>
        </div>
      </div>
    </div>
    <!-- 结果视图 -->
    <div v-else class="result-view">
      <!-- 成功检测到异常值 -->
      <div v-if="detectionResult && detectionResult.success && outlierRows.length > 0">
        <div class="result-banner">
          <div class="banner-text">
            <p class="banner-title">发现异常值</p>
            <p class="banner-subtitle">检测算法: {{ detectionResult.algorithm }}</p>
          </div>
        </div>

        <div class="result-section">
          <h4 class="section-title"><span>01</span>检测结果统计</h4>
          <div class="stats-container">
            <div class="stat-card">
              <p class="stat-value">{{ detectionResult.totalRows }}</p>
              <p class="stat-label">总数据行数</p>
            </div>
            <div class="stat-card">
              <img :src=warningIcon class="card-icon-decorator" />
              <p class="stat-value warning">{{ outlierRows.length }}</p>
              <p class="stat-label">异常值行数</p>
            </div>
            <div class="stat-card">
              <img :src=warningIcon class="card-icon-decorator" />
              <p class="stat-value warning">
                {{ ((outlierRows.length / (detectionResult.totalRows || 1)) * 100).toFixed(1) }}%
              </p>
              <p class="stat-label">异常值比例</p>
            </div>
            <div class="stat-card">
              <img :src=successIcon class="card-icon-decorator" />
              <p class="stat-value success">{{ detectionResult.totalRows - outlierRows.length }}</p>
              <p class="stat-label">删除后则余</p>
            </div>
          </div>
        </div>

        <div class="result-section">
          <h4 class="section-title"><span>02</span>异常值行号</h4>
          <div class="outlier-rows-box">
            {{ outlierRows.map(r => r + 1).join(", ") }}
          </div>
        </div>
      </div>

      <!-- 未发现异常值 -->
      <div v-if="detectionResult && detectionResult.success && outlierRows.length === 0">
        <div class="no-outliers-view">
            <el-icon :size="48" color="#67C23A"><SuccessFilled /></el-icon>
            <h3 class="no-outliers-title">未发现异常值</h3>
            <p class="no-outliers-text">数据质量良好，可以直接进行后续分析</p>
        </div>
      </div>

      <!-- 检测失败 -->
      <div v-if="!detectionResult?.success" class="error-view">
        <el-icon :size="48" color="#F56C6C"><CircleCloseFilled /></el-icon>
        <h3 class="error-title">检测失败</h3>
        <p class="error-text">异常值检测过程中发生错误，请检查数据格式或稍后重试。</p>
        <div class="error-message-box">
          <strong>错误信息：</strong>{{ detectionResult?.message }}
        </div>
      </div>
    </div>


    <template #footer>
      <div v-if="!isResultView" class="dialog-footer">
        <el-button @click="handleCancel" size="default"> 取消 </el-button>
        <el-button
          type="primary"
          @click="handleConfirm"
          size="default"
          :disabled="isLoading"
        >
          {{ confirmButtonText }}
        </el-button>
      </div>
      <div v-else class="dialog-footer">
        <!-- Outlier case -->
        <div v-if="outlierRows.length > 0" class="footer-hint">
          <div class="hint-text-wrapper">
            <el-icon><Warning /></el-icon>
            <span class="hint-text">操作提示</span>
            <span class="hint-text-description">
              点击【确定】将删除这些异常值行，此操作不可撤销。<br />
              点击【取消】保留所有数据，继续其他操作。
            </span>
          </div>
          <div class="footer-buttons">
            <el-button @click="handleCancel" size="default"> 取消 </el-button>
            <el-button type="primary" @click="handleDelete" size="default">
              确定
            </el-button>
          </div>
        </div>
        <!-- No outlier / Error case -->
        <div v-else class="footer-buttons">
          <el-button @click="handleCancel" size="default" type="primary"> 确定 </el-button>
        </div>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { useDialogStore } from "@/store/modules/dialog";
import { useTableDataStore } from "@/store/modules/tableData";
import loadingIcon from "@/assets/svg/loading.svg?url";
import warningIcon from "@/assets/svg/warning.svg?url";
import successIcon from "@/assets/svg/success.svg?url";
import { ElMessage, ElIcon } from "element-plus";
import { WarningFilled, SuccessFilled, CircleCloseFilled, Warning } from '@element-plus/icons-vue';
import { reactive, watch, nextTick, ref, computed } from "vue";
import type { PreprocessConfig } from "@/types/preprocess";

const props = defineProps<{
  detectionResult?: {
    success: boolean;
    labels?: number[];
    totalRows?: number;
    algorithm?: string;
    message?: string;
  } | null;
}>();

const emit = defineEmits<{
  (e: "confirm", config: PreprocessConfig): void;
  (e: "start-outlier-detection", config: PreprocessConfig): void;
  (e: "delete-outliers", outlierRows: number[]): void;
}>();

// store
const dialogStore = useDialogStore();
const tableDataStore = useTableDataStore();

// View and loading state
const isResultView = ref(false);
const isLoading = ref(false);
const progress = ref(0);
let progressInterval: NodeJS.Timeout | null = null;

// Result state
const outlierRows = ref<number[]>([]);

watch(
  () => props.detectionResult,
  (newResult) => {
    if (!newResult) return;

    clearInterval(progressInterval!);
    progress.value = 100;

    setTimeout(() => {
      if (newResult.success) {
        const labels = newResult.labels!;
        outlierRows.value = labels
          .map((label, index) => (label === -1 ? index : -1))
          .filter((index) => index !== -1);
      }
      isLoading.value = false;
      isResultView.value = true;
    }, 300);
  }
);

// tabs state
const activeTab = ref("missing");
const confirmButtonText = computed(() => {
  return activeTab.value === "abnormal" ? "立即检测" : "确定";
});
const getAssetUrl = (name: string) => {
  // vite动态导入svg
  return new URL(`/src/assets/svg/${name}`, import.meta.url).href;
};

const metaConfig = reactive<{
  algorithm: {
    name: string;
    params?: {
      strategy?: string;
      fill_value?: string;
    };
  };
}>({
  algorithm: {
    name: "",
    params: {} // 注意：默认给空对象，后续根据需要可省略
  }
});

// 选项数据
const missingOptions = [
  { label: "按行删除", value: "DropNA" },
  { label: "统计值填充", value: "SimpleImputer" },
  { label: "向前填充", value: "FFill" },
  { label: "向后填充", value: "BFill" },
  { label: "插值填充", value: "Interpolation" }
];
const statisticOptions = [
  { label: "均值", value: "mean" },
  { label: "中位数", value: "median" },
  { label: "众数", value: "most_frequent" },
  { label: "固定值", value: "constant" }
];
const abnormalOptions = [
  { label: "局部离群因子", value: "LocalOutlierFactor" },
  { label: "孤立森林", value: "IsolationForest" },
  { label: "一类支持向量机", value: "OneClassSVM" }
];

// 获取选项描述
const getOptionDescription = (value: string): string => {
  const descriptions: Record<string, string> = {
    // 缺失值处理描述
    DropNA: "删除包含缺失值的行，适用于缺失值较少的情况",
    SimpleImputer: "使用统计值（均值、中位数等）填充缺失值",
    FFill: "使用前一个有效值向前填充，适用于时间序列数据",
    BFill: "使用后一个有效值向后填充，适用于时间序列数据",
    Interpolation: "使用插值方法填充缺失值，适用于连续数据",

    // 异常值检测描述
    LocalOutlierFactor: "基于局部密度的异常值检测，适用于密度不均匀的数据",
    IsolationForest: "基于随机森林的异常值检测，适用于高维数据",
    OneClassSVM: "基于支持向量机的异常值检测，适用于小样本数据"
  };
  return descriptions[value] || "";
};

// 重置配置到初始状态
const resetAllState = () => {
  resetConfig();
  isLoading.value = false;
  isResultView.value = false;
  outlierRows.value = [];
  if (progressInterval) {
    clearInterval(progressInterval);
  }
  progress.value = 0;
};

const resetConfig = () => {
  metaConfig.algorithm.name = "";
  metaConfig.algorithm.params = {
    strategy: undefined,
    fill_value: undefined
  };
};

const handleRadioChange = (value: string) => {
  metaConfig.algorithm.name = value;
  // 如果新选择的算法没有参数，清空参数
  if (value !== "SimpleImputer") {
    metaConfig.algorithm.params = {};
  } else {
    // 如果是SimpleImputer，可以给一个默认值
    metaConfig.algorithm.params = { strategy: "mean" };
  }
};

// 监听对话框状态变化，在对话框打开时重置配置
watch(
  () => dialogStore.dialogs.preprocessData,
  async (newValue, oldValue) => {
    if (newValue && !oldValue) {
      // 对话框从关闭状态变为打开状态，重置配置
      await nextTick();
      activeTab.value = "missing"; // 默认显示第一个 tab
      resetAllState();
      console.log("Data preprocess dialog opened, state reset");
    }
  }
);

// 监听tab切换，重置选项
watch(activeTab, (newTab, oldTab) => {
  if (newTab !== oldTab) {
    resetConfig();
  }
});

function handleCancel() {
  dialogStore.hideDialog("preprocessData");
}

function handleDelete() {
  emit("delete-outliers", outlierRows.value);
}

function handleConfirm() {
  // 校验
  if (!metaConfig.algorithm.name) {
    ElMessage.warning("处理方法不能为空");
    return;
  }
  if (
    activeTab.value === "missing" &&
    metaConfig.algorithm.name === "SimpleImputer" &&
    !metaConfig.algorithm.params?.strategy
  ) {
    ElMessage.warning("请选择填充方法");
    return;
  }
  if (
    activeTab.value === "missing" &&
    metaConfig.algorithm.name === "SimpleImputer" &&
    metaConfig.algorithm.params?.strategy === "constant" &&
    (metaConfig.algorithm.params?.fill_value === "" ||
      metaConfig.algorithm.params?.fill_value === undefined)
  ) {
    ElMessage.warning("请输入固定值");
    return;
  }

  // 构造完整配置
  const config: PreprocessConfig = {
    dataset: {
      meta: {
        headers: tableDataStore.currentTableHeader
      },
      data: tableDataStore.currentTableData
    },
    preprocess: {
      algorithm: {
        name: metaConfig.algorithm.name,
        ...(metaConfig.algorithm.params &&
          Object.keys(metaConfig.algorithm.params).length > 0 && {
            params: metaConfig.algorithm.params
          })
      }
    }
  };

  if (activeTab.value === "abnormal") {
    isLoading.value = true;
    progress.value = 0;
    progressInterval = setInterval(() => {
      if (progress.value < 90) {
        progress.value += 10;
      }
    }, 200);
    emit("start-outlier-detection", config);
  } else {
    emit("confirm", config);
    handleCancel();
  }
}

// Functions to build result HTML (REMOVED)
</script>

<style scoped>
.preprocess-dialog {
  --el-dialog-border-radius: 12px;
  position: relative;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(4px);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 2000;
  border-radius: 12px;
}

.loading-content {
  text-align: center;
  width: 300px;
}

.loading-icon {
  margin-left: 50px;
  margin-bottom: 20px;
}

.loading-title {
  font-size: 20px;
  font-weight: bold;
  color: #333;
  margin-bottom: 20px;
}

.loading-progress {
  width: 100%;
  margin-bottom: 10px;
}

.loading-text {
  font-size: 14px;
  color: #666;
}

.result-view {
  padding: 0 24px;
}

.result-banner {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16px;
  background: url("@/assets/svg/abnormal_header.svg");
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 24px;
  background-size: cover;
  background-position: center;
}
.banner-text {
  margin-left: 70px;
}
.banner-text .banner-title {
  font-size: 20px;
  font-weight: bold;
  color: #3d3d3d;
  margin: 0 0 4px 0;
}
.banner-text .banner-subtitle {
  font-size: 14px;
  color: #3d3d3d;
  margin: 0;
  text-transform: uppercase;
}

.result-section {
  margin-bottom: 24px;
}

.section-title {
  font-size: 18px;
  font-weight: bold;
  color: #3D3D3D;
  margin: 0 0 16px 0;
  display: flex;
  align-items: center;
}
.section-title span {
  display: inline-block;
  background: var(--el-color-primary);
  color: white;
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 4px;
  margin-right: 8px;
}


.stats-container {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
  margin: 30px;
}

.card-icon-decorator {
  position: absolute;
  top: 0;
  right: 0;
  z-index: 1;
}

.stat-card {
  background: #FFFFFF;
  border-radius: 2px;
  padding: 16px;
  text-align: left;
  box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.08);
  border: none;
  position: relative;
}
.stat-card .stat-value {
  font-size: 24px;
  font-weight: bold;
  margin: 0 0 8px 0;
  color: #303133;
}
.stat-card .stat-value.warning {
  color: #E6A23C;
}
.stat-card .stat-value.success {
  color: #67C23A;
}
.stat-card .stat-label {
  font-size: 14px;
  color: #909399;
  margin: 0;
}

.outlier-rows-box {
  color: #3D3D3D;
  margin: 30px;
  font-size: 16px;
  font-weight: bold;
  word-break: break-all;
  line-height: 1.8;
}

.footer-hint {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
  gap: 16px;
  width: 100%;
  background-color: #FEF0F0;
  padding: 8px 12px;
  border-radius: 4px;
}
.hint-text-wrapper {
  display: flex;
  align-items: flex-start;
  color: #ED542A;
  gap: 8px;
}
.hint-text {
  font-weight: bold;
  color: #ED542A;
}
.hint-text-description {
  color: #999999;
  text-align: left;
}
.footer-buttons {
  display: flex;
  gap: 12px;
}

.no-outliers-view,
.error-view {
  text-align: center;
  padding: 60px 20px;
}
.no-outliers-title,
.error-title {
  font-size: 20px;
  margin: 16px 0 8px 0;
}
.no-outliers-text,
.error-text {
  font-size: 14px;
  color: #606266;
  margin-bottom: 24px;
}
.error-title { color: #F56C6C; }
.no-outliers-title { color: #67C23A; }

.error-message-box {
    background: #FEF0F0;
    border: 1px solid #FBC4C4;
    border-radius: 6px;
    padding: 12px;
    margin-top: 16px;
    text-align: left;
    font-size: 13px;
    color: #F56C6C;
}


.custom-tabs-container {
  display: flex;
  gap: 32px;
  justify-content: flex-start;
  align-items: center;
  margin-bottom: 24px;
}

.custom-tab {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  cursor: pointer;
  position: relative;
  user-select: none;
  transition: color 0.3s;
}

.tab-icon {
  width: 24px;
  height: 24px;
  transition: transform 0.3s;
}

.tab-label {
  font-size: 14px;
  font-weight: 500;
  color: var(--el-text-color-regular);
}

.custom-tab.is-active .tab-label {
  color: var(--el-color-primary);
}

.custom-tab::after {
  content: "";
  position: absolute;
  bottom: -1px;
  left: 50%;
  transform: translateX(-50%) scaleX(0);
  width: 28px;
  height: 2px;
  background-color: var(--el-color-primary);
  border-radius: 2px;
  transition: transform 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
}

.custom-tab.is-active::after {
  transform: translateX(-50%) scaleX(1);
}

.tab-content {
  padding: 24px;
  background: #f9fbff;
  border-radius: 8px;
}

.preprocess-form {
  padding: 0 4px;
}

.section-container {
  margin-bottom: 20px;
}

.params-form {
  margin-top: 24px;
  padding-top: 24px;
  border-top: none;
}

.radio-options-container {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
  width: 100%;
  background: transparent;
  border: none;
  padding: 0;
}

.radio-option-wrapper {
  height: 100%;
  padding: 16px;
  border-radius: 2px;
  border: 1px solid var(--el-border-color-light);
  background: #ffffff;
  box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.08);
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  cursor: pointer;
}

.radio-option-wrapper:hover {
  border-color: var(--el-color-primary-light-3);
}

.radio-option-wrapper.is-checked {
  border: 2px solid var(--el-color-primary);
  padding: 15px; /* (16px - 1px) to compensate for the thicker border */
  background: var(--el-color-primary-light-9);
}

.radio-option {
  width: 100%;
  margin: 0;
  height: auto;
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.radio-option :deep(.el-radio__input) {
  margin-top: 2px;
  flex-shrink: 0;
  align-self: flex-start;
}

.radio-option :deep(.el-radio__label) {
  padding-left: 0;
  width: 100%;
  margin: 0;
  white-space: normal;
}

.radio-content {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  width: 100%;
  gap: 6px;
}

.radio-label {
  font-size: 15px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  line-height: 1.4;
}

.radio-description {
  font-size: 13px;
  color: var(--el-text-color-regular);
  line-height: 1.5;
}

/* 暗色模式适配 */
.dark .radio-options-container {
  background: transparent;
}

.dark .radio-option-wrapper {
  background: var(--el-bg-color-overlay);
  border-color: var(--el-border-color-light);
}

.dark .radio-option-wrapper:hover {
  background: var(--el-fill-color-dark);
  border-color: var(--el-color-primary-light-5);
}

.dark .radio-option-wrapper.is-checked {
  background: rgba(var(--el-color-primary-rgb), 0.1);
  border-color: var(--el-color-primary);
}

/* 底部按钮样式 */
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 12px 0 0 0;
  border-top: 1px solid var(--el-border-color-lighter);
  margin-top: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .preprocess-dialog {
    width: 90vw !important;
    margin: 0 auto;
  }

  .radio-option-wrapper {
    padding: 10px;
  }

  .radio-label {
    font-size: 13px;
  }

  .radio-description {
    font-size: 11px;
  }

  .dialog-footer {
    flex-direction: column-reverse;
    gap: 8px;
  }

  .dialog-footer .el-button {
    width: 100%;
  }

  .section-container {
    margin-bottom: 16px;
  }

  .vertical-radio-group {
    gap: 8px;
  }
}
</style>