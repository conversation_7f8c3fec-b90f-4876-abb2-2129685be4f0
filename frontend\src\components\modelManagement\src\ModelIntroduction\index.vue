<template>
  <div class="model-introduction-restyle">
    <!-- Top Part: Name and Basic Description with Background -->
    <div class="intro-header" :style="modelBackgroundStyle">
      <div class="icon-placeholder">
        <img :src="modelIconUrl" alt="model icon" class="model-icon-svg" />
      </div>
      <div class="intro-text">
        <div class="model-name">{{ modelDisplayName || modelConfig?.displayName }}</div>
        <div class="model-type-name">{{ modelType }}</div>
        <p class="description">{{ getBasicDescription() }}</p>
      </div>
    </div>

    <!-- Collapsible Details Card -->
    <el-card class="details-card" shadow="never">
      <div class="details-toggle-row">
        <h4 class="unified-title">算法原理</h4>
        <el-button @click="isDetailsVisible = !isDetailsVisible" class="toggle-details-button">
          {{ isDetailsVisible ? '收起' : '展开' }}
          <el-icon class="el-icon--right">
             <ArrowUp v-if="isDetailsVisible" />
             <ArrowDown v-else />
          </el-icon>
        </el-button>
      </div>
      
      <div class="card-main-content">
        <!-- Algorithm Principle (Always Visible) -->
        <p class="section-content-text">{{ getDetailedDescription() }}</p>

        <!-- Collapsible Sections -->
        <el-collapse-transition>
          <div v-show="isDetailsVisible">
            <div v-if="getUsageTips().length" class="section">
              <h4 class="unified-title">使用建议</h4>
              <div class="tips-grid">
                <div v-for="(tip, index) in getUsageTips()" :key="index" class="tip-card">
                  <img :src="tipsSvgUrl" class="tip-card-decoration" alt="" />
                  <span class="section-content-text">{{ tip }}</span>
                </div>
              </div>
            </div>

            <div v-if="getMainParams() && Object.keys(getMainParams()).length > 0" class="section">
              <h4 class="unified-title">主要参数说明</h4>
              <div class="params-grid">
                <div v-for="(paramConfig, key) in getMainParams()" :key="key" class="param-item">
                  <div class="param-name">
                    {{ paramConfig.name || paramConfig.displayName }}
                  </div>
                  <div class="section-content-text param-description">
                    {{ paramConfig.description }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-collapse-transition>
      </div>
    </el-card>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, watch } from "vue";
import {
  ArrowDown,
  ArrowUp,
} from "@element-plus/icons-vue";
import {
  loadModelParams,
  type ModelParamConfig,
  type SupportedModel
} from "@/utils/modelParamsLoader";
import {
  isMLModel as checkIsMLModel
} from "@/utils/dynamicModelLoader";

// Fix for Vite's glob import deprecation
const svgAssets = import.meta.glob('@/assets/svg/*.svg', { query: '?url', eager: true, import: 'default' });
const getAssetUrl = (name: string) => svgAssets[`/src/assets/svg/${name}.svg`] || '';

const props = defineProps<{
  modelType?: string;
  modelDisplayName?: string;
  isDetailsVisibleDefault?: boolean; // 只传默认值
}>();

// State management
const isDetailsVisible = ref(props.isDetailsVisibleDefault ?? false);
const modelConfig = ref<ModelParamConfig | null>(null);
const mlModelConfig = ref<ModelParamConfig | null>(null);

const modelCategory = computed<'linear' | 'tree' | 'other'>(() => {
    const type = props.modelType;
    if (!type) return 'other';
    if (['LinearRegression', 'Ridge', 'Lasso', 'ElasticNet'].includes(type)) return 'linear';
    if (['DecisionTreeRegressor', 'RandomForestRegressor', 'XGBoost', 'GradientBoostingRegressor'].includes(type)) return 'tree';
    return 'other'; // SVR, MLPRegressor, etc.
});

const modelIconUrl = computed(() => getAssetUrl(`${modelCategory.value}_lg`));
const modelBackgroundUrl = computed(() => getAssetUrl(`${modelCategory.value}_background`));
const tipsSvgUrl = computed(() => getAssetUrl('tips'));


const modelBackgroundStyle = computed(() => ({
  backgroundImage: `url(${modelBackgroundUrl.value})`,
  backgroundSize: 'cover',
  backgroundPosition: 'center',
}));

const isMLModel = computed(() => checkIsMLModel(props.modelType));

const mainParams = computed(() => {
  const config = isMLModel.value ? mlModelConfig.value : modelConfig.value;
  if (config?.introduction?.mainParams) {
    const result: Record<string, any> = {};
    config.introduction.mainParams.forEach((param, index) => {
      result[index] = param;
    });
    return result;
  }
  return {};
});

const loadModelConfiguration = async () => {
  if (!props.modelType) return;
  try {
    if (isMLModel.value) {
      const configModule = await import(`@/config/modelParams/${props.modelType}.json`);
      mlModelConfig.value = configModule.default;
    } else {
      const config = await loadModelParams(props.modelType as SupportedModel);
      modelConfig.value = config;
    }
  } catch (error) {
    console.error("Failed to load model configuration:", error);
  }
};

const getBasicDescription = (): string => {
  const config = isMLModel.value ? mlModelConfig.value : modelConfig.value;
  return config?.description || "加载中...";
};

const getDetailedDescription = (): string => {
  const config = isMLModel.value ? mlModelConfig.value : modelConfig.value;
  return config?.introduction?.detailedDescription || '';
};

const getUsageTips = (): string[] => {
  const config = isMLModel.value ? mlModelConfig.value : modelConfig.value;
  return config?.introduction?.usageTips || config?.tips || [];
};

const getMainParams = () => mainParams.value;

onMounted(loadModelConfiguration);
watch(() => props.modelType, loadModelConfiguration, { immediate: true });
</script>

<style scoped>
.model-introduction-restyle {
  width: 100%;
  padding-bottom: 20px; /* Ensure space for box-shadow */
}

.intro-header {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 24px 16px; /* Increased padding for card overlap */
  color: white;
}

.icon-placeholder {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  width: 64px;
  height: 64px;
}

.model-icon-svg {
  width: 40px;
  height: 40px;
}

.intro-text .model-name {
  font-size: 18px;
  font-weight: bold;
  color: #3D3D3D;
  margin-bottom: 4px;
  line-height: 1.2;
}

.intro-text .model-type-name {
  font-size: 14px;
  text-transform: uppercase;
  color: #3D3D3D;
  margin-bottom: 8px;
  line-height: 1.2;
}

.intro-text .description {
  font-size: 14px;
  color: #999999;
  line-height: 1.4;
  margin: 0;
}

.details-card {
  margin: -10px 16px 0; /* Move card up */
  position: relative; /* Establish stacking context */
  border-radius: 8px;
  background: #FFFFFF;
  box-shadow: 0px 0px 40px 0px rgba(0, 93, 255, 0.0784);
  border: none;
}

:deep(.el-card__body) {
  padding: 16px;
}

.details-toggle-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.toggle-details-button {
  border-radius: 4px;
  background: #F9FBFF;
  border: 1px solid #E9E9EB;
}

.card-main-content {
  margin-top: 16px;
}

.card-main-content > .section-content-text {
  margin-bottom: 24px;
}

.section {
  margin-top: 24px;
}
.section:first-child {
  margin-top: 0;
}

.unified-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin: 0;
}

.section .unified-title {
    margin-bottom: 12px;
}

.section-content-text {
  font-size: 14px;
  color: var(--el-text-color-regular);
  line-height: 1.7;
  margin: 0;
}

.tips-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 12px;
}

.tip-card {
  position: relative;
  padding: 16px;
  border-radius: 2px;
  background: #F9FBFF;
  box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.08);
}

.tip-card-decoration {
  position: absolute;
  top: -7px;
  right: 13px;
  width: 20px;
  height: auto;
  pointer-events: none;
}

.params-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 16px;
}

.param-name {
  font-weight: 600;
  font-size: 14px;
  color: var(--el-text-color-primary);
  margin-bottom: 4px;
}

.param-description {
  color: var(--el-text-color-secondary);
}

</style>
