/**
 * 动态模型配置加载器
 * 用于动态扫描和加载所有模型配置文件
 */
import type {
  ModelConfig,
  ModelMetadata,
  ModelCategory,
} from "@/types/modelTypes";

/**
 * 动态模型管理器类
 * 分离了模型配置和优化算法的加载和管理
 */
export class DynamicModelManager {
  private static instance: DynamicModelManager;
  
  // 分别存储模型配置和优化算法
  private modelConfigs: Map<string, ModelMetadata> = new Map();
  private optimizerConfigs: Map<string, ModelMetadata> = new Map();
  private generationConfigs: Map<string, ModelMetadata> = new Map();
  
  private categories: Map<string, ModelCategory> = new Map();
  private initialized = false;

  private constructor() {}

  public static getInstance(): DynamicModelManager {
    if (!DynamicModelManager.instance) {
      DynamicModelManager.instance = new DynamicModelManager();
    }
    return DynamicModelManager.instance;
  }

  /**
   * 初始化模型配置
   */
  public async initialize(): Promise<void> {
    if (this.initialized) return;

    try {
      // 先加载模型配置，再加载优化算法
      await this.loadModelConfigs();
      await this.loadOptimizerConfigs();
      await this.loadGenerationConfigs();
      this.categorizeModels();
      this.initialized = true;
      console.log("Dynamic model manager initialized successfully");
    } catch (error) {
      console.error("Failed to initialize dynamic model manager:", error);
      throw error;
    }
  }

  /**
   * 动态加载模型配置文件
   * 只加载 modelParams 目录下的文件
   */
  private async loadModelConfigs(): Promise<void> {
    try {
      // 使用 Vite 的 import.meta.glob 只加载模型配置文件
      const configModules = import.meta.glob("@/config/modelParams/*.json", {
        eager: false,
        import: "default",
      });

      // 加载模型配置
      for (const [filePath, importFn] of Object.entries(configModules)) {
        try {
          const fileName = filePath.split("/").pop() || "";
          const configId = fileName.replace(".json", "");

          if (!configId) {
            console.warn(`Invalid file path: ${filePath}`);
            continue;
          }

          // 动态导入配置文件
          const config: ModelConfig = (await importFn()) as ModelConfig;

          // 推断分类
          if (!config.category) {
            config.category = this.inferModelCategory(configId);
          }

          const metadata: ModelMetadata = {
            id: configId,
            fileName: fileName,
            config,
            sourcePath: "modelParams"
          };

          this.modelConfigs.set(configId, metadata);
          console.log(`Loaded model config: ${configId} from ${fileName}`);
        } catch (error) {
          console.warn(`Failed to load model config from ${filePath}:`, error);
        }
      }

      console.log(`Successfully loaded ${this.modelConfigs.size} model configurations`);
    } catch (error) {
      console.error("Failed to load model configurations:", error);
    }
  }

  /**
   * 动态加载优化算法配置文件
   * 只加载 searchParams 目录下的文件
   */
  private async loadOptimizerConfigs(): Promise<void> {
    try {
      // 使用 Vite 的 import.meta.glob 只加载优化算法配置文件
      const configModules = import.meta.glob("@/config/searchParams/*.json", {
        eager: false,
        import: "default",
      });

      // 加载优化算法配置
      for (const [filePath, importFn] of Object.entries(configModules)) {
        try {
          const fileName = filePath.split("/").pop() || "";
          const configId = fileName.replace(".json", "");

          if (!configId) {
            console.warn(`Invalid file path: ${filePath}`);
            continue;
          }

          // 动态导入配置文件
          const config: ModelConfig = (await importFn()) as ModelConfig;

          const metadata: ModelMetadata = {
            id: configId,
            fileName: fileName,
            config,
            sourcePath: "searchParams"
          };

          this.optimizerConfigs.set(configId, metadata);
          console.log(`Loaded optimizer config: ${configId} from ${fileName} with type: ${config.type || 'unknown'}`);
        } catch (error) {
          console.warn(`Failed to load optimizer config from ${filePath}:`, error);
        }
      }

      console.log(`Successfully loaded ${this.optimizerConfigs.size} optimizer configurations`);
    } catch (error) {
      console.error("Failed to load optimizer configurations:", error);
    }
  }

    /**
   * 动态加载优化算法配置文件
   * 只加载 searchParams 目录下的文件
   */
    private async loadGenerationConfigs(): Promise<void> {
      try {
        // 使用 Vite 的 import.meta.glob 只加载优化算法配置文件
        const configModules = import.meta.glob("@/config/generationParams/*.json", {
          eager: false,
          import: "default",
        });
  
        // 加载优化算法配置
        for (const [filePath, importFn] of Object.entries(configModules)) {
          try {
            const fileName = filePath.split("/").pop() || "";
            const configId = fileName.replace(".json", "");
  
            if (!configId) {
              console.warn(`Invalid file path: ${filePath}`);
              continue;
            }
  
            // 动态导入配置文件
            const config: ModelConfig = (await importFn()) as ModelConfig;
  
            const metadata: ModelMetadata = {
              id: configId,
              fileName: fileName,
              config,
              sourcePath: "generationParams"
            };
  
            this.generationConfigs.set(configId, metadata);
            console.log(`Loaded generation config: ${configId} from ${fileName}`);
          } catch (error) {
            console.warn(`Failed to load generation config from ${filePath}:`, error);
          }
        }
  
        console.log(`Successfully loaded ${this.generationConfigs.size} generation configurations`);
      } catch (error) {
        console.error("Failed to load generation configurations:", error);
      }
    }

  /**
   * 根据模型ID推断分类
   */
  private inferModelCategory(modelId: string): string {
    const treeModels = [
      "DecisionTreeRegressor",
      "RandomForestRegressor",
      "GradientBoostingRegressor",
      "XGBoost",
    ];
    const linearModels = ["LinearRegression", "Ridge", "Lasso", "ElasticNet"];

    if (treeModels.includes(modelId)) return "tree";
    if (linearModels.includes(modelId)) return "linear";
    return "ml";
  }

  /**
   * 将模型按分类组织
   */
  private categorizeModels(): void {
    // 清空现有分类
    this.categories.clear();

    // 定义分类信息
    const categoryInfo = {
      linear: {
        key: "linear",
        label: "线性模型",
        description: "基于线性关系的回归模型",
      },
      tree: {
        key: "tree",
        label: "树模型",
        description: "基于决策树的集成学习模型",
      },
      ml: { 
        key: "ml", 
        label: "其他模型", 
        description: "其他机器学习算法模型" 
      }
    };

    // 初始化分类
    Object.values(categoryInfo).forEach((info) => {
      this.categories.set(info.key, { ...info, models: [] });
    });

    // 将模型分配到对应分类 - 只处理modelConfigs中的模型
    this.modelConfigs.forEach((metadata) => {
      const category = this.categories.get(metadata.config.category);
      if (category) {
        category.models.push(metadata);
      }
    });

    // 移除空分类
    this.categories.forEach((category, key) => {
      if (category.models.length === 0) {
        this.categories.delete(key);
      }
    });
  }

  /**
   * 获取所有模型配置
   */
  public getAllModels(): ModelMetadata[] {
    return Array.from(this.modelConfigs.values());
  }

  /**
   * 获取所有优化算法配置
   */
  public getOptimizationAlgorithms(): ModelMetadata[] {
    return Array.from(this.optimizerConfigs.values());
  }

  /**
   * 根据类型获取优化算法配置
   * @param type 算法类型：'single-target-search' 或 'multi-target-search'
   */
  public getOptimizationAlgorithmsByType(type: 'single-target-search' | 'multi-target-search'): ModelMetadata[] {
    return Array.from(this.optimizerConfigs.values()).filter(metadata => 
      metadata.config.type === type
    );
  }

  /**
   * 获取单目标优化算法
   */
  public getSingleTargetAlgorithms(): ModelMetadata[] {
    return this.getOptimizationAlgorithmsByType('single-target-search');
  }

  /**
   * 获取多目标优化算法
   */
  public getMultiTargetAlgorithms(): ModelMetadata[] {
    return this.getOptimizationAlgorithmsByType('multi-target-search');
  }

  /**
   * 获取所有生成算法配置
   */
  public getGenerationAlgorithms(): ModelMetadata[] {
    return Array.from(this.generationConfigs.values());
  }

  /**
   * 根据ID获取模型配置
   */
  public getModelById(id: string): ModelMetadata | undefined {
    // 先从模型中查找，找不到再从优化算法中查找
    return this.modelConfigs.get(id) || this.optimizerConfigs.get(id);
  }

  /**
   * 获取所有分类
   */
  public getCategories(): ModelCategory[] {
    return Array.from(this.categories.values())
      .filter(category => category.models.length > 0);
  }

  /**
   * 根据分类获取模型
   */
  public getModelsByCategory(category: string): ModelMetadata[] {
    const cat = this.categories.get(category);
    return cat ? cat.models : [];
  }

  /**
   * 检查模型是否为ML模型（非线性模型）
   */
  public isMLModel(modelId: string): boolean {
    const metadata = this.getModelById(modelId);
    return metadata ? metadata.config.category !== "linear" : false;
  }

  /**
   * 获取模型显示名称
   */
  public getModelDisplayName(modelId: string): string {
    const metadata = this.getModelById(modelId);
    return metadata ? metadata.config.displayName : modelId;
  }

  /**
   * 重新加载所有配置
   */
  public async reload(): Promise<void> {
    this.modelConfigs.clear();
    this.optimizerConfigs.clear();
    this.categories.clear();
    this.initialized = false;
    await this.initialize();
  }
}

// 导出单例实例
export const dynamicModelManager = DynamicModelManager.getInstance();

// 便捷函数
export const initializeModelManager = () => dynamicModelManager.initialize();
export const getAllModels = () => dynamicModelManager.getAllModels();
export const getModelById = (id: string) => dynamicModelManager.getModelById(id);
export const getCategories = () => dynamicModelManager.getCategories();
export const getModelsByCategory = (category: string) => dynamicModelManager.getModelsByCategory(category);
export const isMLModel = (modelId: string) => dynamicModelManager.isMLModel(modelId);
export const getModelDisplayName = (modelId: string) => dynamicModelManager.getModelDisplayName(modelId);
export const getOptimizationAlgorithms = () => dynamicModelManager.getOptimizationAlgorithms();
export const getOptimizationAlgorithmsByType = (type: 'single-target-search' | 'multi-target-search') => 
  dynamicModelManager.getOptimizationAlgorithmsByType(type);
export const getSingleTargetAlgorithms = () => dynamicModelManager.getSingleTargetAlgorithms();
export const getMultiTargetAlgorithms = () => dynamicModelManager.getMultiTargetAlgorithms();
export const getGenerationAlgorithms = () => dynamicModelManager.getGenerationAlgorithms();
