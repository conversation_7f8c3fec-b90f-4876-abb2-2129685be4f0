from app.types.search_request import SearchConfig
from app.algorithms.genetic_algorithms import GeneticAlgorithm, ParticleSwarmOptimization, DifferentialEvolution
import numpy as np, pandas as pd
from typing import Any, Callable
from sklearn.base import BaseEstimator
from sklearn.preprocessing import StandardScaler
from typing import List
from app.algorithms.smbo_algorithms import MultiObjectiveBayesianOptimization

SEARCH_CONFIG = {
    "GeneticAlgorithm": {
        "alg": GeneticAlgorithm,
        "alg_params": {
            "population_size": 40,
            "generations": 40,
            "crossover": 0.8,
            "mutation": 0.1,
            "max_iter": 100,
            "max_time": 10000,
            "criterion": 0.01
        },
    },
    "ParticleSwarmOptimization": {
        "alg": ParticleSwarmOptimization,
        "alg_params": {
            "population_size": 40,
            "max_iter": 100,
            "c1": 2.0,
            "c2": 2.0,
            "w": 0.7,
            "max_time": 10000,
        },
    },
    "DifferentialEvolution": {
        "alg": DifferentialEvolution,
        "alg_params": {
            "population_size": 40,
            "max_iter": 100,
            "F": 0.5,
            "CR": 0.7,
            "max_time": 10000,
        },
    },
    "MultiObjectiveBayesianOptimization": {
        "alg": MultiObjectiveBayesianOptimization,
        "alg_params": {
            "n_iterations": 100,
        },
    }
}

def check_search_params_type(alg_name: str, alg_param: dict) -> dict:
    if alg_name == "GeneticAlgorithm":
        new_params = {}
        for key, value in alg_param.items():
            if key == "population_size":
                new_params["population_size"] = int(value)
            elif key == "generations":
                new_params["generations"] = int(value)
            elif key == "crossover":
                new_params["crossover"] = float(value)
            elif key == "mutation":
                new_params["mutation"] = float(value)
            elif key == "max_iter":
                new_params["max_iter"] = int(value)
            elif key == "max_time":
                new_params["max_time"] = int(value)
            elif key == "criterion":
                new_params["criterion"] = float(value)
        return new_params
    elif alg_name == "ParticleSwarmOptimization":
        new_params = {}
        for key, value in alg_param.items():
            if key == "population_size":
                new_params["population_size"] = int(value)
            elif key == "max_iter":
                new_params["max_iter"] = int(value)
            elif key == "c1":
                new_params["c1"] = float(value)
            elif key == "c2":
                new_params["c2"] = float(value)
            elif key == "w":
                new_params["w"] = float(value)
            elif key == "max_time":
                new_params["max_time"] = int(value)
            elif key == "criterion":
                new_params["criterion"] = float(value)
            elif key == "generations":
                new_params["generations"] = int(value)
        return new_params
    elif alg_name == "DifferentialEvolution":
        new_params = {}
        for key, value in alg_param.items():
            if key == "population_size":
                new_params["population_size"] = int(value)
            elif key == "max_iter":
                new_params["max_iter"] = int(value)
            elif key == "F":
                new_params["F"] = float(value)
            elif key == "CR":
                new_params["CR"] = float(value)
            elif key == "max_time":
                new_params["max_time"] = int(value)
            elif key == "criterion":
                new_params["criterion"] = float(value)
        return new_params
    elif alg_name == "MultiObjectiveBayesianOptimization":
        new_params = {}
        for key, value in alg_param.items():
            if key == "n_iterations":
                new_params["n_iterations"] = int(value)
        return new_params
    else:
        raise ValueError(f"Invalid algorithm: {alg_name}")

def check_search_space(feature_ranges: dict, feature_names: List[str]):
    xtrain = pd.DataFrame(feature_ranges)[feature_names]
    xl = xtrain.loc["min", :]
    xu = xtrain.loc["max", :]
    return {
        "xl": xl.values,
        "xu": xu.values,
        "feature_names": feature_names,
    }

def check_alg_param(alg_name: str, alg_param: dict) -> "tuple[Any, dict]":
    if alg_name not in SEARCH_CONFIG.keys():
        raise ValueError(f"Invalid algorithm: {alg_name}")
    alg = SEARCH_CONFIG[alg_name]["alg"]
    params = SEARCH_CONFIG[alg_name]["alg_params"].copy()
    if alg_param is not None:
        for key in alg_param.keys():
            params[key] = alg_param[key]
    return alg, params

class SearchService(object):

    @staticmethod
    def check_search_config(search_config: SearchConfig, feature_names: List[str]) -> "tuple[Any, dict]":
        alg, params = check_alg_param(search_config.alg_name, search_config.alg_param)
        params = check_search_params_type(search_config.alg_name, params)
        feature_ranges = check_search_space(search_config.feature_ranges, feature_names)
        return alg, params, feature_ranges

    @staticmethod
    def run_optimize(
        alg: Any, 
        model: BaseEstimator, 
        scaler: StandardScaler, 
        search_config: SearchConfig, 
        __callable__: Callable[[float, str, str], None]
    ) -> None:
        alg(**search_config.alg_param).fit(
            model=model,
            scaler=scaler,
            target_value=search_config.target_value,
            target_name=search_config.target_name,
            xl=search_config.feature_ranges["xl"],
            xu=search_config.feature_ranges["xu"],
            feature_names=search_config.feature_ranges["feature_names"],
            __callable__=__callable__
        )

class SearchServiceMultiple(object):

    @staticmethod
    def check_search_config_multiple(search_config: SearchConfig) -> "tuple[Any, dict]":
        alg, params = check_alg_param(search_config.alg_name, search_config.alg_param)
        params = check_search_params_type(search_config.alg_name, params)
        return alg, params

    @staticmethod
    def run_optimize_multiple(
        alg: Any, 
        search_config: SearchConfig,
        data_pack: List[dict],
        feature_ranges: dict,
        __callable__: Callable[[float, str, str], None]
    ) -> None:
        alg(**search_config.alg_param).fit(
            data_pack=data_pack,
            feature_ranges=feature_ranges,
            __callable__=__callable__
        )