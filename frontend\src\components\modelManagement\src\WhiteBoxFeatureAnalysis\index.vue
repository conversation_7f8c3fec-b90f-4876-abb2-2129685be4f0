<template>
  <div class="feature-analysis-charts">
    <div v-if="hasData" class="charts-section">
      <!-- 特征权重图 (ECharts) -->
      <div v-if="importanceData" class="chart-container">
        <div class="chart-header">
          <span class="chart-title">特征权重图</span>
          <div class="button-group">
            <el-button
              type="primary"
              size="small"
              @click="exportImportanceData"
              :icon="Document"
            >
              导出数据
            </el-button>
            <el-button
              type="primary"
              size="small"
              @click="saveImportanceChart"
              :icon="Download"
            >
              保存图片
            </el-button>
          </div>
        </div>
        <div ref="importanceChartRef" class="chart-content-echarts" />
      </div>

      <!-- 决策树结构图 (d3-graphviz) -->
      <div v-if="treeStructureData" class="chart-container">
        <div class="chart-header">
          <span class="chart-title">决策树结构</span>
          <div class="button-group">
            <el-button
              type="primary"
              size="small"
              @click="exportTreeData"
              :icon="Document"
            >
              导出数据
            </el-button>
            <el-button
              type="primary"
              size="small"
              @click="saveTreeChart"
              :icon="Download"
            >
              保存图片
            </el-button>
            <el-button
              type="primary"
              size="small"
              @click="showTreeDialog"
              :icon="ZoomIn"
            >
              放大
            </el-button>
          </div>
        </div>
        <div :id="treeChartId" class="chart-content-interactive" />
      </div>
    </div>
    <el-empty v-else description="暂无特征分析数据" class="model-empty" />

    <!-- 决策树放大显示 Dialog -->
    <el-dialog
      v-model="isTreeDialogVisible"
      title="决策树结构"
      width="80%"
      top="5vh"
      append-to-body
    >
      <div :id="treeDialogChartId" class="dialog-chart-content" />
      <template #footer>
        <el-button @click="isTreeDialogVisible = false">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, nextTick } from "vue";
import { Download, Document, ZoomIn } from "@element-plus/icons-vue";
import { ElMessage, ElButton, ElEmpty, ElDialog } from "element-plus";
import { useECharts } from "@pureadmin/utils";
import { graphviz } from "d3-graphviz";
import type { Graphviz } from "d3-graphviz";
import {
  exportChartImage,
  exportSvgToImage,
  exportSingleSheet,
  exportTextFile
} from "@/utils/exportUtils";

// --- Props and Component State ---
interface Props {
  modelResult: {
    featureAnalysis?: {
      importance?: { feature: string; importance: number }[];
      treeStructure?: string;
    };
  };
}

const props = defineProps<Props>();

const analysisData = computed(() => props.modelResult?.featureAnalysis);
const importanceData = computed(() => analysisData.value?.importance);
const treeStructureData = computed(() => analysisData.value?.treeStructure);
const hasData = computed(
  () => !!importanceData.value || !!treeStructureData.value
);

// --- ECharts for Importance ---
const importanceChartRef = ref<HTMLDivElement | null>(null);
const { setOptions: setImportanceOptions, getInstance: getImportanceInstance } =
  useECharts(importanceChartRef, { theme: "default" });

const renderImportanceChart = () => {
  if (!importanceData.value) return;

  const sortedData = [...importanceData.value].sort(
    (a, b) => a.importance - b.importance
  );

  setImportanceOptions({
    backgroundColor: "#F9FBFF",
    tooltip: { trigger: "axis", axisPointer: { type: "shadow" } },
    grid: { left: "3%", right: "5%", bottom: "3%", containLabel: true },
    xAxis: {
      type: "value",
      name: "权重",
      boundaryGap: [0, 0.01],
      nameLocation: "end",
      nameTextStyle: { color: "#005DFF", opacity: 0.6 },
      axisLabel: { color: "#999999" },
      axisLine: { show: true, lineStyle: { color: "rgba(0, 93, 255, 0.3)" } },
      splitLine: { show: true, lineStyle: { type: "dashed", color: "#E0E6F1" } }
    },
    yAxis: {
      type: "category",
      data: sortedData.map(item => item.feature),
      axisLabel: { color: "#999999" },
      axisLine: { show: true, lineStyle: { color: "rgba(0, 93, 255, 0.3)" } },
      splitLine: { show: false }
    },
    series: [
      {
        name: "Importance",
        type: "bar",
        data: sortedData.map(item => item.importance),
        itemStyle: { color: "#005DFF" }
      }
    ]
  });
};

const saveImportanceChart = async () => {
  const chartInstance = getImportanceInstance();
  if (!chartInstance) {
    ElMessage.error("图表未初始化");
    return;
  }
  const dataURL = chartInstance.getDataURL({
    type: "png",
    pixelRatio: 2,
    backgroundColor: "#F9FBFF"
  });
  await exportChartImage(dataURL, "特征重要性.png");
};

const exportImportanceData = async () => {
  if (!importanceData.value || importanceData.value.length === 0) {
    ElMessage.warning("没有可导出的特征重要性数据");
    return;
  }
  const headers = ["feature", "importance"];
  const content = importanceData.value.map(item => [
    item.feature,
    item.importance
  ]);
  await exportSingleSheet(
    { headers, content },
    { suggestedName: "特征重要性数据", sheetName: "特征重要性" }
  );
  ElMessage.success("数据已开始导出");
};

// --- d3-graphviz for Tree Structure ---
const treeChartId = "tree-structure-chart";
let treeGraphInstance: Graphviz<any, any, any, any> | null = null;

const renderTreeChart = () => {
  if (!treeStructureData.value) return;
  if (!treeGraphInstance) {
    // 在外层禁用滚轮缩放
    treeGraphInstance = graphviz(`#${treeChartId}`).zoom(false);
  }
  treeGraphInstance.renderDot(treeStructureData.value);
};

const saveTreeChart = async () => {
  const container = document.getElementById(treeChartId);
  const svgElement = container?.querySelector("svg");

  if (!svgElement) {
    ElMessage.error("图表内容为空，无法保存");
    return;
  }
  await exportSvgToImage(svgElement, "决策树结构.png", "#ffffff");
};

const exportTreeData = async () => {
  if (!treeStructureData.value) {
    ElMessage.warning("没有可导出的决策树数据");
    return;
  }
  await exportTextFile(treeStructureData.value, "决策树结构.dot", [
    { name: "DOT文件", extensions: ["dot"] }
  ]);
};

// --- Dialog for Tree Structure ---
const isTreeDialogVisible = ref(false);
const treeDialogChartId = "tree-dialog-chart";
let treeDialogGraphInstance: Graphviz<any, any, any, any> | null = null;

const renderTreeInDialog = () => {
  if (!treeStructureData.value) return;
  if (!treeDialogGraphInstance) {
    treeDialogGraphInstance = graphviz(`#${treeDialogChartId}`).zoom(true);
  }
  treeDialogGraphInstance.renderDot(treeStructureData.value);
};

const showTreeDialog = () => {
  isTreeDialogVisible.value = true;
  nextTick(() => {
    renderTreeInDialog();
  });
};

// --- Lifecycle and Watchers ---
const renderAllCharts = async () => {
  await nextTick();
  renderImportanceChart();
  renderTreeChart();
};

onMounted(() => {
  renderAllCharts();
});

watch(analysisData, renderAllCharts, { deep: true });
</script>

<style lang="scss" scoped>
.feature-analysis-charts {
  .charts-section {
    display: flex;
    flex-direction: column;
    gap: 20px;
  }

  .chart-container {
    position: relative;
    border-radius: 8px;
    overflow: hidden;

    .chart-header {
      position: absolute;
      top: 15px;
      left: 20px;
      right: 20px;
      z-index: 10;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .button-group {
        display: flex;
        gap: 10px;
      }
    }

    .chart-title {
      font-size: 16px;
      font-weight: 500;
    }

    .chart-content-echarts {
      height: 400px;
    }

    .chart-content-interactive {
      background-color: #ffffff;
      overflow: auto;
      height: 500px;
      padding: 1rem;

      :deep(svg) {
        width: 100%;
        height: 100%;
      }
    }
  }

  .model-empty {
    min-height: 200px;
  }

  .dialog-chart-content {
    height: 70vh;
    overflow: auto;
    border: 1px solid #e0e6f1;
    background-color: #ffffff;
    :deep(svg) {
      width: 100%;
      height: 100%;
    }
  }
}
</style>