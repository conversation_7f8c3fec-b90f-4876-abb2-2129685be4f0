import App from "./App.vue";
import router from "./router";
import { setupStore } from "@/store";
import { getPlatformConfig } from "./config";
import {
  useWorkspaceStoreHook,
  useWorkspaceStore,
} from "@/store/modules/workspace";
// Declare workspaceStore in module scope for global IPC handlers
let workspaceStore: any;
import { MotionPlugin } from "@vueuse/motion";
import { useEcharts } from "@/plugins/echarts";
import { createApp, type Directive } from "vue";
import { useElementPlus } from "@/plugins/elementPlus";
import { injectResponsiveStorage } from "@/utils/responsive";
import { initializeSocket } from "@/utils/socket";

import Table from "@pureadmin/table";
// import PureDescriptions from "@pureadmin/descriptions";

// 引入重置样式
import "./style/reset.scss";
// 导入公共样式
import "./style/index.scss";
// 一定要在main.ts中导入tailwind.css，防止vite每次hmr都会请求src/style/index.scss整体css文件导致热更新慢的问题
import "./style/tailwind.css";
import "element-plus/dist/index.css";
// 导入字体图标
import "./assets/iconfont/iconfont.js";
import "./assets/iconfont/iconfont.css";

const app = createApp(App);

// 自定义指令
import * as directives from "@/directives";
Object.keys(directives).forEach((key) => {
  app.directive(key, (directives as { [key: string]: Directive })[key]);
});

// 全局注册@iconify/vue图标库
import {
  IconifyIconOffline,
  IconifyIconOnline,
  FontIcon,
} from "./components/ReIcon";
app.component("IconifyIconOffline", IconifyIconOffline);
app.component("IconifyIconOnline", IconifyIconOnline);
app.component("FontIcon", FontIcon);

// 全局注册按钮级别权限组件
import { Auth } from "@/components/ReAuth";
import { Perms } from "@/components/RePerms";
app.component("Auth", Auth);
app.component("Perms", Perms);

// 全局注册vue-tippy
import "tippy.js/dist/tippy.css";
import "tippy.js/themes/light.css";
import VueTippy from "vue-tippy";
app.use(VueTippy);

getPlatformConfig(app).then(async (config: any) => {
  setupStore(app);
  // Initialize module-level workspaceStore and clear persisted state
  workspaceStore = useWorkspaceStoreHook();
  workspaceStore.clearWorkspace();

  // Socket.IO initialization has been moved to layout/index.vue
  // to delay connection until after the start screen

  // Set up global IPC event listeners for file handling
  if (window.ipcRenderer) {
    console.log("Setting up global IPC event listeners");

    // Listen for Excel file selection
    window.ipcRenderer.on("excel-file-selected", (_, filePath: string) => {
      console.log("Global: Received excel-file-selected event:", filePath);
      workspaceStore.setCurrentFile(filePath);
      workspaceStore.addFileToWorkspace(filePath);
      workspaceStore.markDataAsSaved(filePath);
    });

    // Listen for single file mode setting
    window.ipcRenderer.on("set-single-file-mode", (_, filePath: string) => {
      console.log("Global: Received set-single-file-mode event:", filePath);
      console.log("Global: Current workspace state before:", {
        singleFileMode: workspaceStore.singleFileMode,
        currentFilePath: workspaceStore.getCurrentFilePath,
      });
      workspaceStore.setSingleFileMode(filePath);
      console.log("Global: Current workspace state after:", {
        singleFileMode: workspaceStore.singleFileMode,
        currentFilePath: workspaceStore.getCurrentFilePath,
      });
    });

    // Listen for workspace file selection
    window.ipcRenderer.on("workspace-file-selected", (_, filePath: string) => {
      console.log("Global: Received workspace-file-selected event:", filePath);
      workspaceStore.setCurrentFile(filePath);
      workspaceStore.addFileToWorkspace(filePath);
    });
  }

  app.use(router);
  await router.isReady();
  injectResponsiveStorage(app, config);
  app.use(MotionPlugin).use(useElementPlus).use(Table).use(useEcharts);
  // 这里需要使用echarts
  // .use(PureDescriptions)
  app.mount("#app").$nextTick(() => {
    postMessage({ payload: "removeLoading" }, "*");
  });
});
