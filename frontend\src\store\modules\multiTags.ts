import { defineStore } from "pinia";
import {
  type multiType,
  type positionType,
  store,
  isUrl,
  isEqual,
  isNumber,
  isBoolean,
  getConfig,
  routerArrays,
  storageLocal,
  responsiveStorageNameSpace
} from "../utils";
import { usePermissionStoreHook } from "./permission";

export const useMultiTagsStore = defineStore({
  id: "pure-multiTags",
  state: () => ({
    // 存储标签页信息（路由信息）
    multiTags: storageLocal().getItem<StorageConfigs>(
      `${responsiveStorageNameSpace()}configure`
    )?.multiTagsCache
      ? storageLocal().getItem<StorageConfigs>(
          `${responsiveStorageNameSpace()}tags`
        )
      : [
          ...routerArrays,
          ...usePermissionStoreHook().flatteningRoutes.filter(
            v => v?.meta?.fixedTag
          )
        ],
    multiTagsCache: storageLocal().getItem<StorageConfigs>(
      `${responsiveStorageNameSpace()}configure`
    )?.multiTagsCache
  }),
  getters: {
    getMultiTagsCache(state) {
      return state.multiTagsCache;
    }
  },
  actions: {
    multiTagsCacheChange(multiTagsCache: boolean) {
      this.multiTagsCache = multiTagsCache;
      if (multiTagsCache) {
        storageLocal().setItem(
          `${responsiveStorageNameSpace()}tags`,
          this.multiTags
        );
      } else {
        storageLocal().removeItem(`${responsiveStorageNameSpace()}tags`);
      }
    },
    tagsCache(multiTags) {
      this.getMultiTagsCache &&
        storageLocal().setItem(
          `${responsiveStorageNameSpace()}tags`,
          multiTags
        );
    },
    handleTags<T>(
      mode: string,
      value?: T | multiType,
      position?: positionType
    ): T {
      switch (mode) {
        case "equal":
          this.multiTags = value;
          this.tagsCache(this.multiTags);
          break;
        case "push":
          {
            const tagVal = value as multiType;
            // 不添加到标签页
            if (tagVal?.meta?.hiddenTag) return;
            // 如果是外链无需添加信息到标签页
            if (isUrl(tagVal?.name)) return;
            // 如果title为空拒绝添加空信息到标签页
            if (tagVal?.meta?.title.length === 0) return;
            // showLink:false 不添加到标签页
            if (isBoolean(tagVal?.meta?.showLink) && !tagVal?.meta?.showLink)
              return;
            
            const tagPath = tagVal.path;
        
            // 针对文件路径的特殊处理
            const existingTagIndex = this.multiTags.findIndex(tag => {
              // 对于首页等固定标签，只需要检查路径
              if (tagPath === "/welcome" || tag?.meta?.fixedTag) {
                return tag.path === tagPath;
              }
              
              // 对于文件路径标签页，特殊处理
              if (tagPath.startsWith("/dataManagement/imandex/") && tag.path.startsWith("/dataManagement/imandex/")) {
                // 比较解码后的文件路径
                const currentFilePath = tagVal?.params?.filePath ? decodeURIComponent(tagVal.params.filePath as string) : '';
                const existingFilePath = tag?.params?.filePath ? decodeURIComponent(tag.params.filePath as string) : '';
                
                if (currentFilePath && existingFilePath && currentFilePath === existingFilePath) {
                  console.log('Found duplicate file tab, replacing:', currentFilePath);
                  // 找到重复的文件标签页，移除旧的
                  this.multiTags.splice(this.multiTags.indexOf(tag), 1);
                  return false; // 继续添加新的标签页
                }
              }
              
              // 对于其他标签，检查路径、query和params
              return tag.path === tagPath &&
                      isEqual(tag?.query, tagVal?.query) &&
                      isEqual(tag?.params, tagVal?.params);
            });
        
            // 如果找到完全相同的标签，直接返回，不添加重复标签
            if (existingTagIndex !== -1) {
              console.log('Tag already exists, skipping:', tagPath);
              return;
            }
        
            // 动态路由可打开的最大数量
            const dynamicLevel = tagVal?.meta?.dynamicLevel ?? -1;
            if (dynamicLevel > 0) {
              if (
                this.multiTags.filter(e => e?.path === tagPath).length >=
                dynamicLevel
              ) {
                // 如果当前已打开的动态路由数大于dynamicLevel，替换第一个动态路由标签
                const index = this.multiTags.findIndex(
                  item => item?.path === tagPath
                );
                index !== -1 && this.multiTags.splice(index, 1);
              }
            }
            
            this.multiTags.push(value);
            this.tagsCache(this.multiTags);
            
            if (
              getConfig()?.MaxTagsLevel &&
              isNumber(getConfig().MaxTagsLevel)
            ) {
              if (this.multiTags.length > getConfig().MaxTagsLevel) {
                this.multiTags.splice(1, 1);
              }
            }
          }
          break;
        case "splice":
          if (!position) {
            const index = this.multiTags.findIndex(v => v.path === value);
            if (index === -1) return;
            this.multiTags.splice(index, 1);
          } else {
            this.multiTags.splice(position?.startIndex, position?.length);
          }
          this.tagsCache(this.multiTags);
          return this.multiTags;
        case "slice":
          return this.multiTags.slice(-1);
      }
    }
  }
});

export function useMultiTagsStoreHook() {
  return useMultiTagsStore(store);
}
