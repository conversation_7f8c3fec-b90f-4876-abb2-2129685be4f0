<template>
  <div v-if="appStore.showStartupModal" class="startup-overlay">
    <div class="startup-content" :class="{ 'flipped': showRecentProjects }">
      <!-- 正面：主界面 -->
      <div class="front-face" :class="{ 'hidden': showRecentProjects }">
        <StartMenuSvg class="startup-svg" />
        <div class="close-button" @click="closeStartupModal">
          <CloseIcon />
        </div>
        <div class="feature-text">
          研发单位：<br>中国航天科技集团有限公司<br>第四研究院四十二所
        </div>
        <div class="logo-buttons-container">
          <div class="action-buttons">
            <button class="workspace-action-button" @click="openDirectory">
              <FolderColoredIcon class="button-svg-icon" />
              <span>打开目录</span>
            </button>
            <button class="workspace-action-button" @click="addFile">
              <FileColoredIcon class="button-svg-icon" />
              <span>添加文件</span>
            </button>
            <button class="workspace-action-button recent-button" @click="toggleRecentProjects">
              <ClockIcon class="button-svg-icon" />
              <span>最近项目</span>
            </button>
          </div>
        </div>
      </div>

      <!-- 背面：最近项目列表 -->
      <div class="back-face">
        <div class="recent-projects-header">
          <h3>最近打开的项目</h3>
          <button class="back-button" @click="toggleRecentProjects">
            <ArrowLeftIcon class="button-svg-icon" />
            返回
          </button>
        </div>
        <div class="recent-projects-list">
          <div v-if="recentProjects.length === 0" class="no-recent-projects">
            <p>暂无最近项目</p>
          </div>
          <div v-else>
            <div 
              v-for="project in recentProjects" 
              :key="project.path"
              class="recent-project-item"
              @click="openRecentProject(project)"
            >
              <div class="project-icon">
                <FolderColoredIcon v-if="project.type === 'directory'" class="icon" />
                <FileColoredIcon v-else class="icon" />
              </div>
              <div class="project-info">
                <div class="project-name">{{ project.name }}</div>
                <div class="project-path">{{ project.path }}</div>
                <div class="project-time">{{ formatTime(project.lastOpened) }}</div>
              </div>
              <button class="remove-button" @click.stop="removeRecentProject(project.path)">
                <CloseIcon class="remove-icon" />
              </button>
            </div>
          </div>
        </div>
        <div class="recent-projects-actions">
          <button class="clear-button" @click="clearAllRecentProjects" :disabled="recentProjects.length === 0">
            清空列表
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { useRouter } from "vue-router";
import { useWorkspaceStoreHook } from "@/store/modules/workspace";
import { useAppStoreHook } from "@/store/modules/app";
import { recentProjectsManager, type RecentProject } from "@/utils/recentProjects";

import StartMenuSvg from "@/assets/svg/start_menu.svg?component";
import LogoNoTitle from "@/assets/svg/logo_no_title.svg?component";
import FileColoredIcon from "@/assets/svg/file_colored.svg?component";
import FolderColoredIcon from "@/assets/svg/folder_colored.svg?component";
import CloseIcon from "@/assets/svg/close.svg?component";
import ClockIcon from "@/assets/svg/clock.svg?component";
import ArrowLeftIcon from "@/assets/svg/arrow_left.svg?component";

// 定义组件名称
defineOptions({
  name: "StartupModal"
});

const router = useRouter();
const workspaceStore = useWorkspaceStoreHook();
const appStore = useAppStoreHook();

// 最近项目相关状态
const showRecentProjects = ref(false);
const recentProjects = ref<RecentProject[]>([]);

const closeStartupModal = () => {
  appStore.setShowStartupModal(false);
};

// 加载最近项目列表
const loadRecentProjects = async () => {
  try {
    // 验证项目是否仍存在，并直接获取验证后的项目列表
    recentProjects.value = await recentProjectsManager.validateRecentProjects();
  } catch (error) {
    console.error("加载最近项目失败:", error);
  }
};

// 切换最近项目显示
const toggleRecentProjects = async () => {
  if (!showRecentProjects.value) {
    await loadRecentProjects();
  }
  showRecentProjects.value = !showRecentProjects.value;
};

// 打开最近项目
const openRecentProject = async (project: RecentProject) => {
  try {
    if (project.type === 'directory') {
      workspaceStore.setWorkspacePath(project.path);
      if (workspaceStore.clearSingleFileMode) workspaceStore.clearSingleFileMode();
      router.push(`/workspace/${encodeURIComponent(project.path)}`);
    } else {
      const isExcelFile = /\.(xlsx|xls|csv)$/i.test(project.path);
      if (isExcelFile) {
        workspaceStore.setCurrentFile(project.path);
        workspaceStore.markDataAsSaved?.(project.path);
        await router.push(`/dataManagement/imandex/${encodeURIComponent(project.path)}`);
      } else {
        const fileDir = project.path.substring(0, project.path.lastIndexOf("/") !== -1 ? project.path.lastIndexOf("/") : project.path.lastIndexOf("\\"));
        workspaceStore.setCurrentFile(project.path);
        await router.push(`/workspace/${encodeURIComponent(fileDir)}`);
        setTimeout(() => {
          window.ipcRenderer.send('workspace-file-selected', project.path);
        }, 300);
      }
    }
    
    // 更新最近项目记录
    await recentProjectsManager.addRecentProject(project.path, project.type);
    closeStartupModal();
  } catch (error) {
    ElMessage.error("打开项目失败");
    // 如果项目不存在，从最近项目中移除
    await removeRecentProject(project.path);
  }
};

// 移除最近项目
const removeRecentProject = async (filePath: string) => {
  try {
    await recentProjectsManager.removeRecentProject(filePath);
    await loadRecentProjects();
  } catch (error) {
    console.error("移除最近项目失败:", error);
  }
};

// 清空所有最近项目
const clearAllRecentProjects = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要清空所有最近项目记录吗？',
      '确认清空',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    );
    
    await recentProjectsManager.clearRecentProjects();
    await loadRecentProjects();
    ElMessage.success('已清空最近项目列表');
  } catch {
    // 用户取消操作
  }
};

// 格式化时间显示
const formatTime = (isoString: string): string => {
  const date = new Date(isoString);
  const now = new Date();
  const diffMs = now.getTime() - date.getTime();
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
  
  if (diffDays === 0) {
    return '今天 ' + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
  } else if (diffDays === 1) {
    return '昨天 ' + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
  } else if (diffDays < 7) {
    return `${diffDays}天前`;
  } else {
    return date.toLocaleDateString('zh-CN');
  }
};

const openDirectory = async () => {
  try {
    const path = await window.ipcRenderer.invoke("dialog:openDirectory");
    if (path) {
      workspaceStore.setWorkspacePath(path);
      if (workspaceStore.clearSingleFileMode) workspaceStore.clearSingleFileMode();
      router.push(`/workspace/${encodeURIComponent(path)}`);
      
      // 添加到最近项目
      await recentProjectsManager.addRecentProject(path, 'directory');
      closeStartupModal();
    }
  } catch (error) {
    ElMessage.error("选择目录失败");
  }
};

const addFile = async () => {
  try {
    const filePath = await window.ipcRenderer.invoke("dialog:openFile");
    if (filePath) {
      const isExcelFile = /\.(xlsx|xls|csv)$/i.test(filePath);
      if (isExcelFile) {
        workspaceStore.setCurrentFile(filePath);
        workspaceStore.markDataAsSaved?.(filePath);
        await router.push(`/dataManagement/imandex/${encodeURIComponent(filePath)}`);
      } else {
        const fileDir = filePath.substring(0, filePath.lastIndexOf("/") !== -1 ? filePath.lastIndexOf("/") : filePath.lastIndexOf("\\"));
        if (!workspaceStore.getCurrentWorkspacePath) {
          workspaceStore.addFileToWorkspace?.(filePath);
        }
        workspaceStore.setCurrentFile(filePath);
        await router.push(`/workspace/${encodeURIComponent(fileDir)}`);
        setTimeout(() => {
          window.ipcRenderer.send('workspace-file-selected', filePath);
        }, 300);
      }
      
      // 添加到最近项目
      await recentProjectsManager.addRecentProject(filePath, 'file');
      closeStartupModal();
    }
  } catch (error) {
    ElMessage.error("打开文件失败");
  }
};



// 组件挂载时加载最近项目
onMounted(() => {
  // 移除自动加载最近项目，只在用户点击按钮时才加载
});
</script>

<style scoped>
/* 启动浮层样式 */
.startup-overlay {
  position: fixed;
  top: 70px;
  /* 留出标题栏空间 */
  left: 0;
  width: 100vw;
  height: calc(100vh - 30px);
  background-color: rgba(0, 0, 0, 0.15);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

/* 深色模式下的启动浮层 */
html.dark .startup-overlay {
  background-color: rgba(0, 0, 0, 0.9);
}

.startup-content {
  position: relative;
  width: 650px;
  max-width: 90vw;
  height: 400px;
  perspective: 1000px;
  transition: transform 0.8s;
  transform-style: preserve-3d;
}

.startup-content.flipped {
  transform: rotateY(180deg);
}

.front-face,
.back-face {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  backface-visibility: hidden;
  border-radius: 12px;
  background: white;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.front-face {
  display: flex;
  justify-content: center;
  align-items: center;
  transition: opacity 0.3s ease;
}

.front-face.hidden {
  opacity: 0;
  pointer-events: none;
}

.back-face {
  transform: rotateY(180deg);
  padding: 20px;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  z-index: 1000;
}

.startup-svg {
  width: 100%;
  height: auto;
}

.close-button {
  position: absolute;
  top: 15px;
  right: 15px;
  width: 30px;
  height: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  font-size: 24px;
  color: black;
}


.close-button svg {
  width: 20px;
  height: 20px;
}

.feature-text {
  position: absolute;
  left: 40px;
  bottom: 100px;
  color: white;
  font-size: 14px;
  max-width: 180px;
  text-align: left;
  line-height: 1.5;
}

.logo-no-title {
  height: auto;
}

.logo-buttons-container {
  position: absolute;
  left: 80px;
  bottom: 25px;
  display: flex;
  align-items: center;
}

.action-buttons {
  display: flex;
  flex-direction: row;
  margin-left: 180px;
  gap: 10px;
}

.workspace-action-button {
  width: 118px;
  height: 32px;
  border-radius: 4px;
  background: #FAFAFA;
  box-sizing: border-box;
  border: 1px solid #005DFF;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  cursor: pointer;
  color: #005DFF;
  font-size: 14px;
  transition: all 0.3s;
}

.workspace-action-button:hover {
  background: rgb(233, 241, 255);
}

.workspace-action-button:active {
  background: rgb(214, 229, 255);
}

.button-svg-icon {
  width: 14px;
  height: 14px;
  display: inline-block;
}

/* 深色模式适配 */
html.dark .workspace-action-button {
  background: #2A2A2A;
  border-color: #005DFF;
  color: #409EFF;
}

html.dark .workspace-action-button:hover {
  background: rgba(0, 93, 255, 0.1);
}

/* 最近项目相关样式 */
.recent-projects-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #e4e6e7;
}

.recent-projects-header h3 {
  margin: 0;
  font-size: 18px;
  color: #333;
}

.back-button {
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 6px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  background: #fff;
  color: #333;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s;
}

.back-button:hover {
  border-color: #005DFF;
  color: #005DFF;
}

.recent-projects-list {
  flex: 1;
  overflow-y: auto;
  margin-bottom: 20px;
}

.no-recent-projects {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  color: #999;
  font-size: 16px;
}

.recent-project-item {
  display: flex;
  align-items: center;
  padding: 12px;
  margin-bottom: 8px;
  border: 1px solid #e4e6e7;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s;
}

.recent-project-item:hover {
  border-color: #005DFF;
  background-color: #f8fbff;
}

.project-icon {
  margin-right: 12px;
}

.project-icon .icon {
  width: 24px;
  height: 24px;
}

.project-info {
  flex: 1;
  min-width: 0;
}

.project-name {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.project-path {
  font-size: 12px;
  color: #666;
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.project-time {
  font-size: 11px;
  color: #999;
}

.remove-button {
  padding: 4px;
  border: none;
  background: transparent;
  color: #999;
  cursor: pointer;
  border-radius: 3px;
  transition: all 0.3s;
  z-index: 1001;
}

.remove-button:hover {
  background-color: #f5f5f5;
  color: #f56c6c;
}

.remove-icon {
  width: 24px;
  height: 24px;
}

.recent-projects-actions {
  display: flex;
  justify-content: center;
  gap: 10px;
  padding-top: 10px;
  border-top: 1px solid #e4e6e7;
}



.clear-button {
  padding: 8px 16px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  background: #fff;
  color: #666;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s;
}

.clear-button:hover:not(:disabled) {
  border-color: #f56c6c;
  color: #f56c6c;
}

.clear-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 深色模式适配 */
html.dark .front-face,
html.dark .back-face {
  background: #2A2A2A;
  color: #fff;
}

html.dark .recent-projects-header {
  border-bottom-color: #404040;
}

html.dark .recent-projects-header h3 {
  color: #fff;
}

html.dark .back-button {
  background: #3A3A3A;
  border-color: #505050;
  color: #fff;
}

html.dark .back-button:hover {
  border-color: #005DFF;
  color: #409EFF;
}

html.dark .recent-project-item {
  border-color: #404040;
  background: #3A3A3A;
}

html.dark .recent-project-item:hover {
  border-color: #005DFF;
  background-color: rgba(0, 93, 255, 0.1);
}

html.dark .project-name {
  color: #fff;
}

html.dark .project-path {
  color: #ccc;
}

html.dark .project-time {
  color: #999;
}

html.dark .remove-button:hover {
  background-color: #404040;
}

html.dark .recent-projects-actions {
  border-top-color: #404040;
}

html.dark .clear-button {
  background: #3A3A3A;
  border-color: #505050;
  color: #ccc;
}

html.dark .clear-button:hover:not(:disabled) {
  border-color: #f56c6c;
  color: #f56c6c;
}

/* 确保所有Element Plus的弹出层都在最上层 */
:global(.el-message-box) {
  z-index: 100000 !important;
}

:global(.el-overlay) {
  z-index: 99999 !important;
}
</style>
