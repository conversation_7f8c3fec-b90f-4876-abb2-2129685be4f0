<template>
  <div class="main">
    <div class="card-header">
      <span>优化列表</span>
    </div>

    <!-- 工具栏 -->
    <div class="toolbar content-card">
      <div class="left">
        <el-button
          type="primary"
          :loading="loading"
          :icon="Refresh"
          @click="fetchOptimizationList"
        >
          刷新
        </el-button>
        <el-switch
          v-model="autoRefresh"
          active-text="自动刷新"
          inactive-text="手动刷新"
          @change="handleAutoRefreshChange"
        />
        <el-select
          v-if="autoRefresh"
          v-model="refreshInterval"
          style="width: 120px"
          size="small"
        >
          <el-option label="5秒" :value="5000" />
          <el-option label="10秒" :value="10000" />
          <el-option label="30秒" :value="30000" />
          <el-option label="1分钟" :value="60000" />
        </el-select>
        <el-button
          v-if="sortBy && sortOrder"
          type="info"
          size="small"
          plain
          @click="clearSort"
        >
          <el-icon>
            <Close />
          </el-icon>
          清除排序
        </el-button>
        <!-- 批量删除按钮 -->
        <el-button
          v-if="selectedOptimizations.length > 0"
          type="danger"
          :icon="Delete"
          @click="handleBatchDelete"
        >
          批量删除 ({{ selectedOptimizations.length }})
        </el-button>
      </div>
      <div class="center">
        <div class="stats">
          <span class="stat-item">
            <span class="label">总数:</span>
            <span class="value">{{ optimizationList.length }}</span>
          </span>
          <span class="stat-item">
            <span class="label">已完成:</span>
            <span class="value success">{{ getStatusCount("completed") }}</span>
          </span>
          <span class="stat-item">
            <span class="label">运行中:</span>
            <span class="value warning">{{ getStatusCount("running") }}</span>
          </span>
          <span class="stat-item">
            <span class="label">等待中:</span>
            <span class="value info">{{ getStatusCount("pending") }}</span>
          </span>
          <span class="stat-item">
            <span class="label">失败:</span>
            <span class="value danger">{{ getStatusCount("failed") }}</span>
          </span>
        </div>
      </div>
      <div class="right">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索优化名称"
          style="width: 300px"
          clearable
          @input="handleSearch"
        >
          <template #prefix>
            <el-icon>
              <Search />
            </el-icon>
          </template>
        </el-input>
      </div>
    </div>

    <div class="table-container content-card">
      <el-scrollbar>
        <ReTable :data="filteredOptimizationList">
          <template #default="{ data }">
            <el-table
              ref="tableRef"
              v-loading="loading"
              :data="data"
              style="width: 100%"
              empty-text="暂无优化任务数据"
              :row-key="(row) => row.uid"
              :default-sort="{ prop: 'createdAt', order: 'descending' }"
              @sort-change="handleSortChange"
              @selection-change="handleSelectionChange"
              @row-click="handleRowClick"
            >
              <!-- 多选列 -->
              <el-table-column
                type="selection"
                width="55"
                :reserve-selection="false"
              />
              <el-table-column
                prop="uid"
                label="UID"
                min-width="200"
                sortable="custom"
              />
              <el-table-column
                prop="name"
                label="优化名称"
                min-width="150"
                sortable="custom"
              />
              <el-table-column
                prop="status"
                label="状态"
                width="120"
                sortable="custom"
              >
                <template #default="{ row }">
                  <el-tag :type="getStatusTagType(row.status)" size="small">
                    {{ getStatusLabel(row.status) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column
                prop="progress"
                label="进度"
                width="120"
                sortable="custom"
              >
                <template #default="{ row }">
                  <el-progress
                    :percentage="getProgressPercentage(row.progress)"
                    :status="getProgressStatus(row.status)"
                    :stroke-width="8"
                  />
                </template>
              </el-table-column>
              <el-table-column
                prop="createdAt"
                label="创建时间"
                width="180"
                sortable="custom"
              >
                <template #default="{ row }">
                  {{ formatDate(row.createdAt) }}
                </template>
              </el-table-column>
              <el-table-column label="操作" width="160" fixed="right">
                <template #default="{ row }">
                  <el-button
                    type="primary"
                    size="small"
                    :disabled="row.status !== 'completed'"
                    @click="viewOptimizationDetails(row)"
                  >
                    详情
                  </el-button>
                  <el-button
                    type="danger"
                    size="small"
                    @click="handleDeleteOptimizations([row.uid])"
                  >
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </template>
        </ReTable>
      </el-scrollbar>
    </div>

    <!-- 优化详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="优化详情"
      width="80%"
      :before-close="handleCloseDetailDialog"
      class="detail-dialog"
      top="8vh"
    >
      <div v-if="selectedOptimization" class="detail-content">
        <!-- 基本信息卡片 -->
        <div class="info-card">
          <div class="header">
            <div class="title">
              <h3>{{ selectedOptimization.name }}</h3>
              <p class="subtitle">优化任务详情</p>
            </div>
            <div class="status">
              <el-tag
                :type="getStatusTagType(selectedOptimization.status)"
                size="large"
                effect="dark"
              >
                {{ getStatusLabel(selectedOptimization.status) }}
              </el-tag>
            </div>
          </div>
          <!-- 进度条 -->
          <div class="progress-section">
            <div class="progress-info">
              <span class="progress-label">优化进度</span>
              <span class="progress-value"
                >{{
                  getProgressPercentage(selectedOptimization.progress)
                }}%</span
              >
            </div>
            <el-progress
              :percentage="getProgressPercentage(selectedOptimization.progress)"
              :status="getProgressStatus(selectedOptimization.status)"
              :stroke-width="12"
              :show-text="false"
              class="custom-progress"
            />
          </div>
        </div>

        <!-- 详细信息网格 -->
        <div class="param-section">
          <div
            class="param-header"
            style="cursor: pointer"
            @click="basicInfoVisible = !basicInfoVisible"
          >
            <div class="param-header-left">
              <el-icon>
                <InfoFilled />
              </el-icon>
              <span>基本信息</span>
            </div>
            <el-icon
              class="header-arrow"
              :class="{ 'is-active': basicInfoVisible }"
            >
              <ArrowRight />
            </el-icon>
          </div>
          <el-collapse-transition>
            <div v-show="basicInfoVisible">
              <div class="details-grid">
                <div class="detail-item">
                  <div class="detail-label">
                    <el-icon><Key /></el-icon>
                    <span>优化任务UID</span>
                  </div>
                  <div class="detail-value code-text">
                    {{ selectedOptimization.uid }}
                  </div>
                </div>
                <div class="detail-item">
                  <div class="detail-label">
                    <el-icon><Document /></el-icon>
                    <span>关联模型UID</span>
                  </div>
                  <div class="detail-value code-text">
                    {{
                      getUniqueModelUids(selectedOptimization.params.modelUid)
                    }}
                  </div>
                </div>
                <div class="detail-item" style="grid-column: span 2">
                  <div class="detail-label">
                    <el-icon><Aim /></el-icon>
                    <span>优化目标配置</span>
                  </div>
                  <div class="detail-value">
                    <div class="targets-container">
                      <template
                        v-if="
                          getFormattedTargets(selectedOptimization).length > 0
                        "
                      >
                        <div
                          v-for="(target, index) in getFormattedTargets(
                            selectedOptimization,
                          )"
                          :key="index"
                          class="target-card"
                        >
                          <div class="target-header">
                            <span class="target-index">#{{ index + 1 }}</span>
                            <el-tag
                              :type="target.isDuplicate ? 'warning' : 'primary'"
                              size="small"
                              effect="plain"
                            >
                              {{ target.isDuplicate ? "重复" : "唯一" }}
                            </el-tag>
                          </div>
                          <div class="target-content">
                            <div class="target-name">{{ target.name }}</div>
                            <div class="target-details">
                              <div class="target-detail-item">
                                <span class="detail-label-sm">目标值:</span>
                                <span class="detail-value-sm">{{
                                  target.value
                                }}</span>
                              </div>
                              <div class="target-detail-item">
                                <span class="detail-label-sm">权重:</span>
                                <el-progress
                                  :percentage="target.weight * 100"
                                  :show-text="false"
                                  :stroke-width="6"
                                  style="flex: 1; margin: 0 8px"
                                />
                                <span class="detail-value-sm"
                                  >{{ (target.weight * 100).toFixed(0) }}%</span
                                >
                              </div>
                              <div class="target-detail-item">
                                <span class="detail-label-sm">精度:</span>
                                <span class="detail-value-sm"
                                  >{{
                                    (target.criterion * 100).toFixed(0)
                                  }}%</span
                                >
                              </div>
                            </div>
                          </div>
                        </div>
                      </template>
                      <div v-else class="no-targets">暂无目标配置信息</div>
                    </div>
                  </div>
                </div>
                <div class="detail-item">
                  <div class="detail-label">
                    <el-icon><Cpu /></el-icon>
                    <span>搜索算法</span>
                  </div>
                  <div class="detail-value">
                    {{ selectedOptimization.params.algName }}
                  </div>
                </div>
                <div class="detail-item">
                  <div class="detail-label">
                    <el-icon><Timer /></el-icon>
                    <span>异步执行</span>
                  </div>
                  <div class="detail-value">
                    <el-tag
                      :type="
                        selectedOptimization.asynchronous ? 'success' : 'info'
                      "
                      size="small"
                      effect="light"
                    >
                      {{ selectedOptimization.asynchronous ? "是" : "否" }}
                    </el-tag>
                  </div>
                </div>
                <div class="detail-item">
                  <div class="detail-label">
                    <el-icon><Calendar /></el-icon>
                    <span>创建时间</span>
                  </div>
                  <div class="detail-value">
                    {{ formatDate(selectedOptimization.createdAt) }}
                  </div>
                </div>
                <div class="detail-item">
                  <div class="detail-label">
                    <el-icon><RefreshRight /></el-icon>
                    <span>更新时间</span>
                  </div>
                  <div class="detail-value">
                    {{ formatDate(selectedOptimization.updatedAt) }}
                  </div>
                </div>
              </div>
            </div>
          </el-collapse-transition>
        </div>

        <!-- 算法参数 -->
        <div class="param-section">
          <div
            class="param-header"
            style="cursor: pointer"
            @click="algParamsVisible = !algParamsVisible"
          >
            <div class="param-header-left">
              <el-icon>
                <Setting />
              </el-icon>
              <span>算法参数</span>
            </div>
            <el-icon
              class="header-arrow"
              :class="{ 'is-active': algParamsVisible }"
            >
              <ArrowRight />
            </el-icon>
          </div>
          <el-collapse-transition>
            <div v-show="algParamsVisible">
              <div class="param-content">
                <pre>{{
                  JSON.stringify(selectedOptimization.params.algParam, null, 2)
                }}</pre>
              </div>
            </div>
          </el-collapse-transition>
        </div>

        <!-- 优化结果 -->
        <div v-loading="resultsLoading" class="param-section">
          <div class="param-header">
            <div class="param-header-left">
              <el-icon>
                <DataAnalysis />
              </el-icon>
              <span>优化结果</span>
            </div>
            <div class="param-header-right">
              <el-button
                :icon="Download"
                size="small"
                type="primary"
                :disabled="optimizationResults.length === 0"
                @click="exportOptimizationResults"
              >
                导出
              </el-button>
            </div>
          </div>
          <div class="param-content">
            <ReTable :data="optimizationResults">
              <template #default="{ data }">
                <el-table
                  :data="data"
                  max-height="300px"
                  empty-text="暂无优化结果数据"
                  style="width: 100%"
                >
                  <el-table-column type="index" label="#" width="50" />
                  <el-table-column
                    v-if="
                      optimizationTargetColumn.prop &&
                      resultTableColumns.some(
                        (c) => c.prop === optimizationTargetColumn.prop,
                      )
                    "
                    :prop="optimizationTargetColumn.prop"
                    :label="optimizationTargetColumn.label"
                    sortable
                    show-overflow-tooltip
                  />
                  <el-table-column
                    v-for="column in filteredResultTableColumns"
                    :key="column.prop"
                    :prop="column.prop"
                    :label="column.label"
                    sortable
                    show-overflow-tooltip
                  />
                </el-table>
              </template>
            </ReTable>
          </div>
        </div>

        <!-- 错误信息 -->
        <div v-if="selectedOptimization.error" class="error-section">
          <div class="error-header">
            <el-icon color="#f56c6c"><Warning /></el-icon>
            <span>错误信息</span>
          </div>
          <div class="error-content">
            <el-tag type="danger" effect="dark" size="large">{{
              selectedOptimization.error
            }}</el-tag>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button size="large" @click="detailDialogVisible = false"
            >关闭</el-button
          >
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import {
  getSearchList,
  deleteSearches,
  getSearchInfo,
} from "@/api/models/optimize";
import { onMounted, onUnmounted, ref, computed } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import {
  Refresh,
  Search,
  Close,
  Warning,
  Delete,
  Key,
  Document,
  Aim,
  Medal,
  Cpu,
  Setting,
  Calendar,
  RefreshRight,
  Timer,
  DataAnalysis,
  ArrowRight,
  Download,
  InfoFilled,
} from "@element-plus/icons-vue";
import type { Search as Optimization } from "@/types/optimize";
import ReTable from "@/components/ReTable/index.vue";
import { exportSingleSheet } from "@/utils/exportUtils";
import { useWorkspaceStoreHook } from "@/store/modules/workspace";

defineOptions({
  name: "OptimizationList",
});

interface FormattedTarget {
  name: string;
  value: number;
  weight: number;
  criterion: number;
  isDuplicate: boolean;
}

// 响应式数据
const optimizationList = ref<Optimization[]>([]);
const loading = ref(false);
const searchKeyword = ref("");
const autoRefresh = ref(false);
const refreshInterval = ref(10000);
const sortBy = ref<string>("");
const sortOrder = ref<"ascending" | "descending" | null>(null);
const tableRef = ref();
let refreshTimer: NodeJS.Timeout | null = null;

const workspaceStore = useWorkspaceStoreHook();

// 多选相关数据
const selectedOptimizations = ref<Optimization[]>([]);

// 详情对话框
const detailDialogVisible = ref(false);
const selectedOptimization = ref<Optimization | null>(null);
const optimizationResults = ref<any[]>([]);
const resultTableColumns = ref<Array<{ prop: string; label: string }>>([]);
const resultsLoading = ref(false);
const algParamsVisible = ref(false);
const basicInfoVisible = ref(false);

const filteredOptimizationList = computed(() => {
  let result = optimizationList.value;

  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase();
    result = result.filter((opt) => opt.name.toLowerCase().includes(keyword));
  }

  if (sortBy.value && sortOrder.value) {
    result = [...result].sort((a, b) => {
      let aValue: any = a[sortBy.value as keyof Optimization];
      let bValue: any = b[sortBy.value as keyof Optimization];

      if (sortBy.value === "progress") {
        aValue = getProgressPercentage(aValue);
        bValue = getProgressPercentage(bValue);
      } else if (sortBy.value === "createdAt" || sortBy.value === "updatedAt") {
        aValue = new Date(aValue).getTime();
        bValue = new Date(bValue).getTime();
      }

      if (aValue == null && bValue == null) return 0;
      if (aValue == null) return sortOrder.value === "ascending" ? -1 : 1;
      if (bValue == null) return sortOrder.value === "ascending" ? 1 : -1;

      if (typeof aValue === "string" && typeof bValue === "string") {
        aValue = aValue.toLowerCase();
        bValue = bValue.toLowerCase();
      }

      if (aValue < bValue) {
        return sortOrder.value === "ascending" ? -1 : 1;
      } else if (aValue > bValue) {
        return sortOrder.value === "ascending" ? 1 : -1;
      } else {
        return 0;
      }
    });
  }

  return result;
});

const optimizationTargetColumn = computed(() => {
  if (selectedOptimization.value) {
    const targetName = selectedOptimization.value.params.targetName;
    return {
      prop: targetName,
      label: targetName,
    };
  }
  return { prop: "", label: "" };
});

const filteredResultTableColumns = computed(() => {
  if (selectedOptimization.value && optimizationTargetColumn.value.prop) {
    return resultTableColumns.value.filter(
      (col) => col.prop !== optimizationTargetColumn.value.prop,
    );
  }
  return resultTableColumns.value;
});

const fetchOptimizationList = async () => {
  try {
    loading.value = true;
    const workspacePath = workspaceStore.getCurrentWorkspacePath || "";
    const workspaceName = workspacePath
      ? workspacePath
          .replace(/[\\/]+$/, "")
          .split(/[\\/]/)
          .pop() || ""
      : "";
    const params = { path: workspaceName };
    const res = await getSearchList(params);
    if (res.code === 200) {
      optimizationList.value = res.data;
    } else {
      ElMessage.error(res.msg || "获取优化列表失败");
    }
  } catch (error) {
    console.error("获取优化列表失败:", error);
    ElMessage.error("获取优化列表失败");
  } finally {
    loading.value = false;
  }
};

const handleAutoRefreshChange = (value: boolean) => {
  if (value) {
    startAutoRefresh();
  } else {
    stopAutoRefresh();
  }
};

const startAutoRefresh = () => {
  if (refreshTimer) clearInterval(refreshTimer);
  refreshTimer = setInterval(fetchOptimizationList, refreshInterval.value);
};

const stopAutoRefresh = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer);
    refreshTimer = null;
  }
};

const handleSearch = () => {
  /* 搜索逻辑已通过计算属性实现 */
};

const handleSortChange = ({ prop, order }: { prop: string; order: string }) => {
  if (prop && order) {
    sortBy.value = prop;
    sortOrder.value = order as "ascending" | "descending";
  } else {
    sortBy.value = "";
    sortOrder.value = null;
  }
};

const clearSort = () => {
  sortBy.value = "";
  sortOrder.value = null;
  if (tableRef.value) tableRef.value.clearSort();
};

const getStatusCount = (status: string) => {
  return optimizationList.value.filter((opt) => opt.status === status).length;
};

const getStatusTagType = (
  status: string,
): "primary" | "success" | "warning" | "info" | "danger" => {
  const statusMap: Record<
    string,
    "primary" | "success" | "warning" | "info" | "danger"
  > = {
    completed: "success",
    running: "warning",
    pending: "info",
    failed: "danger",
  };
  return statusMap[status] || "info";
};

const getStatusLabel = (status: string) => {
  const labelMap: Record<string, string> = {
    completed: "已完成",
    running: "运行中",
    pending: "等待中",
    failed: "失败",
  };
  return labelMap[status] || status;
};

const getProgressPercentage = (progress: string) => {
  if (progress === "completed") return 100;
  if (progress === "running") return 50;
  if (progress === "pending") return 0;
  const num = parseInt(progress);
  return isNaN(num) ? 0 : num;
};

const getProgressStatus = (status: string) => {
  if (status === "completed") return "success";
  if (status === "failed") return "exception";
  return undefined;
};

const formatDate = (dateString: string) => {
  if (!dateString) return "-";
  try {
    return new Date(dateString).toLocaleString("zh-CN", {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
    });
  } catch {
    return dateString;
  }
};

// 获取去重后的模型UIDs
const getUniqueModelUids = (modelUid: string | string[] | undefined) => {
  if (!modelUid) return "-";

  // 如果是字符串，直接返回
  if (typeof modelUid === "string") {
    return modelUid;
  }

  // 如果是数组，进行去重
  if (Array.isArray(modelUid)) {
    const uniqueUids = [...new Set(modelUid)];

    // 如果只有一个UID，直接返回字符串
    if (uniqueUids.length === 1) {
      return uniqueUids[0];
    }

    // 如果有多个UID，用逗号分隔显示
    return uniqueUids.join(", ");
  }

  return "-";
};

// 格式化目标数据
const getFormattedTargets = (
  optimization: Optimization | null,
): FormattedTarget[] => {
  if (!optimization) return [];

  const { targetName, targetValue } = optimization.params;

  // 处理目标名称
  let names: string[] = [];
  if (typeof targetName === "string") {
    try {
      // 尝试解析JSON字符串
      const parsed = JSON.parse(targetName);
      names = Array.isArray(parsed) ? parsed : [targetName];
    } catch {
      names = [targetName];
    }
  } else if (Array.isArray(targetName)) {
    names = targetName;
  }

  // 处理目标值
  let values: any[] = [];
  if (typeof targetValue === "string") {
    try {
      const parsed = JSON.parse(targetValue);
      values = Array.isArray(parsed) ? parsed : [parsed];
    } catch {
      values = [{ value: targetValue, weight: 1, criterion: 0.05 }];
    }
  } else if (Array.isArray(targetValue)) {
    values = targetValue;
  } else if (typeof targetValue === "object" && targetValue !== null) {
    values = [targetValue];
  } else {
    values = [{ value: targetValue, weight: 1, criterion: 0.05 }];
  }

  // 组合数据并去重
  const targetsMap = new Map<string, FormattedTarget>();
  const duplicateNames = new Set<string>();

  // 先找出重复的名称
  const nameCount = new Map<string, number>();
  names.forEach((name) => {
    nameCount.set(name, (nameCount.get(name) || 0) + 1);
  });
  nameCount.forEach((count, name) => {
    if (count > 1) duplicateNames.add(name);
  });

  // 组合并去重
  names.forEach((name, index) => {
    if (index < values.length) {
      const value = values[index];
      const key = `${name}-${value.value}-${value.weight}-${value.criterion}`;

      if (!targetsMap.has(key)) {
        targetsMap.set(key, {
          name,
          value: typeof value === "object" ? value.value : value,
          weight: typeof value === "object" ? value.weight || 1 : 1,
          criterion: typeof value === "object" ? value.criterion || 0.05 : 0.05,
          isDuplicate: duplicateNames.has(name),
        });
      }
    }
  });

  return Array.from(targetsMap.values());
};

const viewOptimizationDetails = async (optimization: Optimization) => {
  selectedOptimization.value = optimization;
  detailDialogVisible.value = true;

  // 重置之前的结果
  optimizationResults.value = [];
  resultTableColumns.value = [];

  if (!optimization.uid) return;

  resultsLoading.value = true;
  try {
    const res = await getSearchInfo(optimization.uid);
    if (res.code === 200 && Array.isArray(res.data)) {
      if (res.data.length > 0) {
        optimizationResults.value = res.data;
        resultTableColumns.value = Object.keys(res.data[0]).map((key) => ({
          prop: key,
          label: key,
        }));
      }
    } else {
      ElMessage.error(res.msg || "获取优化结果失败");
    }
  } catch (error) {
    console.error("获取优化结果失败:", error);
    ElMessage.error("获取优化结果失败");
  } finally {
    resultsLoading.value = false;
  }
};

const handleCloseDetailDialog = () => {
  detailDialogVisible.value = false;
  selectedOptimization.value = null;
  optimizationResults.value = [];
  resultTableColumns.value = [];
  algParamsVisible.value = false;
  basicInfoVisible.value = false;
};

const handleDeleteOptimizations = (uids: string[]) => {
  deleteSearches(uids).then((res: any) => {
    if (res.code === 200) {
      ElMessage.success("删除成功");
      fetchOptimizationList();
    } else {
      ElMessage.error(res.msg || "删除失败");
    }
  });
};

const handleSelectionChange = (selection: Optimization[]) => {
  selectedOptimizations.value = selection;
};

const handleRowClick = (row: Optimization, column: any) => {
  if (column && column.label === "操作") return;
  if (tableRef.value) {
    tableRef.value.toggleRowSelection(
      row,
      !selectedOptimizations.value.some((opt) => opt.uid === row.uid),
    );
  }
};

const handleBatchDelete = () => {
  if (selectedOptimizations.value.length === 0) {
    ElMessage.warning("请先选择要删除的优化任务");
    return;
  }
  const uids = selectedOptimizations.value.map((opt) => opt.uid);
  ElMessageBox.confirm(
    `确定要删除选中的 ${selectedOptimizations.value.length} 个优化任务吗？此操作不可恢复。`,
    "确认删除",
    { confirmButtonText: "确定", cancelButtonText: "取消", type: "warning" },
  )
    .then(() => {
      handleDeleteOptimizations(uids);
      selectedOptimizations.value = [];
      if (tableRef.value) tableRef.value.clearSelection();
    })
    .catch(() => {});
};

const exportOptimizationResults = () => {
  if (optimizationResults.value.length === 0) {
    ElMessage.warning("没有可导出的数据");
    return;
  }

  if (!selectedOptimization.value) return;

  const columnsToExport: { prop: string; label: string }[] = [];
  if (optimizationTargetColumn.value.prop) {
    columnsToExport.push(optimizationTargetColumn.value);
  }
  columnsToExport.push(...filteredResultTableColumns.value);

  const headers = ["序号", ...columnsToExport.map((c) => c.label)];

  const content = optimizationResults.value.map((row, index) => [
    index + 1,
    ...columnsToExport.map((c) => row[c.prop]),
  ]);

  exportSingleSheet(
    { headers, content },
    { suggestedName: `${selectedOptimization.value.name}_优化结果` },
  );
};

onMounted(fetchOptimizationList);
onUnmounted(stopAutoRefresh);
</script>

<style scoped>
.main {
  padding: 25px;
  height: calc(100% - 40px);
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  gap: 20px;
  overflow: hidden;
  background-color: transparent;
}

.card-header {
  font-size: 18px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px;
  background: var(--el-bg-color);
}

.left,
.right {
  display: flex;
  gap: 12px;
  align-items: center;
}

.center {
  display: flex;
  align-items: center;
  gap: 20px;
}

.stats {
  display: flex;
  gap: 20px;
  font-size: 14px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.label {
  font-weight: 500;
  color: var(--el-text-color-secondary);
}

.value {
  font-weight: 600;
}

.value.success {
  color: var(--el-color-success);
}

.value.warning {
  color: var(--el-color-warning);
}

.value.info {
  color: var(--el-color-info);
}

.value.danger {
  color: var(--el-color-danger);
}

.content-card {
  background-color: var(--el-bg-color, #ffffff);
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0px 0px 12px rgba(0, 0, 0, 0.05);
}

.table-container {
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;
}

:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.el-table__row:hover) {
  background-color: var(--el-fill-color-light);
}

:deep(.el-table th) {
  font-weight: 600;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 详情对话框样式 */
.detail-dialog {
  :deep(.el-dialog__header) {
    background: linear-gradient(
      135deg,
      var(--el-color-primary-light-9) 0%,
      var(--el-color-primary-light-8) 100%
    );
    border-radius: 8px 8px 0 0;
    padding: 20px 24px;
  }

  :deep(.el-dialog__title) {
    font-size: 18px;
    font-weight: 600;
    color: var(--el-color-primary-dark-2);
  }

  :deep(.el-dialog__body) {
    padding: 24px;
  }

  :deep(.el-dialog__footer) {
    padding: 16px 24px;
    border-top: 1px solid var(--el-border-color-lighter);
    background: var(--el-fill-color-extra-light);
  }

  .targets-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 12px;
    margin-top: 8px;
  }

  .target-card {
    background: linear-gradient(
      135deg,
      var(--el-fill-color-extra-light) 0%,
      var(--el-fill-color-lighter) 100%
    );
    border: 1px solid var(--el-border-color-lighter);
    border-radius: 8px;
    padding: 12px;
    transition: all 0.3s ease;
  }

  .target-card:hover {
    border-color: var(--el-color-primary-light-5);
    box-shadow: 0 2px 8px rgba(64, 158, 255, 0.15);
    transform: translateY(-2px);
  }

  .target-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
  }

  .target-index {
    font-size: 12px;
    font-weight: 600;
    color: var(--el-color-primary);
    background: var(--el-color-primary-light-9);
    padding: 2px 8px;
    border-radius: 12px;
  }

  .target-content {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .target-name {
    font-size: 14px;
    font-weight: 600;
    color: var(--el-text-color-primary);
    padding: 8px;
    background: var(--el-bg-color);
    border-radius: 6px;
    border-left: 3px solid var(--el-color-primary);
  }

  .target-details {
    display: flex;
    flex-direction: column;
    gap: 6px;
  }

  .target-detail-item {
    display: flex;
    align-items: center;
    font-size: 13px;
  }

  .detail-label-sm {
    color: var(--el-text-color-secondary);
    font-weight: 500;
    min-width: 50px;
  }

  .detail-value-sm {
    color: var(--el-text-color-primary);
    font-weight: 600;
    margin-left: 4px;
  }

  .no-targets {
    text-align: center;
    color: var(--el-text-color-secondary);
    padding: 20px;
    font-size: 14px;
  }
}

.detail-content {
  .info-card {
    background: linear-gradient(
      135deg,
      var(--el-bg-color) 0%,
      var(--el-fill-color-extra-light) 100%
    );
    border: 1px solid var(--el-border-color-light);
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 24px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
  }

  .header {
    display: flex;
    align-items: center;
    gap: 16px;
    margin-bottom: 20px;
  }

  .title {
    flex: 1;
  }

  h3 {
    margin: 0 0 8px 0;
    font-size: 20px;
    font-weight: 600;
    color: var(--el-text-color-primary);
  }

  .subtitle {
    margin: 0;
    font-size: 14px;
    color: var(--el-text-color-secondary);
    font-weight: 500;
  }

  .status .el-tag {
    font-weight: 600;
    padding: 8px 16px;
    border-radius: 20px;
  }

  .progress-section {
    .progress-info {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;
    }

    .progress-label {
      font-size: 14px;
      font-weight: 500;
      color: var(--el-text-color-regular);
    }

    .progress-value {
      font-size: 16px;
      font-weight: 600;
      color: var(--el-color-primary);
    }

    .custom-progress :deep(.el-progress-bar__outer) {
      border-radius: 8px;
      background: var(--el-fill-color-light);
    }

    .custom-progress :deep(.el-progress-bar__inner) {
      border-radius: 8px;
    }
  }

  .details-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
    margin-top: 12px;
    margin-bottom: 24px;
  }

  .detail-item {
    background: var(--el-bg-color);
    border: 1px solid var(--el-border-color-lighter);
    border-radius: 8px;
    padding: 16px;
    transition: all 0.2s ease;
  }

  .detail-item:hover {
    border-color: var(--el-color-primary-light-7);
    box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
    transform: translateY(-1px);
  }

  .detail-label {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
    font-size: 13px;
    font-weight: 500;
    color: var(--el-text-color-secondary);
  }

  .detail-label .el-icon {
    color: var(--el-color-primary);
  }

  .detail-value {
    font-size: 14px;
    font-weight: 500;
    color: var(--el-text-color-primary);
    word-break: break-all;
  }

  .detail-value.code-text {
    font-family: "Monaco", "Menlo", "Ubuntu Mono", monospace;
    background: var(--el-fill-color-light);
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
  }

  .param-section {
    background: var(--el-bg-color);
    border: 1px solid var(--el-border-color-lighter);
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 24px;
  }

  .param-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    font-weight: 600;
    color: var(--el-text-color-secondary);
  }
  .param-header .el-icon {
    color: var(--el-color-primary);
  }

  .param-header-left {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .header-arrow {
    transition: transform 0.3s ease;
  }

  .header-arrow.is-active {
    transform: rotate(90deg);
  }

  .param-header-left .el-icon {
    color: var(--el-color-primary);
  }

  .param-content {
    margin-top: 12px;
  }

  .param-content pre {
    background-color: var(--el-fill-color-light);
    padding: 12px;
    border-radius: 6px;
    white-space: pre-wrap;
    word-break: break-all;
    font-family: "Monaco", "Menlo", "Ubuntu Mono", monospace;
    font-size: 13px;
  }

  .error-section {
    background: linear-gradient(135deg, #fef0f0 0%, #fde2e2 100%);
    border: 1px solid #fecaca;
    border-radius: 8px;
    padding: 16px;
  }

  .error-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;
    font-size: 14px;
    font-weight: 600;
    color: #dc2626;
  }

  .error-content .el-tag {
    border-radius: 6px;
    font-weight: 500;
  }
}
</style>
