{"name": "RandomGeneration", "displayName": "随机生成法", "description": "随机生成法", "defaultParams": {"sampleCount": {"value": 100, "type": "number", "optimizeParam": true, "description": "样本数量", "displayName": "样本数量", "min": 1, "max": 10000, "step": 10, "nullable": true}, "randomSeed": {"value": 1, "type": "number", "optimizeParam": true, "description": "随机种子", "displayName": "随机种子", "min": 1, "max": 1000, "step": 1, "nullable": true}}, "tips": ["随机生成法"], "introduction": {"detailedDescription": "随机生成法", "usageTips": ["随机生成法"], "scenarios": "随机生成法", "mainParams": [{"name": "sample_count", "description": "样本数量"}, {"name": "random_seed", "description": "随机种子"}]}}