import {
  Menu,
  type MenuItemConstructorOptions,
  type MenuItem,
  app,
  BrowserWindow,
  dialog,
} from "electron";
import { existsSync, readFileSync } from "node:fs";
import path from "path";

/**
 * 菜单管理器 - 负责创建和管理应用菜单
 */
export class MenuManager {
  private isDev: boolean;

  constructor(isDev: boolean = false) {
    this.isDev = isDev;
  }

  /**
   * 创建应用菜单
   */
  createMenu(label = "进入全屏幕") {
    const menu = Menu.buildFromTemplate(
      this.buildAppMenu(label) as (MenuItemConstructorOptions | MenuItem)[],
    );
    Menu.setApplicationMenu(menu);
  }

  /**
   * 构建应用菜单模板
   */
  private buildAppMenu(
    fullscreenLabel: string,
  ): Array<MenuItemConstructorOptions | MenuItem> {
    const devMenuItems: MenuItemConstructorOptions[] = [];
    if (this.isDev) {
      devMenuItems.push(
        { label: "开发者工具", role: "toggleDevTools" },
        { label: "强制刷新", role: "forceReload" },
      );
    }

    const template: Array<MenuItemConstructorOptions | MenuItem> = [
      {
        label: "文件",
        submenu: [
          {
            label: "打开文件夹...",
            accelerator: "CmdOrCtrl+Shift+O",
            toolTip: "打开现有项目文件夹",
            click: async () => {
              const win = BrowserWindow.getFocusedWindow();
              if (win && !win.isDestroyed()) {
                win.focus();
                win.webContents.send("menu-triggered-import-project");
              }
            },
          },
          {
            label: "打开文件...",
            accelerator: "CmdOrCtrl+O",
            toolTip: "打开单个文件进行编辑",
            click: async () => {
              const win = BrowserWindow.getFocusedWindow();
              if (win && !win.isDestroyed()) {
                win.focus();
                const currentURL = win.webContents.getURL();
                const isDataImportPage = currentURL.includes(
                  "/dataManagement/imandex",
                );

                if (isDataImportPage) {
                  win.webContents.send("menu-triggered-open-file");
                } else {
                  // Handle file opening logic here
                  win.webContents.send("menu-triggered-open-file");
                }
              }
            },
          },
          { type: "separator" },
          {
            label: "项目管理",
            submenu: [
              {
                label: "导出项目",
                accelerator: "CmdOrCtrl+Shift+E",
                toolTip: "导出当前项目状态，包括标签页、工作区等信息",
                click: async () => {
                  const win = BrowserWindow.getFocusedWindow();
                  if (win && !win.isDestroyed()) {
                    win.focus();
                    win.webContents.send("menu-triggered-export-project");
                  }
                },
              },
              {
                label: "导入项目",
                accelerator: "CmdOrCtrl+Shift+I",
                toolTip: "导入项目状态，恢复之前的工作环境",
                click: async () => {
                  const win = BrowserWindow.getFocusedWindow();
                  if (win && !win.isDestroyed()) {
                    win.focus();
                    win.webContents.send("menu-triggered-import-project-state");
                  }
                },
              },
            ],
          },
          { type: "separator" },
          {
            label: "退出",
            role: "quit",
            accelerator: "CmdOrCtrl+Q",
          },
        ],
      },
      {
        label: "编辑",
        submenu: [
          {
            label: "撤销",
            role: "undo",
            accelerator: "CmdOrCtrl+Z",
          },
          {
            label: "重做",
            role: "redo",
            accelerator: "CmdOrCtrl+Shift+Z",
          },
          { type: "separator" },
          {
            label: "剪切",
            role: "cut",
            accelerator: "CmdOrCtrl+X",
          },
          {
            label: "复制",
            role: "copy",
            accelerator: "CmdOrCtrl+C",
          },
          {
            label: "粘贴",
            role: "paste",
            accelerator: "CmdOrCtrl+V",
          },
          {
            label: "删除",
            role: "delete",
            accelerator: "Delete",
          },
          {
            label: "全选",
            role: "selectAll",
            accelerator: "CmdOrCtrl+A",
          },
        ],
      },
      {
        label: "显示",
        submenu: [
          {
            label: "放大",
            role: "zoomIn",
            accelerator: "CmdOrCtrl+Plus",
          },
          {
            label: "默认大小",
            role: "resetZoom",
            accelerator: "CmdOrCtrl+0",
          },
          {
            label: "缩小",
            role: "zoomOut",
            accelerator: "CmdOrCtrl+-",
          },
          { type: "separator" },
          {
            label: fullscreenLabel,
            role: "togglefullscreen",
            accelerator: "F11",
          },
        ],
      },
      ...(this.isDev
        ? [
            {
              label: "开发",
              submenu: devMenuItems,
            },
          ]
        : []),
      {
        label: "关于",
        submenu: [
          {
            label: "关于应用",
            role: "about" as const,
            accelerator: "F1",
          },
        ],
      },
    ];

    return template;
  }

  /**
   * 创建上下文菜单 - 文件菜单
   */
  createFileMenu(win: BrowserWindow) {
    const fileMenu = Menu.buildFromTemplate([
      {
        label: "打开文件夹...",
        click: () => {
          win?.webContents.send("menu-triggered-import-project");
        },
      },
      {
        label: "打开文件...",
        click: () => {
          win?.webContents.send("menu-triggered-open-file");
        },
      },
      { type: "separator" },
      {
        label: "退出",
        click: () => app.quit(),
      },
    ]);

    fileMenu.popup({ window: win });
  }

  /**
   * 创建上下文菜单 - 项目菜单
   */
  createProjectMenu(win: BrowserWindow) {
    const projectMenu = Menu.buildFromTemplate([
      {
        label: "导入项目",
        click: () => {
          win?.webContents.send("menu-triggered-import-project-state");
        },
      },
      {
        label: "导出项目",
        click: () => {
          win?.webContents.send("menu-triggered-export-project");
        },
      },
    ]);

    projectMenu.popup({ window: win });
  }

  /**
   * 创建上下文菜单 - 编辑菜单
   */
  createEditMenu(win: BrowserWindow) {
    const editMenu = Menu.buildFromTemplate([
      { label: "撤销", role: "undo" },
      { label: "重做", role: "redo" },
      { type: "separator" },
      { label: "剪切", role: "cut" },
      { label: "复制", role: "copy" },
      { label: "粘贴", role: "paste" },
      { type: "separator" },
      { label: "全选", role: "selectAll" },
    ]);

    editMenu.popup({ window: win });
  }

  /**
   * 创建上下文菜单 - 视图菜单
   */
  createViewMenu(win: BrowserWindow) {
    const viewMenu = Menu.buildFromTemplate([
      { label: "放大", role: "zoomIn" },
      { label: "默认大小", role: "resetZoom" },
      { label: "缩小", role: "zoomOut" },
      { type: "separator" },
      {
        label: win?.isFullScreen() ? "退出全屏" : "进入全屏",
        role: "togglefullscreen",
      },
    ]);

    viewMenu.popup({ window: win });
  }

  /**
   * 创建上下文菜单 - 开发菜单
   */
  createDevMenu(win: BrowserWindow) {
    if (!this.isDev) return;

    const devMenu = Menu.buildFromTemplate([
      { label: "开发者工具", role: "toggleDevTools" },
      { label: "强制刷新", role: "forceReload" },
    ]);

    devMenu.popup({ window: win });
  }

  /**
   * 创建上下文菜单 - 关于菜单
   */
  createAboutMenu(win: BrowserWindow) {
    const aboutMenu = Menu.buildFromTemplate([
      {
        label: "关于应用",
        click: () => {
          let version = "未知版本";
          try {
            const packagePath = path.join(app.getAppPath(), "package.json");
            if (existsSync(packagePath)) {
              const packageData = JSON.parse(readFileSync(packagePath, "utf8"));
              version = packageData.version || "未知版本";
            }
          } catch (error) {
            console.error("读取版本号失败:", error);
          }
          dialog.showMessageBox(win!, {
            title: "关于应用",
            message: "ML Desktop",
            detail: `版本 ${version}\n一个机器学习数据处理和建模的桌面应用。`,
            buttons: ["确定"],
            type: "info",
          });
        },
      },
    ]);

    aboutMenu.popup({ window: win });
  }
}
