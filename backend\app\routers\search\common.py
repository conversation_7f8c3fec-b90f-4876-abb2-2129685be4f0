from flask import current_app, send_file, make_response, jsonify, request, Blueprint
from app.extensions.socketio import socketio
from app.utils.params import camel_to_snake, snake_to_camel
from app.services.ProgressService import ProgressService
from app.types.flaskapp import FlaskWithExecutor
from app.services.DBService import DBService
import logging
from typing import cast

logger = logging.getLogger("socketio")

search_common_bp = Blueprint("search_common", __name__)

@socketio.on('get_search_info')
def get_search_info(data: dict):
    data = camel_to_snake(data) # type: ignore
    uid: str = data["uid"]
    print(uid)
    try:
        if uid is not None:
            ProgressService.report_search(uid)
            return_data = {
                "uid": uid,
            }
            return {"code": 200, "msg": "success", "data": snake_to_camel(return_data)}
        else:
            return {"code": 400, "msg": "uid is None", "data": None}
    except Exception as e:
        logger.error(f"handle_search_info: 搜索信息获取失败: {e}")
        return {"code": 500, "msg": f"搜索信息获取失败: {e}", "data": None}

@search_common_bp.route("/get_search_list", methods=["GET"])
def get_search_list():
    search_list = DBService.get_search_list()
    compact_search_list = []
    try:
        for search in search_list:
            tmp_dict = search.to_dict()
            tmp_dict.pop("result")
            compact_search_list.append(tmp_dict)
        return make_response(jsonify({"code": 200, "msg": "success", "data": snake_to_camel(compact_search_list)}), 200)
    except Exception as e:
        logger.error(f"get_search_list: 搜索列表获取失败: {e}")
        return make_response(jsonify({"code": 500, "msg": f"搜索列表获取失败: {e}", "data": None}), 500)

@search_common_bp.route("/get_search_info", methods=["POST"])
def get_search_info():
    data: dict = camel_to_snake(request.json) # type: ignore
    uid: str | None = data.get("uid", None)
    try:
        if uid is not None:
            search_info = DBService.get_search_info(uid)
            return make_response(jsonify({"code": 200, "msg": "success", "data": snake_to_camel(search_info.result)}), 200)
        else:
            return make_response(jsonify({"code": 400, "msg": "uid is None", "data": None}), 400)
    except Exception as e:
        logger.error(f"get_search_info: 搜索信息获取失败: {e}")
        return make_response(jsonify({"code": 500, "msg": f"搜索信息获取失败: {e}", "data": None}), 500)

@search_common_bp.route("/delete_searches", methods=["POST"])
def delete_searches():
    data = request.json
    uids = data.get("uids")
    DBService.delete_searches(uids)
    return make_response(jsonify({"code": 200, "msg": "success", "data": None}), 200)