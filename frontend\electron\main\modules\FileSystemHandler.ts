import fs from "fs";
import path from "path";
import { app, dialog, type B<PERSON>er<PERSON>indow } from "electron";
import mammoth from "mammoth";

// 文件信息接口
export interface FileInfo {
  path: string;
  name: string;
  extension: string;
  type: string;
  exists: boolean;
}

/**
 * 文件系统处理器 - 负责所有文件系统相关操作
 */
export class FileSystemHandler {
  private static instance: FileSystemHandler;

  static getInstance(): FileSystemHandler {
    if (!FileSystemHandler.instance) {
      FileSystemHandler.instance = new FileSystemHandler();
    }
    return FileSystemHandler.instance;
  }

  // 文件类型定义
  private readonly fileTypes = {
    excel: [".xlsx", ".xls", ".csv", ".xlsm", ".xlsb"],
    markdown: [".md", ".markdown"],
    text: [".txt", ".log"],
    code: [".ts", ".js", ".py", ".json", ".html", ".css"],
  };

  // 文件过滤器
  readonly filters = {
    excel: [
      {
        name: "Excel文件",
        extensions: ["xlsx", "xls", "csv", "xlsm", "xlsb"],
      },
    ],
    markdown: [
      {
        name: "Markdown文件",
        extensions: ["md", "markdown"],
      },
    ],
    text: [
      {
        name: "文本文件",
        extensions: ["txt", "log"],
      },
    ],
    code: [
      {
        name: "代码文件",
        extensions: ["ts", "js", "py", "json", "html", "css"],
      },
    ],
    all: [
      {
        name: "所有文件",
        extensions: ["*"],
      },
    ],
  };

  /**
   * 检测文件类型
   */
  private getFileType(filePath: string): {
    type: string;
    category: string;
    supported: boolean;
  } {
    const ext = path.extname(filePath).toLowerCase();

    const textExtensions = [
      ".txt",
      ".md",
      ".json",
      ".xml",
      ".html",
      ".css",
      ".js",
      ".ts",
      ".vue",
      ".py",
      ".java",
      ".cpp",
      ".c",
      ".h",
      ".sql",
      ".log",
      ".ini",
      ".cfg",
      ".conf",
      ".yaml",
      ".yml",
    ];

    const officeExtensions = [
      ".doc",
      ".docx",
      ".xls",
      ".xlsx",
      ".ppt",
      ".pptx",
    ];
    const pdfExtensions = [".pdf"];
    const imageExtensions = [
      ".jpg",
      ".jpeg",
      ".png",
      ".gif",
      ".bmp",
      ".svg",
      ".ico",
    ];
    const archiveExtensions = [".zip", ".rar", ".7z", ".tar", ".gz"];
    const executableExtensions = [
      ".exe",
      ".msi",
      ".dmg",
      ".app",
      ".deb",
      ".rpm",
    ];
    const mediaExtensions = [".mp3", ".mp4", ".avi", ".mov", ".wav", ".flac"];

    if (textExtensions.includes(ext)) {
      return { type: ext, category: "text", supported: true };
    } else if (officeExtensions.includes(ext)) {
      return { type: ext, category: "office", supported: true };
    } else if (pdfExtensions.includes(ext)) {
      return { type: ext, category: "pdf", supported: true };
    } else if (imageExtensions.includes(ext)) {
      return { type: ext, category: "image", supported: true };
    } else if (archiveExtensions.includes(ext)) {
      return { type: ext, category: "archive", supported: false };
    } else if (executableExtensions.includes(ext)) {
      return { type: ext, category: "executable", supported: false };
    } else if (mediaExtensions.includes(ext)) {
      return { type: ext, category: "media", supported: false };
    } else {
      return { type: ext, category: "unknown", supported: false };
    }
  }

  /**
   * 获取图像文件的 MIME 类型
   */
  private getMimeType(extension: string): string {
    const mimeTypes: Record<string, string> = {
      ".jpg": "image/jpeg",
      ".jpeg": "image/jpeg",
      ".png": "image/png",
      ".gif": "image/gif",
      ".bmp": "image/bmp",
      ".svg": "image/svg+xml",
      ".ico": "image/x-icon",
      ".webp": "image/webp",
    };
    return mimeTypes[extension.toLowerCase()] || "image/jpeg";
  }

  /**
   * 获取不支持文件类型的提示信息
   */
  private getUnsupportedMessage(fileInfo: { type: string; category: string }) {
    switch (fileInfo.category) {
      case "image":
        return "图片文件不支持文本编辑，请使用图片查看器打开";
      case "archive":
        return "压缩文件不支持直接编辑，请先解压缩";
      case "executable":
        return "可执行文件不支持编辑";
      case "media":
        return "音视频文件不支持文本编辑，请使用媒体播放器打开";
      default:
        return "不支持的文件类型，无法在文本编辑器中打开";
    }
  }

  /**
   * 提取 DOCX 文档的文本内容
   */
  private async extractDocxText(filePath: string): Promise<string> {
    try {
      try {
        const result = await mammoth.extractRawText({ path: filePath });
        return result.value || "无法提取文档内容";
      } catch {
        console.log("Mammoth not available");
        return `Word 文档预览\n\n文件路径: ${filePath}\n\n注意：无法提取文档内容，请使用 Microsoft Word 或其他兼容软件打开此文件。\n\n要完整支持 Word 文档，请安装 mammoth 库：npm install mammoth`;
      }
    } catch (error: any) {
      console.error("Error extracting DOCX text:", error);
      return `Word 文档预览\n\n文件路径: ${filePath}\n\n错误：无法读取文档内容 - ${error?.message || "Unknown error"}`;
    }
  }

  /**
   * 读取目录内容
   */
  async readDirectory(dirPath: string) {
    const files = await fs.promises.readdir(dirPath, { withFileTypes: true });
    return files.map((dirent) => ({
      name: dirent.name,
      isDirectory: dirent.isDirectory(),
      path: path.join(dirPath, dirent.name),
    }));
  }

  /**
   * 创建新目录
   */
  async createDirectory(targetPath: string) {
    await fs.promises.mkdir(targetPath, { recursive: true });
    return { success: true };
  }

  /**
   * 创建新文件
   */
  async createFile(filePath: string) {
    await fs.promises.writeFile(filePath, "");
    return { success: true };
  }

  /**
   * 删除文件/目录
   */
  async deletePath(targetPath: string) {
    const stats = await fs.promises.stat(targetPath);
    if (stats.isDirectory()) {
      await fs.promises.rmdir(targetPath, { recursive: true });
    } else {
      await fs.promises.unlink(targetPath);
    }
    return { success: true };
  }

  /**
   * 重命名文件/目录
   */
  async rename(oldPath: string, newPath: string) {
    try {
      await fs.promises.rename(oldPath, newPath);
      return { success: true };
    } catch (error) {
      console.error("Error renaming file/directory:", error);
      throw error;
    }
  }

  /**
   * 检查文件/目录是否存在
   */
  async access(filePath: string) {
    try {
      await fs.promises.access(filePath);
      return { exists: true };
    } catch {
      return { exists: false };
    }
  }

  /**
   * 读取文件内容
   */
  async readFile(filePath: string) {
    try {
      const content = await fs.promises.readFile(filePath, "utf-8");
      return content;
    } catch (error) {
      console.error("Error reading file:", error);
      throw error;
    }
  }

  /**
   * 检测文件类型和读取内容
   */
  async readFileWithType(filePath: string) {
    try {
      const fileInfo = this.getFileType(filePath);

      if (!fileInfo.supported) {
        return {
          success: false,
          fileInfo,
          error: `不支持的文件类型: ${fileInfo.type}`,
          message: this.getUnsupportedMessage(fileInfo),
        };
      }

      let content = "";
      let imageData = null;
      let rawData: Buffer | null = null;

      if (fileInfo.category === "text") {
        content = await fs.promises.readFile(filePath, "utf-8");
      } else if (fileInfo.category === "office") {
        if (fileInfo.type === ".docx") {
          content = await this.extractDocxText(filePath);
        } else if (fileInfo.type === ".doc") {
          content = "暂不支持 .doc 格式，请转换为 .docx 格式";
        } else {
          content = `不支持的Office 文档 (${fileInfo.type})，请使用Office工具进行编辑`;
        }
      } else if (fileInfo.category === "pdf") {
        rawData = await fs.promises.readFile(filePath);
        content = "";
      } else if (fileInfo.category === "image") {
        const imageBuffer = await fs.promises.readFile(filePath);
        const base64Data = imageBuffer.toString("base64");
        const mimeType = this.getMimeType(fileInfo.type);
        imageData = `data:${mimeType};base64,${base64Data}`;
        content = "";
      }

      return {
        success: true,
        fileInfo,
        content,
        imageData,
        rawData,
      };
    } catch (error: any) {
      console.error("Error reading file with type:", error);
      return {
        success: false,
        fileInfo: this.getFileType(filePath),
        error: error?.message || "Unknown error",
        message: "读取文件时发生错误",
      };
    }
  }

  /**
   * 读取文件内容为Buffer
   */
  async readFileBuffer(filePath: string) {
    try {
      const buffer = await fs.promises.readFile(filePath);
      return buffer;
    } catch (error) {
      console.error("Error reading file buffer:", error);
      throw error;
    }
  }

  /**
   * 写入文件内容
   */
  async writeFile(filePath: string, content: string) {
    try {
      await fs.promises.writeFile(filePath, content, "utf-8");
      return { success: true };
    } catch (error) {
      console.error("Error writing file:", error);
      throw error;
    }
  }

  /**
   * 写入文件内容 (Buffer)
   */
  async writeFileBuffer(filePath: string, arrayBuffer: ArrayBuffer) {
    try {
      const buffer = Buffer.from(arrayBuffer);
      await fs.promises.writeFile(filePath, buffer);
      return { success: true };
    } catch (error) {
      console.error("Error writing file buffer:", error);
      throw error;
    }
  }

  /**
   * 写入文件内容 (Base64Buffer)
   */
  async writeFileBase64Buffer(filePath: string, arrayBuffer: string) {
    try {
      const buffer = Buffer.from(arrayBuffer, "base64");
      await fs.promises.writeFile(filePath, buffer);
      return { success: true };
    } catch (error) {
      console.error("Error writing file buffer:", error);
      throw error;
    }
  }

  /**
   * 验证文件路径是否存在
   */
  async validatePath(filePath: string) {
    try {
      const exists = await fs.promises
        .access(filePath)
        .then(() => true)
        .catch(() => false);
      return { exists, path: filePath };
    } catch (error) {
      return { exists: false, path: filePath, error: error };
    }
  }

  /**
   * 获取最近文件路径
   */
  private getRecentFilesPath() {
    return path.join(app.getPath("userData"), "recent-files.json");
  }

  /**
   * 获取最近文件
   */
  async getRecentFiles() {
    try {
      const recentFilesPath = this.getRecentFilesPath();
      const exists = await fs.promises
        .access(recentFilesPath)
        .then(() => true)
        .catch(() => false);

      if (!exists) {
        return { success: true, files: [] };
      }

      const fileContent = await fs.promises.readFile(recentFilesPath, "utf-8");
      const files = JSON.parse(fileContent);

      return { success: true, files: Array.isArray(files) ? files : [] };
    } catch (error) {
      console.error("获取最近文件失败:", error);
      return { success: false, files: [] };
    }
  }

  /**
   * 保存最近文件
   */
  async saveRecentFiles(files: any[]) {
    try {
      const recentFilesPath = this.getRecentFilesPath();
      await fs.promises.writeFile(
        recentFilesPath,
        JSON.stringify(files, null, 2),
        "utf-8",
      );
      return { success: true };
    } catch (error) {
      console.error("保存最近文件失败:", error);
      return { success: false, message: "保存失败" };
    }
  }

  /**
   * 创建文件信息对象
   */
  private createFileInfo(filePath: string): FileInfo {
    return {
      path: filePath,
      name: path.basename(filePath),
      extension: path.extname(filePath).toLowerCase(),
      type: this.getFileTypeCategory(filePath),
      exists: fs.existsSync(filePath),
    };
  }

  /**
   * 获取文件类型分类
   */
  private getFileTypeCategory(filePath: string): string {
    const ext = path.extname(filePath).toLowerCase();
    for (const [type, extensions] of Object.entries(this.fileTypes)) {
      if (extensions.includes(ext)) {
        return type;
      }
    }
    return "other";
  }

  /**
   * 打开文件对话框
   */
  async openFile(
    window: BrowserWindow,
    options: {
      filters?: { name: string; extensions: string[] }[];
      isDataImport?: boolean;
    } = {},
  ): Promise<FileInfo | null> {
    try {
      const { canceled, filePaths } = await dialog.showOpenDialog(window, {
        properties: ["openFile"],
        filters: options.filters || (this.filters.all as any),
      });

      if (!canceled && filePaths.length > 0) {
        const fileInfo = this.createFileInfo(filePaths[0]);

        if (
          options.isDataImport &&
          !this.fileTypes.excel.includes(fileInfo.extension as any)
        ) {
          await dialog.showMessageBox(window, {
            type: "error",
            title: "文件类型错误",
            message: "请选择Excel文件",
          });
          return null;
        }

        // 发送统一的文件选择事件
        window.webContents.send("file-selected", fileInfo);
        return fileInfo;
      }
    } catch (error) {
      console.error("Error opening file:", error);
      dialog.showErrorBox("错误", "打开文件时发生错误");
    }
    return null;
  }

  /**
   * 导入项目
   */
  async importProject(
    window: BrowserWindow,
  ): Promise<{ path: string; name: string } | null> {
    try {
      const { canceled, filePaths } = await dialog.showOpenDialog(window, {
        properties: ["openDirectory"],
        title: "选择项目文件夹",
      });

      if (!canceled && filePaths.length > 0) {
        const projectPath = filePaths[0];
        const projectInfo = {
          path: projectPath,
          name: path.basename(projectPath),
        };
        window.webContents.send("project-imported", projectInfo);
        return projectInfo;
      }
    } catch (error) {
      console.error("Error importing project:", error);
      dialog.showErrorBox("错误", "导入项目时发生错误");
    }
    return null;
  }

  /**
   * 处理文件菜单的打开文件操作
   */
  async handleOpenFile(win: BrowserWindow) {
    if (!win || win.isDestroyed()) return;

    // 获取当前窗口URL
    const currentURL = win.webContents.getURL();
    const isDataImportPage = currentURL.includes("/dataManagement/imandex");

    // 根据当前页面类型选择合适的过滤器
    await this.openFile(win, {
      filters: isDataImportPage
        ? (this.filters.excel as any)
        : (this.filters.all as any),
      isDataImport: isDataImportPage,
    });
  }

  /**
   * 处理导入项目操作
   */
  async handleImportProject(win: BrowserWindow) {
    if (!win || win.isDestroyed()) return;
    await this.importProject(win);
  }
  /**
   * 获取目录大小信息
   */
  async getDirectorySize(dirPath: string): Promise<{
    size: number;
    fileCount: number;
  }> {
    try {
      let totalSize = 0;
      let fileCount = 0;

      const calculateSize = async (currentPath: string) => {
        const stats = await fs.promises.stat(currentPath);

        if (stats.isFile()) {
          totalSize += stats.size;
          fileCount++;
        } else if (stats.isDirectory()) {
          const entries = await fs.promises.readdir(currentPath);
          for (const entry of entries) {
            const entryPath = path.join(currentPath, entry);
            await calculateSize(entryPath);
          }
        }
      };

      await calculateSize(dirPath);
      return { size: totalSize, fileCount };
    } catch (error) {
      console.error("Error calculating directory size:", error);
      return { size: 0, fileCount: 0 };
    }
  }

  /**
   * 递归读取目录结构
   */
  async readDirectoryRecursive(dirPath: string): Promise<{
    files: Array<{
      relativePath: string;
      fullPath: string;
      isFile: boolean;
    }>;
  }> {
    try {
      const files: Array<{
        relativePath: string;
        fullPath: string;
        isFile: boolean;
      }> = [];

      const readRecursive = async (currentPath: string, relativePath = "") => {
        const entries = await fs.promises.readdir(currentPath, {
          withFileTypes: true,
        });

        for (const entry of entries) {
          const fullPath = path.join(currentPath, entry.name);
          const relPath = relativePath
            ? path.join(relativePath, entry.name)
            : entry.name;

          if (entry.isFile()) {
            files.push({
              relativePath: relPath,
              fullPath: fullPath,
              isFile: true,
            });
          } else if (entry.isDirectory()) {
            files.push({
              relativePath: relPath,
              fullPath: fullPath,
              isFile: false,
            });
            await readRecursive(fullPath, relPath);
          }
        }
      };

      await readRecursive(dirPath);
      return { files };
    } catch (error) {
      console.error("Error reading directory recursively:", error);
      return { files: [] };
    }
  }

  /**
   * 检查路径是否存在
   */
  async pathExists(filePath: string): Promise<boolean> {
    try {
      await fs.promises.access(filePath);
      return true;
    } catch {
      return false;
    }
  }
}
