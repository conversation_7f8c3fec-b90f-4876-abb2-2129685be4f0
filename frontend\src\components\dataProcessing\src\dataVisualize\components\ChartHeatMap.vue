<template>
  <div class="chart-container" ref="containerRef">
    <div class="chart-content" ref="chartRef"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, watch, nextTick } from "vue";
import * as echarts from "echarts";
import type { ECharts } from "echarts";

const props = defineProps<{
  chartData: any[][];
  columns: string[];
  selectedColumns: string[];
}>();

const containerRef = ref<HTMLElement | null>(null);
const chartRef = ref<HTMLElement | null>(null);
let chartInstance: ECharts | null = null;

// 导出函数，供父组件调用
const getChartInstance = () => chartInstance;
const getChartOptions = () => createHeatmapOption();
const getChartData = () => {
  // 使用所有列生成热力图数据
  const selectedIndices = Array.from({ length: props.columns.length }, (_, i) => i);
  
  if (selectedIndices.length < 2) return null;
  
  // 生成相关性矩阵数据
  const data = [];
  
  for (let i = 0; i < selectedIndices.length; i++) {
    for (let j = 0; j < selectedIndices.length; j++) {
      const xIndex = selectedIndices[i];
      const yIndex = selectedIndices[j];
      
      // 计算两列之间的相关程度
      let correlationValue = 0;
      
      // 尝试计算简单的共现频率
      const xValues = props.chartData.map(row => row[xIndex]);
      const yValues = props.chartData.map(row => row[yIndex]);
      
      let matchCount = 0;
      for (let k = 0; k < xValues.length; k++) {
        if (xValues[k] === yValues[k]) matchCount++;
      }
      
      correlationValue = matchCount / xValues.length;
      
      // 如果是同一列，相关性为1
      if (i === j) correlationValue = 1;
      
      data.push([props.columns[xIndex], props.columns[yIndex], correlationValue]);
    }
  }
  
  return {
    columns: ['列1', '列2', '相关度'],
    data: data
  };
};
defineExpose({ getChartInstance, getChartOptions, getChartData });

// 创建热力图
const createHeatmapOption = () => {
  // 使用所有列生成热力图
  const selectedIndices = Array.from({ length: props.columns.length }, (_, i) => i);
  
  if (selectedIndices.length < 2) return {};
  
  // 计算热力图需要的网格数据
  const data = [];
  
  // 生成相关性矩阵
  for (let i = 0; i < selectedIndices.length; i++) {
    for (let j = 0; j < selectedIndices.length; j++) {
      const xIndex = selectedIndices[i];
      const yIndex = selectedIndices[j];
      
      // 计算两列之间的相关程度
      let correlationValue = 0;
      
      // 尝试计算简单的共现频率
      const xValues = props.chartData.map(row => row[xIndex]);
      const yValues = props.chartData.map(row => row[yIndex]);
      
      let matchCount = 0;
      for (let k = 0; k < xValues.length; k++) {
        if (xValues[k] === yValues[k]) matchCount++;
      }
      
      correlationValue = matchCount / xValues.length;
      
      // 如果是同一列，相关性为1
      if (i === j) correlationValue = 1;
      
      data.push([i, j, correlationValue]);
    }
  }
  
  // 处理X轴和Y轴标签，避免过长
  const axisLabels = selectedIndices.map(idx => {
    const label = props.columns[idx];
    if (typeof label === 'string' && label.length > 10) {
      return label.substring(0, 10) + '...';
    }
    return label;
  });
  
  // 根据列数动态调整标签显示策略
  const labelInterval = props.columns.length > 50 ? Math.floor(props.columns.length / 20) : 0;
  
  return {
    grid: {
      left: 100,    // 使用固定值而不是百分比
      right: 50,
      bottom: 100,
      top: 50,
      containLabel: false  // 不自动包含标签
    },
    tooltip: {
      position: 'top',
      formatter: function (params) {
        const xIndex = params.data[0];
        const yIndex = params.data[1];
        const value = params.data[2];
        return `${props.columns[selectedIndices[xIndex]]}<br/>${props.columns[selectedIndices[yIndex]]}<br/>相关度: ${value.toFixed(2)}`;
      }
    },
    xAxis: {
      type: 'category',
      data: axisLabels,
      splitArea: {
        show: true
      },
      axisLabel: {
        interval: labelInterval,  // 动态调整标签间隔
        rotate: 45,
        margin: 8,
        fontSize: props.columns.length > 30 ? 10 : 12  // 根据列数调整字体大小
      },
      position: 'bottom'
    },
    yAxis: {
      type: 'category',
      data: axisLabels,
      splitArea: {
        show: true
      },
      axisLabel: {
        interval: labelInterval,  // 动态调整标签间隔
        fontSize: props.columns.length > 30 ? 10 : 12  // 根据列数调整字体大小
      },
      position: 'left'
    },
    visualMap: {
      min: 0,
      max: 1,
      calculable: true,
      orient: 'horizontal',
      left: 'center',
      bottom: 10,
      itemWidth: 20,
      itemHeight: 140,
      inRange: {
        color: ['#e0f3f8', '#abd9e9', '#74add1', '#4575b4', '#313695']
      }
    },
    series: [{
      name: '相关度',
      type: 'heatmap',
      data: data,
      label: {
        show: false
      },
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }]
  };
};

// 调整图表容器大小以保持1:1比例
const resizeChartContainer = () => {
  if (!containerRef.value || !chartRef.value) return;
  
  const containerWidth = containerRef.value.clientWidth;
  const containerHeight = containerRef.value.clientHeight;
  
  // 取较小的值作为正方形的边长
  const size = Math.min(containerWidth, containerHeight);
  
  // 设置图表容器为正方形
  chartRef.value.style.width = `${size}px`;
  chartRef.value.style.height = `${size}px`;
  
  // 如果图表实例存在，调整其大小
  if (chartInstance) {
    chartInstance.resize();
  }
};

// 初始化图表
const initChart = async () => {
  await nextTick();
  
  if (chartRef.value) {
    // 先调整容器大小
    resizeChartContainer();
    
    // 初始化图表
    chartInstance = echarts.init(chartRef.value);
    updateChart();
  }
};

// 更新图表
const updateChart = () => {
  if (chartInstance) {
    const option = createHeatmapOption();
    chartInstance.setOption(option);
  }
};

// 监听数据变化，更新图表
watch(() => props.chartData, updateChart, { deep: true });

// 窗口大小变化时，重新调整图表大小
const handleResize = () => {
  resizeChartContainer();
};

onMounted(() => {
  initChart();
  window.addEventListener('resize', handleResize);
});

onBeforeUnmount(() => {
  if (chartInstance) {
    chartInstance.dispose();
    chartInstance = null;
  }
  window.removeEventListener('resize', handleResize);
});
</script>

<style scoped>
.chart-container {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.chart-content {
  /* 初始大小，会被 JS 动态调整 */
  width: 100%;
  height: 100%;
}
</style>