<template>
  <VirtualSampleGeneration
    :disabled="props.disabled"
    :columns="props.columns"
    :table-data="props.tableData"
  />
</template>

<script lang="ts" setup>
import type { PropType } from "vue";
import type { TableColumn } from "@/components/dataProcessing/src/dataTable";
import { VirtualSampleGeneration } from "@/components/dataProcessing/src/dataGeneration";

// Props 定义
const props = defineProps({
  disabled: { type: Boolean, default: false },
  columns: { type: Array as PropType<TableColumn[]>, default: () => [] },
  tableData: { type: Array as PropType<any[][]>, default: () => [] },
});
</script>


