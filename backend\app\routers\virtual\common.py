from flask import current_app, send_file, make_response, jsonify, request, Blueprint
from app.extensions.socketio import socketio
from app.utils.params import camel_to_snake, snake_to_camel
import logging
import re
import numpy as np
import pandas as pd
from typing import TypedDict, List, Dict, Any, Literal
from itertools import permutations, product
from latex2sympy2 import latex2sympy

logger = logging.getLogger("socketio")

virtual_common_bp = Blueprint("virtual_common", __name__)

class GenerationRequest(TypedDict):
    """
    constraints: list[str] # 约束条件
    data: {
        values: list[list[float]] # 数据
        columns: list[str] # 列名
        ranges: Dict[str, Dict[str, Any]] | None = {
            "x1": {
                "min": float
                "max": float
                "count": int
            },
            "x2": {
                "min": float
                "max": float
                "count": int
            },
            ...
        }
    }
    generation: {
        method: Literal["grid", "random"] # 生成方法
        params: dict # 生成参数
    }
    """
    constraints: List[str]
    data: Dict[str, Any]
    generation: Dict[str, Any]

def grid_generation_with_ranges(ranges: Dict[str, Dict[str, Any]], mean_vals: pd.Series) -> np.ndarray:
    """
    根据范围生成网格
    """
    min_vals = []
    max_vals = []
    keys = []
    for key, value in ranges.items():
        min_vals.append(value.get("min"))
        max_vals.append(value.get("max"))
        keys.append(key)
    min_vals = pd.Series(min_vals, index=keys)
    max_vals = pd.Series(max_vals, index=keys)
    samples_ = grid_generation(min_vals, max_vals, value.get("count"))
    samples = pd.DataFrame(np.zeros((samples_.shape[0], mean_vals.shape[0])), columns=mean_vals.index)
    for col in mean_vals.index:
        if col in keys:
            samples[col] = samples_[:, keys.index(col)]
        else:
            samples[col] = mean_vals[col]
    return samples.values

def grid_generation(min_vals: pd.Series, max_vals: pd.Series, count: int) -> np.ndarray:
    """
    根据范围生成网格
    """
    grids = np.linspace(min_vals, max_vals, count)
    grids = grids.T
    samples = product(*grids)
    samples = np.array(list(samples))
    return samples

@virtual_common_bp.route("/generate", methods=["POST"])
def generate():
    """
    return: {
        code: int
        msg: str
        data: {
            data: {
                values: List[List[float]]
                columns: List[str]
                shape: Tuple[int, int]
            }
            generation: {
                method: Literal["grid", "random"]
                params: Dict[str, Any]
            }
        }
    }
    """
    post_data: GenerationRequest = camel_to_snake(request.json) # type: ignore
    try:
        table_data = pd.DataFrame(post_data.get("data").get("values"), columns=post_data.get("data").get("columns"))
        table_data = table_data.apply(pd.to_numeric, errors="coerce")
        min_vals = table_data.min(axis=0)
        max_vals = table_data.max(axis=0)
        mean_vals = table_data.mean(axis=0)
        if post_data.get("generation").get("method") == "GridGeneration":
            ranges = post_data.get("data").get("ranges")
            if ranges is not None:
                samples = grid_generation_with_ranges(ranges, mean_vals)
            else:
                grid_count = post_data.get("generation").get("params").get("grid_count")
                samples = grid_generation(min_vals, max_vals, grid_count)
        elif post_data.get("generation").get("method") == "RandomGeneration":
            sample_count = post_data.get("generation").get("params").get("sample_count")
            random_seed = post_data.get("generation").get("params").get("random_seed")
            np.random.seed(random_seed)
            samples = np.random.random((sample_count, max_vals.shape[0])) * (max_vals - min_vals).values + min_vals.values
        else:
            raise ValueError(f"Invalid generation method: {post_data.get('generation').get('method')}")
        samples = samples.round(2)

        # 处理约束条件
        constraints = post_data.get("constraints")
        if constraints is not None:
            contents = pd.DataFrame(samples, columns=table_data.columns)
            constraints_info = []
            for constraint in constraints:
                constraint_info = {
                    "latex": constraint,
                    "python": "",
                    "result": None,
                    "error": False
                }
                # 处理LaTeX的text符号
                constraint = re.sub(r'\\text\s*\{([^}]*)\}', r'`\1`', constraint) # '`nNi/\\%`' -> '`nNi/%`'

                # 处理 \frac{}{}, \dfrac{}{}, \tfrac{}{} 等分数符号
                # 使用原始字符串(r前缀)来避免转义问题，并处理可能的空格
                constraint = re.sub(r'\\frac\s*\{([^}]*)\}\s*\{([^}]*)\}', r'(\1)/(\2)', constraint)
                constraint = re.sub(r'\\dfrac\s*\{([^}]*)\}\s*\{([^}]*)\}', r'(\1)/(\2)', constraint)
                constraint = re.sub(r'\\tfrac\s*\{([^}]*)\}\s*\{([^}]*)\}', r'(\1)/(\2)', constraint)

                # 处理^{}符号
                constraint = re.sub(r'\^\{([^}]*)\}', r'**\1', constraint) # '`nNi/\\%`  ^{2} =2' -> '`nNi/\\%`  **2 =2'
                
                # 处理LaTeX数学符号
                constraint = constraint.replace("\\gt", ">").replace("\\ge", ">=")
                constraint = constraint.replace("\\lt", "<").replace("\\le", "<=")
                constraint = constraint.replace("\\eq", "==")
                constraint = constraint.replace("\\neq", "!=")
                
                # 处理更多LaTeX数学符号
                constraint = constraint.replace("\\times", "*").replace("\\cdot", "*")
                constraint = constraint.replace("\\div", "/").replace("\\pm", "+-")
                constraint = constraint.replace("\\mp", "-+")

                # 清理其他LaTeX符号和转义字符
                for s in ["%", "$", "#", "&", "^", "~", "|", "`", "!", "@", "\\"]:
                    constraint = constraint.replace(f"\\{s}", s)
                
                # 清理多余的空格和括号
                constraint = constraint.strip()
                
                try:
                    # 确保等号被正确替换为Python的==
                    constraint_expr = re.sub(r'(?<!<)(?<!>)(?<!!)(?<!=)=', '==', constraint)
                    constraint_info["python"] = constraint_expr
                    before_count = contents.shape[0]
                    contents = contents.query(constraint_expr)
                    if contents.empty:
                        logger.warning(f"约束条件 '{constraint}' 筛选后没有数据")
                        constraint_info["result"] = f"约束条件 '{constraint}' 筛选后没有数据"
                        continue
                    constraint_info["result"] = f"筛选前样本数: {before_count}, 筛选后样本数: {contents.shape[0]}"
                except Exception as e:
                    logger.error(f"应用约束条件 '{constraint}' 时出错: {e}")
                    constraint_info["result"] = f"应用约束条件 '{constraint}' 时出错: {e}"
                    constraint_info["error"] = True
                    continue
                finally:
                    constraints_info.append(constraint_info)
            
            if not contents.empty:
                samples = contents.values
            else:
                samples = np.array([]).reshape(0, len(table_data.columns))
                   
        return_data = {
            "data": {
                "values": samples.tolist(),
                "columns": table_data.columns.tolist(),
                "shape": samples.shape
            },
            "generation": {
                "method": post_data.get("generation").get("method"),
                "params": post_data.get("generation").get("params")
            },
            "constraints": constraints_info
        }
        return {"code": 200, "msg": "success", "data": return_data}
    except Exception as e:
        logger.error(f"generate: 虚拟信息获取失败: {e}")
        return {"code": 500, "msg": f"虚拟信息获取失败: {e}", "data": None}