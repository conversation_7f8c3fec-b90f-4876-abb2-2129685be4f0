import request from "@/utils/request";
import { AxiosHeaders } from "axios";
import type {
  SingleOptimizationRequest,
  MultipleOptimizationRequest,
  OptimizationResult,
  GetSearchListResponse,
  GetSearchInfoResponse,
} from "@/types/optimize";

/**
 * 发送优化请求
 */
export const runOptimizationSingle = (
  data: SingleOptimizationRequest,
): Promise<OptimizationResult> => {
  return request.post("/search/single/run_optimization", data);
};

export const runOptimizationMultiple = (
  data: MultipleOptimizationRequest,
): Promise<OptimizationResult> => {
  return request.post("/search/multiple/run_optimization", data);
};

export const getSearchList = (params: {
  path: string;
}): Promise<GetSearchListResponse> => {
  return request.get("/search/get_search_list", {
    headers: new AxiosHeaders({}),
    skipLoading: true,
    params,
  });
};

export const deleteSearches = (uids: string[]): Promise<void> => {
  return request.post(
    "/search/delete_searches",
    { uids },
    {
      headers: new AxiosHeaders({
        "Content-Type": "application/json",
      }),
      skipLoading: true,
    },
  );
};

export const getSearchInfo = (
  taskUid: string,
): Promise<GetSearchInfoResponse> => {
  return request.post(
    "/search/get_search_info",
    { uid: taskUid },
    {
      headers: new AxiosHeaders({
        "Content-Type": "application/json",
      }),
      skipLoading: true,
    },
  );
};
