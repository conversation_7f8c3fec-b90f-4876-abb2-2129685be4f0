<template>
  <div class="toolbox-panel">
    <div class="panel-header">
      <h4>工具箱</h4>
    </div>
    
    <div class="toolbox-content">
      <!-- 可用特征 -->
      <div class="toolbox-section">
        <div class="section-header">
          <h5>可用特征</h5>
          <div class="header-controls">
            <el-input
              v-model="featureSearchText"
              placeholder="搜索特征"
              size="small"
              class="feature-search-input"
              clearable
              @input="filterFeatures"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
            <span class="section-count">{{ filteredFeatures.length }}/{{ columns.length }}</span>
          </div>
        </div>
        <div class="section-content">
          <div class="tags-container">
            <el-tag
              v-for="col in filteredFeatures"
              :key="`feature-${col.data}`"
              class="clickable-tag feature-tag"
              @click="handleFeatureClick(col)"
            >
              {{ col.title }}
            </el-tag>
          </div>
          <!-- 无搜索结果提示 -->
          <div v-if="filteredFeatures.length === 0 && featureSearchText" class="no-results">
            <el-empty 
              description="未找到匹配的特征" 
              :image-size="60"
              class="compact-empty"
            />
          </div>
        </div>
      </div>
      
      <!-- 运算符 -->
      <div class="toolbox-section">
        <div class="section-header">
          <h5>运算符</h5>
          <span class="section-count">{{ operators.length }}</span>
        </div>
        <div class="section-content">
          <div class="tags-container">
            <el-tag
              v-for="op in operators"
              :key="`op-${op}`"
              class="clickable-tag operator-tag"
              @click="handleOperatorClick(op)"
            >
              <!-- 对于 LaTeX 格式的运算符，使用 MathJax 渲染 -->
              <MathJaxRenderer 
                v-if="op.startsWith('\\') || op.includes('^{') || op.includes('\\frac')"
                :formula="op" 
                :display-mode="false"
              />
              <!-- 对于普通运算符，直接显示 -->
              <span v-else>{{ op }}</span>
            </el-tag>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed } from "vue";
import type { PropType } from "vue";
import { Search } from "@element-plus/icons-vue";
import type { TableColumn } from "@/components/dataProcessing/src/dataTable";
import type { ClickableItem } from "./types";
import { MathJax as MathJaxRenderer } from "@/components/mathJax";

// Props 定义
const props = defineProps({
  columns: { 
    type: Array as PropType<TableColumn[]>, 
    default: () => [] 
  }
});

// Emits 定义
const emit = defineEmits<{
  'item-click': [item: ClickableItem]
}>();

// 响应式数据
const featureSearchText = ref("");

// 运算符列表
const operators = ref([
  "+", "-", "*", "/", "(", ")", "=", "\\neq", 
  "\\gt", "\\lt", "\\ge", "\\le",
  "\\frac{x}{y}", "x^{y}"
]);

// 计算属性：过滤后的特征列表
const filteredFeatures = computed(() => {
  if (!featureSearchText.value.trim()) {
    return props.columns;
  }
  
  const searchText = featureSearchText.value.toLowerCase();
  return props.columns.filter(col => 
    col.title?.toLowerCase().includes(searchText) || 
    col.data.toLowerCase().includes(searchText)
  );
});

// 方法
const filterFeatures = () => {
  // 实时过滤，不需要额外逻辑，computed 会自动处理
  console.log(`搜索特征: "${featureSearchText.value}", 找到 ${filteredFeatures.value.length} 个结果`);
};

// 处理特征点击
const handleFeatureClick = (column: TableColumn) => {
  emit('item-click', {
    type: 'feature',
    value: column
  });
};

// 处理运算符点击
const handleOperatorClick = (operator: string) => {
  emit('item-click', {
    type: 'operator',
    value: getOperatorValue(operator)
  });
};

// 获取运算符实际值的函数
const getOperatorValue = (op: string) => {
  // 对于分式，返回模板格式
  if (op.includes('\\frac')) {
    return '\\frac{}{}';
  }
  // 对于次方，返回模板格式
  if (op.includes('^{')) {
    return '^{}';
  }
  // 对于其他运算符，直接返回
  return op;
};
</script>

<style scoped>
/* 左侧工具箱 */
.toolbox-panel {
  width: 350px;
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  margin: 10px;
}

/* 面板头部 */
.panel-header {
  padding: 15px 20px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 1px solid var(--el-border-color-lighter);
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
}

.panel-header h4 {
  margin: 0;
  color: var(--el-text-color-primary);
  font-size: 16px;
  font-weight: 600;
}

.toolbox-content {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 0;
}

/* 工具箱内的滚动条样式 */
.toolbox-content::-webkit-scrollbar {
  width: 6px;
}

.toolbox-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.toolbox-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.toolbox-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 工具箱区域样式 */
.toolbox-section {
  border-bottom: 1px solid var(--el-border-color-lighter);
  background: #fafbfc;
}

.toolbox-section:last-child {
  border-bottom: none;
}

.toolbox-section:hover {
  background: #f5f7fa;
}

/* 区域头部 */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px 10px 20px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.section-header h5 {
  margin: 0;
  color: var(--el-text-color-primary);
  font-size: 14px;
  font-weight: 600;
}

.header-controls {
  display: flex;
  align-items: center;
  gap: 10px;
}

.feature-search-input {
  width: 150px;
}

.section-count {
  background: var(--el-color-primary);
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  min-width: 20px;
  text-align: center;
}

/* 区域内容 */
.section-content {
  padding: 15px 20px 20px 20px;
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

/* 标签样式 */
.clickable-tag {
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
  font-weight: 500;
}

.feature-tag {
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
  color: #1976d2;
  border: 1px solid #90caf9;
}

.feature-tag:hover {
  background: linear-gradient(135deg, #bbdefb 0%, #90caf9 100%);
  box-shadow: 0 4px 12px rgba(25, 118, 210, 0.3);
}

.operator-tag {
  background: linear-gradient(135deg, #f3e5f5 0%, #e1bee7 100%);
  color: #7b1fa2;
  border: 1px solid #ce93d8;
}

.operator-tag:hover {
  background: linear-gradient(135deg, #e1bee7 0%, #ce93d8 100%);
  box-shadow: 0 4px 12px rgba(123, 31, 162, 0.3);
}

/* 无结果提示 */
.no-results {
  padding: 20px 0;
}

.compact-empty {
  margin: 0;
}
</style>
