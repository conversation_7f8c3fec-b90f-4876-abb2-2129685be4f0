<template>
  <ReTable :data="tableData">
    <template #default="{ data: paginatedData }">
      <el-table
        :data="paginatedData"
        :max-height="350"
        style="width: 100%"
        size="default"
        :border="false"
        row-key="name"
        class="variable-selection-table"
      >
        <el-table-column label="全选" width="80" align="center">
          <template #header>
            <el-checkbox
              v-model="isAllSelected"
              :indeterminate="isIndeterminate"
              :disabled="disabled"
              @change="handleSelectAll"
            />
          </template>
          <template #default="scope">
            <el-checkbox
              v-model="scope.row.isSelected"
              :disabled="disabled"
              @change="handleRowSelection(scope.row)"
            />
          </template>
        </el-table-column>

        <!-- 变量名称列 -->
        <el-table-column prop="name" label="变量名称" min-width="150" />

        <!-- 序号变量列 -->
        <el-table-column label="序号变量" min-width="90" align="center">
          <template #default="scope">
            <el-checkbox
              v-model="scope.row.isIndex"
              :disabled="!scope.row.isSelected || disabled"
              @change="handleTypeSelect(scope.row, 'isIndex')"
            />
          </template>
        </el-table-column>

        <!-- 目标变量列 -->
        <el-table-column label="目标变量" min-width="90" align="center">
          <template #default="scope">
            <el-checkbox
              v-model="scope.row.isTarget"
              :disabled="!scope.row.isSelected || disabled"
              @change="handleTypeSelect(scope.row, 'isTarget')"
            />
          </template>
        </el-table-column>

        <!-- 自变量列 -->
        <el-table-column label="自变量" min-width="90" align="center">
          <template #default="scope">
            <el-checkbox
              v-model="scope.row.isIndependent"
              :disabled="!scope.row.isSelected || disabled"
              @change="handleTypeSelect(scope.row, 'isIndependent')"
            />
          </template>
        </el-table-column>

        <!-- 注释变量列 -->
        <el-table-column label="注释变量" min-width="90" align="center">
          <template #default="scope">
            <el-checkbox
              v-model="scope.row.isNote"
              :disabled="!scope.row.isSelected || disabled"
              @change="handleTypeSelect(scope.row, 'isNote')"
            />
          </template>
        </el-table-column>
      </el-table>
    </template>
  </ReTable>
</template>

<script lang="ts" setup>
import { ref, watch, computed, nextTick } from "vue";
import ReTable from "@/components/ReTable/index.vue";

interface VariableRow {
  name: string;
  isSelected: boolean;
  isIndependent: boolean;
  isTarget: boolean;
  isNote: boolean;
  isIndex: boolean;
}

const props = defineProps<{
  headers: any[];
  modelType?: string;
  disabled?: boolean; // 新增：禁用模式
  initialSelection?: typeof modelConfig.dataset.meta.headers; // 新增：初始选择
}>();

const emit = defineEmits<{
  (
    e: "update:selection",
    value: {
      features: string[];
      target: string[];
      deletes: string[];
      index: string[];
      all: string[];
    }
  ): void;
}>();

const tableData = ref<VariableRow[]>([]);

const isAllSelected = computed(() =>
  tableData.value.length > 0 && tableData.value.every(row => row.isSelected)
);

const isIndeterminate = computed(() => {
  const selectedCount = tableData.value.filter(row => row.isSelected).length;
  return selectedCount > 0 && selectedCount < tableData.value.length;
});

const updateSelection = () => {
  // 防止在初始化期间触发
  if (isInitializing) return;
  
  nextTick(() => {
    const result = {
      features: tableData.value
        .filter(row => row.isSelected && row.isIndependent)
        .map(row => row.name),
      target: tableData.value
        .filter(row => row.isSelected && row.isTarget)
        .map(row => row.name),
      deletes: tableData.value
        .filter(row => row.isSelected && row.isNote)
        .map(row => row.name),
      index: tableData.value
        .filter(row => row.isSelected && row.isIndex)
        .map(row => row.name),
      all: tableData.value.map(row => row.name)
    };
    emit("update:selection", result);
  });
};

const initTableData = (headers: any[]) => {
  if (headers?.length) {
    tableData.value = headers.map((header, index) => {
      const name = typeof header === 'object' ? header.prop || header.title : header;
      const row: VariableRow = {
        name: name,
        isSelected: true,
        isIndependent: false,
        isTarget: false,
        isNote: false,
        isIndex: false
      };

      // 如果是编辑模式且有初始选择，使用初始选择
      if (props.disabled && props.initialSelection) {
        row.isSelected = props.initialSelection.all.includes(name);
        row.isIndex = props.initialSelection.index.includes(name);
        row.isTarget = props.initialSelection.target.includes(name);
        row.isIndependent = props.initialSelection.features.includes(name);
        row.isNote = props.initialSelection.deletes.includes(name);
      } else {
        // Default selection logic for create mode
        if (index === 0) {
          row.isIndex = true;
        } else if (index === 1) {
          // The second column is the target variable
          row.isTarget = true;
        } else {
          row.isIndependent = true;
        }
      }
      return row;
    });
    updateSelection();
  }
};

let isInitializing = false;

watch(
  () => ({ headers: props.headers, initialSelection: props.initialSelection }),
  ({ headers }) => {
    if (!headers || isInitializing) return;
    
    isInitializing = true;
    nextTick(() => {
      initTableData(headers);
      isInitializing = false;
    });
  },
  { immediate: true }  // 移除 deep: true
);

const handleSelectAll = (val: boolean) => {
  if (!props.disabled) {
    tableData.value.forEach(row => {
      row.isSelected = val;
    });
    updateSelection();
  }
};

const handleRowSelection = (row: VariableRow) => {
  if (!props.disabled) {
    if (!row.isSelected) {
      row.isIndependent = false;
      row.isTarget = false;
      row.isNote = false;
      row.isIndex = false;
    }
    updateSelection();
  }
};

const handleTypeSelect = (row: VariableRow, type: keyof VariableRow) => {
  if (!props.disabled) {
    if (row[type]) {
      const types: (keyof VariableRow)[] = [
        "isIndependent",
        "isTarget",
        "isNote",
        "isIndex"
      ];
      types.forEach(t => {
        if (t !== type) {
          (row[t as keyof VariableRow] as boolean) = false;
        }
      });
    }
    updateSelection();
  }
};

const reset = () => {
  initTableData(props.headers);
};

defineExpose({
  reset
});
</script>